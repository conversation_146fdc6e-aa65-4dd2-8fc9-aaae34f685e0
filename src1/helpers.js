import AsyncStorage from '@react-native-async-storage/async-storage';
import {API} from '@env';
import axios from 'axios';
const months = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];

export const getLoginToken = async () => {
  try {
    const tokenValue = await AsyncStorage.getItem('loginToken');
    const id = await AsyncStorage.getItem('userId');

    if (tokenValue && id) {
      const response = await axios.get(`${API}/api/coach/${id}`, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${tokenValue}`,
        },
      });

      return {data: {...response?.data, token: tokenValue, id: id}};
    } else {
      return {error: 'User not authorized'};
    }
  } catch (e) {
    console.log('ieving data 24:', e);
    return {tokenValue: null, id: null};
  }
};

export function numberToWords(num) {
  if (num === 0) return 'zero';

  const belowTwenty = [
    'zero',
    'one',
    'two',
    'three',
    'four',
    'five',
    'six',
    'seven',
    'eight',
    'nine',
    'ten',
    'eleven',
    'twelve',
    'thirteen',
    'fourteen',
    'fifteen',
    'sixteen',
    'seventeen',
    'eighteen',
    'nineteen',
  ];
  const tens = [
    '',
    '',
    'twenty',
    'thirty',
    'forty',
    'fifty',
    'sixty',
    'seventy',
    'eighty',
    'ninety',
  ];
  const thousands = ['thousand', 'million', 'billion'];

  function helper(n) {
    if (n < 20) return belowTwenty[n];
    if (n < 100)
      return (
        tens[Math.floor(n / 10)] +
        (n % 10 !== 0 ? ' ' + belowTwenty[n % 10] : '')
      );
    if (n < 1000)
      return (
        belowTwenty[Math.floor(n / 100)] +
        ' hundred' +
        (n % 100 !== 0 ? ' ' + helper(n % 100) : '')
      );

    for (let i = 0; i < thousands.length; i++) {
      let unit = 1000 ** (i + 1);
      if (n < unit * 1000) {
        return (
          helper(Math.floor(n / unit)) +
          ' ' +
          thousands[i] +
          (n % unit !== 0 ? ' ' + helper(n % unit) : '')
        );
      }
    }
  }

  // Split the number into whole and decimal parts
  const [wholePart, decimalPart] = num.toString().split('.');

  let words = helper(parseInt(wholePart));

  // Convert the decimal part to words if it exists and is not all zeros
  if (decimalPart && parseInt(decimalPart) !== 0) {
    const decimalWords = decimalPart
      .split('')
      .map(digit => belowTwenty[parseInt(digit)])
      .join(' ');
    words += ' point ' + decimalWords;
  }

  return words;
}

export function convertDateIntoIndianFormat(date) {
  const day = date.getDate();
  const month = months[date.getMonth()];
  const year = date.getFullYear();
  return `${day} ${month}, ${year}`;
}