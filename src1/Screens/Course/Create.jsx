import {
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  View,
  TouchableOpacity,
  Button,
  Image,
  useWindowDimensions,
  ActivityIndicator,
  SafeAreaView,
  Alert,
  Platform,
} from 'react-native';
import React, {useEffect, useState, useRef, useCallback} from 'react';
import BouncyCheckbox from 'react-native-bouncy-checkbox';
import {Picker} from '@react-native-picker/picker';
import axios from 'axios';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {RadioButton, configureFonts} from 'react-native-paper';
import {launchImageLibrary} from 'react-native-image-picker';
import Svg, {Path} from 'react-native-svg';
import DatePicker, {getFormatedDate} from 'react-native-modern-datepicker';
import Icon from 'react-native-vector-icons/FontAwesome';
import {API} from '@env';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {getLoginToken} from '../../helpers';
import {useAuth} from '../../components/Auth/AuthContext';
import {DataTable} from 'react-native-paper';
import {useRoute} from '@react-navigation/native';
import moment, { min } from 'moment-timezone';
import {useNavigation} from '@react-navigation/native';
import GoogleSignin from '../GoogleSignIn/GoogleSignin';
import SectionedMultiSelect from 'react-native-sectioned-multi-select';
import DropIcon from 'react-native-vector-icons/MaterialIcons';
import {actions, RichEditor, RichToolbar} from 'react-native-pell-rich-editor';

const Create = () => {
  const route = useRoute();
  const {key} = route.params;
  const {source} = route.params || {source: 'direct'};

  const currentDate = new Date();
  const [open, setOpen] = useState(false);
  const [openEndDate, setOpenEndDate] = useState(false);
  const [descriptionImage, setDescriptionImage] = useState([]);
  const [isEndDate, setIsEndDate] = useState('never');
  const [isStartTimePickerVisible, setStartTimePickerVisibility] =
    useState(false);
  const [isEndTimePickerVisible, setEndTimePickerVisibility] = useState(false);
  const [categories, setCategories] = useState([]);
  const [facilities, setFacilities] = useState([]);
  const [bookedSlots, setBookedSlots] = useState([]);
  const [showBookedInfo, setBookedInfo] = useState(false);
  const [courseId, setCourseId] = useState();
  const [initalDescription, setInitialDescription] = useState('');
  const [initalAmenities, setInitalAmenities] = useState('');
  const [initalwhatYouHaveToBring, setInitalwhatYouHaveToBring] = useState('');
  const [initalcancellationPolicy, setInitalcancellationPolicy] = useState('');
  const [isEdit, setIsEdit] = useState(false);
  const [loading, setLoading] = useState(false);
  const [courseList, setCourseList] = useState();
  const [googleToken, setGoogleToken] = useState(false);
  const [courseStartDate, setCourseStartDate] = useState();
  const [courseEndDate, setCourseEndDate] = useState();
  const navigation = useNavigation();
  const {user, updateUserDetails, setPageName} = useAuth();
  const ProficiencyOptions = [
    {name: 'beginner'},
    {name: 'intermediate'},
    {name: 'advance'},
  ];

  const [selectedItems, setSelectedItems] = useState([]);

  // Academy availability state
  const [academyAvailability, setAcademyAvailability] = useState(null);

  // Ref to track if edit mode has been initialized
  const editModeInitialized = useRef(false);

  // State to track if form is editable (for edit mode)
  const [isFormEditable, setIsFormEditable] = useState(true);

  // Refs for auto-scroll functionality
  const scrollViewRef = useRef(null);
  const fieldRefs = useRef({
    courseName: null,
    description: null,
    startDate: null,
    endDate: null,
    days: null,
    startTime: null,
    endTime: null,
    feesCourse: null,
    fees30: null,
    fees60: null,
    category: null,
    facility: null,
    maxGroupSize: null,
    proficiency: null,
  });

  // Debug useEffect to monitor checkedDays changes
 

  // Synchronize formik days with checkedDays when checkedDays changes (only for initial load)
  const [initialDaysSet, setInitialDaysSet] = useState(false);
  

  // Initial setup useEffect
  useEffect(() => {
    if (user?.data?.refreshToken) {
      setGoogleToken(true);
    }

    getCategories();
    getFacilities();
  }, [user]);

  // Separate useEffect for handling edit mode initialization
  useEffect(() => {
    const {source} = route.params || {source: 'direct'};

    if (
      source === 'course-listing' ||
      source === 'custom-calendar' ||
      source === 'dashboard'
    ) {
      const {listing} = route.params;
      if (listing && !editModeInitialized.current) {
        console.log('Initializing edit mode with listing:', listing);
        editModeInitialized.current = true;
        setIsEdit(true);
        setIsFormEditable(false); // Disable form initially in edit mode
        setLoading(true);
        setCourseList(listing);

        // Use a longer timeout to ensure formik is fully initialized and stable
        const timeoutId = setTimeout(() => {
          try {
            fillCourseDetails(listing);
            // Shorter timeout for loading state
            setTimeout(() => {
              setLoading(false);
            }, 500);
          } catch (error) {
            console.error('Error filling course details:', error);
            setLoading(false);
          }
        }, 200);

        return () => clearTimeout(timeoutId);
      }
    }
  }, [route.params]);

  const fillCourseDetails = useCallback((details) => {
    if (!details || !formik) return;

    console.log('Filling course details:', details);

    // Batch all state updates together using React's automatic batching
    setCourseId(details?._id);

    // Prepare the complete updated values object
    const updatedValues = {
      ...formik.values,
      courseName: details.courseName || '',
      description: details.description || '',
      category: details.category || '',
      sessionType: details.sessionType || 'individual',
      classType: details.classType || 'class',
      maxGroupSize: details.maxGroupSize || '1',
      amenitiesProvided: details.amenitiesProvided || '',
      whatYouHaveToBring: details.whatYouHaveToBring || '',
      cancellationPolicy: details.cancellationPolicy || '',
      camp: details.camp || false,
      campName: details.campName || '',
      customImage: details.customImage || false,
      // Handle nested objects properly
      dates: {
        ...formik.values.dates,
        startDate: details?.dates?.startDate ? details.dates.startDate.split('T')[0] : '',
        endDate: details?.dates?.endDate ? details.dates.endDate.split('T')[0] : '',
        startTime: details?.dates?.startTime || '',
        endTime: details?.dates?.endTime || '',
        days: details?.dates?.days || []
      },
      fees: {
        ...formik.values.fees,
        ...details.fees
      },
      facility: {
        ...formik.values.facility,
        ...details.facility
      },
      proficiency: details?.proficiency || [],
      images: details?.images ? details.images.map(image => ({url: image.url})) : []
    };

    // Set all formik values at once to avoid multiple re-renders
    formik.setValues(updatedValues);

    // Update non-formik states
    if (details?.proficiency && Array.isArray(details.proficiency)) {
      setSelectedItems(details.proficiency);
    }

    // Handle dates and format them for display
    if (details?.dates?.startDate) {
      const startDateMoment = details.dates.startDate.split('T')[0];
      const formattedDate = moment.tz(startDateMoment, 'Asia/Kolkata').format();
      setCourseStartDate(formattedDate);
    }

    if (details?.dates?.endDate) {
      setIsEndDate('on');
      const endDateMoment = details.dates.endDate.split('T')[0];
      const formattedDate = moment.tz(endDateMoment, 'Asia/Kolkata').format();
      setCourseEndDate(formattedDate);
    } else {
      setIsEndDate(details.classType === 'course' ? 'on' : 'never');
    }

    // Handle days
    if (details?.dates?.days && Array.isArray(details.dates.days)) {
      console.log('Days from database:', details.dates.days);
      formik.setFieldValue('dates.days', details.dates.days);
      console.log('Setting checkedDays to:', details.dates.days);
    }

    // Handle rich text content - these will trigger QuillEditorComponent updates
    setInitialDescription(details?.description || '');
    setInitalAmenities(details?.amenitiesProvided || '');
    setInitalwhatYouHaveToBring(details?.whatYouHaveToBring || '');
    setInitalcancellationPolicy(details?.cancellationPolicy || '');

    // Handle images
    if (details?.images && Array.isArray(details.images)) {
      const imageUrls = details.images.map(image => ({url: image.url}));
      setDescriptionImage(imageUrls);
    } else {
      setDescriptionImage([]);
    }

    console.log('Course details filled successfully');
  }, []);

  const getCategories = async () => {
    try {
      let response = await axios.get(`${API}/api/category`);
      setCategories(response?.data?.data);
    } catch (error) {
      console.log(error);
    }
  };

  const getFacilities = async () => {
    if (user) {
      setFacilities(user?.data?.linkedFacilities);
    }
  };

  const checkAvailableSlots = async () => {
    try {
      let endDateToUse = getEndDateBasedOnClassType(
        formik.values.dates.startDate,
        formik.values.classType,
        isEndDate,
        formik.values.dates.endDate
      );

      console.log('checkAvailableSlots endDateToUse:', endDateToUse);

      const startDateTime = `${formik.values.dates.startDate}T${formik.values.dates.startTime}:00`;
      const endDateTime = `${endDateToUse}T${formik.values.dates.endTime}:00`;

      let obj = {
        dates: {
          startDate: startDateTime,
          endDate: endDateTime,
          startTime: formik.values.dates.startTime,
          endTime: formik.values.dates.endTime,
          days: formik.values.dates.days,
          courseId: courseId ? courseId : null,
          id: user?.data?.id,
        },
      };

      // Reset booked info state before checking
      setBookedInfo(false);

      const response = await axios.post(
        `${API}/api/course/availableSlots/${user?.data?.id}`,
        obj,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${user?.data?.token}`,
          },
        },
      );

      setBookedSlots(response?.data?.conflictingDates);

      if (response?.data?.message == 'Slots available') {
        setBookedInfo(false);
        return true;
      } else {
        setBookedInfo(true);
        return false;
      }
    } catch (error) {
      console.log(error);
      // Reset booked info on error
      setBookedInfo(false);
      return false;
    }
  };

  const handleConfirm = async (time, type) => {
    if (type === 'start') {
      if (isTimeValid(time) && formik.values.dates.startDate !== '') {
        const finalTime = formatTime(time);
        
        // Academy start time validation
        if (user?.data?.affiliationType === "academy" && academyAvailability?.startTime) {
          const selectedTime = new Date(`1970-01-01T${finalTime}:00Z`);
          const academyStartTime = new Date(`1970-01-01T${academyAvailability.startTime}:00Z`);
          
          if (selectedTime < academyStartTime) {
            Alert.alert(
              'Coach Availability',
              `Your available time starts from ${academyAvailability.startTime}`,
            );
            setStartTimePickerVisibility(false);
            return;
          }
        }
        
        // Academy end time validation for start time
        if (user?.data?.affiliationType === "academy" && academyAvailability?.endTime) {
          const selectedTime = new Date(`1970-01-01T${finalTime}:00Z`);
          const academyEndTime = new Date(`1970-01-01T${academyAvailability.endTime}:00Z`);
          
          if (selectedTime >= academyEndTime) {
            Alert.alert(
              'Coach Availability',
              `Your available time ends at ${academyAvailability.endTime}`,
            );
            setStartTimePickerVisibility(false);
            return;
          }
        }
        
        formik.setFieldValue('dates.startTime', '');
        formik.setFieldValue('dates.endTime', '');
        formik.setFieldValue('dates.startTime', finalTime);
        setStartTimePickerVisibility(false);
      } else {
        // Handle invalid time selection
        Alert.alert(
          'Invalid Time Selection',
          'Please select the start time after the current time.',
        );
        setStartTimePickerVisibility(false);
      }
    }
    if (type === 'end') {
      const endTime = formatTime(time);
      const endDate = formik.values.dates.endDate
        ? formik.values.dates.endDate
        : getLastDateOfStartYear(formik.values.dates.startDate);

      const startTimeStr = moment
        .tz(
          `${formik.values.dates.startDate}T${formik.values.dates.startTime}`,
          'Asia/Kolkata',
        )
        .format()
        .split('+')[0];
      const endTimeStr = moment
        .tz(`${endDate}T${endTime}`, 'Asia/Kolkata')
        .format()
        .split('+')[0];

      const startPart = startTimeStr.split('T')[1];
      const endPart = endTimeStr.split('T')[1];

      const startTimeDate = new Date(`1970-01-01T${startPart}Z`);
      const endTimeDate = new Date(`1970-01-01T${endPart}Z`);
      
      // Academy end time validation
      if (user?.data?.affiliationType === "academy" && academyAvailability?.endTime) {
        const selectedEndTime = new Date(`1970-01-01T${endTime}:00Z`);
        const academyEndTime = new Date(`1970-01-01T${academyAvailability.endTime}:00Z`);
        
        if (selectedEndTime > academyEndTime) {
          Alert.alert(
            'Coach Availability',
            `Your available time ends at ${academyAvailability.endTime}`,
          );
          setEndTimePickerVisibility(false);
          return;
        }
      }
      
      if (startTimeDate >= endTimeDate) {
        Alert.alert(
          'Please select the end time after the start time. Atleast with 10 minutes gap',
        );
        setEndTimePickerVisibility(false);
        return;
      } else {
        const finalTime = formatTime(time);
        formik.setFieldValue('dates.endTime', finalTime);
        setEndTimePickerVisibility(false);
      }
    }
  };

  const formatTime = time => {
    // Convert the given date string to a Date object
    const givenTime = new Date(time);

    const givenHours = givenTime.getHours();
    const givenMinutes = givenTime.getMinutes();

    const finalTime = `${givenHours}:${givenMinutes}`;
    const formattedTime = `${givenHours
      .toString()
      .padStart(2, '0')}:${givenMinutes.toString().padStart(2, '0')}`;

    return formattedTime;
  };

  const isTimeValid = dateTimeString => {
    // Get the current time
    const currentTime = new Date();

    let currentDate = currentTime.toISOString().split('T')[0];

    const currentHours = currentTime.getHours();
    const currentMinutes = currentTime.getMinutes();

    // Convert the given date string to a Date object
    const givenTime = new Date(dateTimeString);

    const givenHours = givenTime.getHours();
    const givenMinutes = givenTime.getMinutes();

    const finalTime = `${givenHours}:${givenMinutes}`;

    if (formik.values.dates.startDate === currentDate) {
      if (
        givenHours > currentHours ||
        (givenHours === currentHours && givenMinutes >= currentMinutes)
      ) {
        return true; // Given time is greater than or equal to current time
      } else {
        return false; // Given time is less than current time
      }
    } else {
      return true;
    }
  };

  const showTimePicker = type => {
    // Check if in edit mode first
    if (isEdit == true) {
      return;
    }

    if (type == 'start') {
      setStartTimePickerVisibility(true);
      setEndTimePickerVisibility(false);
    }
    if (type == 'end') {
      setEndTimePickerVisibility(true);
      setStartTimePickerVisibility(false);
    }
  };

  const hideTimePicker = type => {
    if (type == 'start') {
      setStartTimePickerVisibility(false);
    }
    if (type == 'end') {
      setEndTimePickerVisibility(false);
    }
  };

  const toggleDay = day => {
    if (!isFormEditable) return;
    let updatedDays;
    if (formik.values.dates.days.includes(day)) {
      updatedDays = formik.values.dates.days.filter(item => item !== day);
    } else {
      updatedDays = [...formik.values.dates.days, day];
    }
    formik.setFieldValue('dates.days', updatedDays);
    // If you need checkedDays for other logic, you can keep setCheckedDays(updatedDays);
  };

  function daysDifference(startDate, endDate, daysArray) {
    const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const start = new Date(startDate);
    const end = new Date(endDate);

    console.log('daysDifference function called with:', {
      startDate,
      endDate,
      daysArray,
      startDateObj: start,
      endDateObj: end
    });

    let count = 0;
    let currentDate = new Date(start);

    while (currentDate <= end) {
      const dayName = daysOfWeek[currentDate.getDay()];
      if (daysArray.includes(dayName)) {
        console.log(`Found matching day: ${dayName} on ${currentDate.toISOString().split('T')[0]}`);
        count++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    console.log('daysDifference result:', count);
    return count;
  }

  const getValidationSchema = (classType, isEndDateState) => Yup.object().shape({
    courseName: Yup.string().required('Course Name is required'),
    campName: Yup.string(),

    description: Yup.string().required('Description is required'),

    dates: Yup.object().shape({
      startDate: Yup.string()
        .required('Start Date is required')
        .test('is-valid-date', 'Please select a valid start date', function(value) {
          if (!value) return false;
          const date = new Date(value);
          return !isNaN(date.getTime());
        }),
      // .min(new Date(), 'Date selected should not be before the current date'),

      endDate: Yup.string()
        .when([], {
          is: () => classType === 'course',
          then: (schema) => schema.required('End Date is required'),
          otherwise: (schema) => {
            // For class type, validate based on isEndDate state
            if (isEndDateState === 'on') {
              return schema.required('End Date is required');
            }
            return schema.nullable();
          }
        })
        .test('is-valid-end-date', 'Please select a valid end date', function(value) {
          // Only validate if value exists (required validation handles empty values)
          if (!value) return true;
          const date = new Date(value);
          return !isNaN(date.getTime());
        })
        .test('end-after-start', 'End date must be after start date', function(value) {
          const { startDate } = this.parent;
          if (!value || !startDate) return true;
          return new Date(value) >= new Date(startDate);
        }),

      startTime: Yup.string().required('Start time is required'),
      endTime: Yup.string(),
      days: Yup.array()
        .min(1, 'At least one day must be selected')
        .required('At least one day must be selected'),
    }),

    fees: Yup.object().shape({
      feesCourse: classType === 'course'
        ? Yup.number()
            .transform((value, originalValue) => {
              return originalValue === '' ? undefined : value;
            })
            .required('Course fee is required')
        : Yup.number().nullable(),
      fees30: classType === 'class'
        ? Yup.number()
            .transform((value, originalValue) => {
              return originalValue === '' ? undefined : value;
            })
            .nullable()
        : Yup.number().nullable(),
      fees60: classType === 'class'
        ? Yup.number()
            .transform((value, originalValue) => {
              return originalValue === '' ? undefined : value;
            })
            .nullable()
        : Yup.number().nullable(),
    }).when('classType', {
      is: 'class',
      then: (schema) => schema.test(
        'at-least-one-fee',
        'At least one fee (30 min or 60 min) is required',
        function(value) {
          const { fees30, fees60 } = value || {};
          return (fees30 !== null && fees30 !== undefined && fees30 !== '') ||
                 (fees60 !== null && fees60 !== undefined && fees60 !== '');
        }
      ),
      otherwise: (schema) => schema
    }),

    category: Yup.string().required('Category is required'),
    sessionType: Yup.string().required('Session-type is required'),
    facility: Yup.object().shape({
      name: Yup.string().required('Facility is required')
    }).required('Facility is required'),
    proficiency: Yup.array()
      .min(1, 'At least one proficiency must be selected')
      .required('Proficiency is required'),
    maxGroupSize: Yup.number()
      .transform((value, originalValue) => {
        return originalValue === '' ? undefined : value;
      })
      .required('Maximum group size is required'),
    amenitiesProvided: Yup.string(),
    whatYouHaveToBring: Yup.string(),
    cancellationPolicy: Yup.string(),
  });

  const formik = useFormik({
    initialValues: {
      courseName: '',
      camp: false,
      campName: '',
      description: '',
      images: [],
      classType: 'class',
      dates: {
        startDate: '',
        endDate: '',
        startTime: '',
        endTime: '',
        days: [],
      },

      fees: {
        feesCourse: null,
        fees30: null,
        fees45: null,
        fees60: null,
      },
      category: '',
      sessionType: 'individual',
      facility: {
        location: {type: 'Point', coordinates: [], is_location_exact: true},
        name: '',
        addressLine1: '',
        addressLine2: '',
        city: '',
        state: '',
        pinCode: '',
        country: '',
        amenities: '',
      },
      proficiency: [],
      maxGroupSize: '1',
      customImage: false,
      amenitiesProvided: '',
      whatYouHaveToBring: '',
      cancellationPolicy: '',
    },

    validate: (values) => {
      const schema = getValidationSchema(values.classType, isEndDate);
      try {
        schema.validateSync(values, { abortEarly: false });
        return {};
      } catch (error) {
        const errors = {};
        error.inner.forEach((err) => {
          if (err.path) {
            // Handle nested paths like 'fees.feesCourse'
            const pathParts = err.path.split('.');
            if (pathParts.length === 1) {
              errors[err.path] = err.message;
            } else {
              // Handle nested objects
              let current = errors;
              for (let i = 0; i < pathParts.length - 1; i++) {
                if (!current[pathParts[i]]) {
                  current[pathParts[i]] = {};
                }
                current = current[pathParts[i]];
              }
              current[pathParts[pathParts.length - 1]] = err.message;
            }
          }
        });
        return errors;
      }
    },

    onSubmit: async value => {
      // console.log("Inside on submit: ")
      try {
        setLoading(true);
        // Call checkAvailableSlots function and wait for its result

        // Set default end date when isEndDate is 'never'
        let defaultEndDate;
        if (
          isEndDate === 'never' &&
          user?.data?.affiliationType === "academy" &&
          academyAvailability?.endDate &&
          new Date(academyAvailability.endDate) >= new Date()
        ) {
          defaultEndDate = academyAvailability.endDate.split('T')[0];
        } else if (isEndDate === 'never') {
          defaultEndDate = getLastDateOfStartYear(formik.values.dates.startDate);
        }
        if (isEndDate === 'never') {
          await formik.setFieldValue('dates.endDate', defaultEndDate);
          // Small delay to ensure state is updated
          await new Promise(resolve => setTimeout(resolve, 100));
        }

        const slotsAvailable = await checkAvailableSlots();

        // Get the correct end date based on class type and isEndDate option
        const endDateForValidation = getEndDateBasedOnClassType(
          formik.values.dates.startDate,
          formik.values.classType,
          isEndDate,
          formik.values.dates.endDate
        );

        // console.log('Debug daysDifference params:', {
        //   startDate: formik.values.dates.startDate,
        //   endDate: endDateForValidation,
        //   days: formik.values?.dates?.days,
        //   classType: formik.values.classType,
        //   isEndDate: isEndDate
        // });

        const isDaysExist = await daysDifference(
          formik.values.dates.startDate,
          endDateForValidation,
          formik.values?.dates?.days,
        );

        if (isDaysExist === 0) {
          Alert.alert('Selected days are not in between the selected dates.');
          setLoading(false);
          return;
        }

        // Proceed further only if slots are available
        if (slotsAvailable) {
          let startDateTime;
          let endDateTime;

          if (
            formik.values.dates.startDate !== '' &&
            formik.values.dates.endDate === ''
          ) {
            let defaultEndDate = getLastDateOfCurrentYear();
            formik.setFieldValue('dates.endDate', defaultEndDate);

            if (defaultEndDate) {
              startDateTime = `${formik.values.dates.startDate}T${formik.values.dates.startTime}:00`;
              endDateTime = `${defaultEndDate}T${formik.values.dates.endTime}:00`;
            }
          } else if (
            formik.values.dates.startDate !== '' &&
            formik.values.dates.endDate !== ''
          ) {
            startDateTime = `${formik.values.dates.startDate}T${formik.values.dates.startTime}:00`;
            endDateTime = `${formik.values.dates.endDate}T${formik.values.dates.endTime}:00`;
          }

          value = {
            ...value,

            fees: {
              fees:
                formik.values.classType === 'course'
                  ? formik.values?.fees?.feesCourse
                  : formik.values?.fees?.fees30 ||
                    formik.values?.fees?.fees45 ||
                    formik.values?.fees?.fees60,
              feesCourse:
                formik.values.classType === 'class'
                  ? ''
                  : formik.values?.fees?.feesCourse,
              fees30:
                formik.values.classType === 'class'
                  ? formik.values?.fees?.fees30
                  : '',
              fees45:
                formik.values.classType === 'class'
                  ? formik.values?.fees?.fees45
                  : '',
              fees60:
                formik.values.classType === 'class'
                  ? formik.values?.fees?.fees60
                  : '',
            },

            dates: {
              startDate: startDateTime,
              endDate: endDateTime,
              startTime: formik.values.dates.startTime,
              endTime: formik.values.dates.endTime,
              days: formik.values.dates.days,
            },
          };

          let obj = {
            ...value,
            images:
              value.images && value.images.length > 0
                ? value.images
                : [
                    {
                      url: `${
                        categories.filter(x => x.name === value.category)[0]
                          ?.image
                      }`,
                    },
                  ],
            customImage: value.images && value.images.length > 0 ? true : false,
            coach_id: user?.data?._id,
            coachName: `${user?.data?.firstName} ${user?.data?.lastName}`,
            coachEmail: user?.data?.email,
          };

          if (courseId) {
            const result = await axios.patch(
              `${API}/api/course/${courseId}`,
              obj,
              {
                headers: {
                  'Content-Type': 'application/json',
                  Authorization: `Bearer ${user?.data?.token}`,
                },
              },
            );

            if (result?.data) {
              setTimeout(() => {
                setLoading(false);
                Alert.alert('Course updated successfully');
              }, 2000);
              formik.setFieldValue('dates.days', []);
              setTimeout(() => {
                setIsEdit(true);
                setPageName('Course List');
                navigation.navigate('CourseListing');
              }, 3000);
            } else {
              console.log('error');
            }
          } else {
            const result = await axios.post(`${API}/api/course/`, obj, {
              headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${user?.data?.token}`,
              },
            });

            if (result?.data) {
              await updateUserDetails();
              setTimeout(() => {
                setLoading(false);
                Alert.alert('Course is saved successfully');
                formik.setFieldValue('dates.days', []);
                handleReset();
                setPageName('Calendar');
                navigation.navigate('Calendar');
              }, 3000);
            }
          }
        } else {
          Alert.alert(
            'Slots Not Available',
            'Please choose different dates or times.',
          );
          setLoading(false);
        }
      } catch (error) {
        console.log('Error In Course Creation: ', error);
        if(error.response) {
          console.log("Axios Course Error Response:", error.response.data);
          console.log("Axios Course Error Status:", error.response.status);
          
          // Show backend error message in alert with header
          const errorMessage = error.response.data?.error || 'An error occurred';
          Alert.alert(
            'Course Creation Error',
            errorMessage,
            [{ text: 'OK' }]
          );
        } else if(error.request) {
          console.log("Axios Course Error Request:", error.request);
          Alert.alert('Network Error', 'Please check your internet connection and try again.');
        } else {
          console.log("Axios Course Error Message:", error.message);
          Alert.alert('Error', 'An unexpected error occurred. Please try again.');
        }
      }
      finally {
        setLoading(false);
      }
    },
  });

  function getLastDateOfCurrentYear() {
    var today = new Date(); // Get current date
    var currentYear = today.getFullYear(); // Get current year
    var lastDate = `${currentYear}-12-31`; // Set to December 31st of the current year
    return lastDate;
    //return localDateString;
  }

  function getLastDateOfStartYear(startDate) {
    if (!startDate) {
      // Fallback to current year if no start date provided
      return getLastDateOfCurrentYear();
    }

    // Extract year from start date (format: YYYY-MM-DD)
    const startYear = new Date(startDate).getFullYear();
    const lastDate = `${startYear}-12-31`; // Set to December 31st of the start year
    return lastDate;
  }

  function getEndDateBasedOnClassType(startDate, classType, isEndDateOption, formikEndDate) {
    // For class type with 'never' option: provide formik end date (which will be set to start year end)
    if (classType === 'class' && isEndDateOption === 'never') {
      return formikEndDate || getLastDateOfStartYear(startDate);
    }

    // For course type with 'on' option: use formik end date when available
    if (classType === 'course' && isEndDateOption === 'on') {
      return formikEndDate || getLastDateOfStartYear(startDate);
    }

    // For course type with 'never' option: use start year end date
    if (classType === 'course' && isEndDateOption === 'never') {
      return getLastDateOfStartYear(startDate);
    }

    // Default fallback
    return formikEndDate || getLastDateOfStartYear(startDate);
  }



  async function handleImagePicker() {
    if (isEdit == true) {
      return;
    }
    const options = {
      mediaType: 'photo',
      multiple: true, // Allow multiple selection
    };

    try {
      const result = await launchImageLibrary(options);

      // Check if the user cancelled the picker
      if (result.didCancel) {
      } else if (result.error) {
        console.error('ImagePicker Error:', result.error);
      } else {
        const formData = new FormData();
        formData.append('image', {
          uri: `${result?.assets[0]?.uri}`,
          type: 'image/jpg',
          name: `${result?.assets[0]?.fileName}`,
        });

        const response = await axios.post(
          `${API}/api/course/uploadImage`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          },
        );

        const url = response?.data?.url;

        await setDescriptionImage([...descriptionImage, {url: url}]);

        //Update formik values
        const updatedImages = [...formik.values.images, {url: url}];
        await formik.setFieldValue('images', updatedImages);

        // Set customImage to true when images are uploaded
        await formik.setFieldValue('customImage', true);
      }
    } catch (error) {
      formik.setFieldError('images', error.message);
      if (error?.response && error.response.status === 400) {
        const errorMessage = error.response.data.error;
        console.error('Error during image selection:', errorMessage);
      }
      console.error('Error during image selection:', error);
    }
  }
  const showDatePicker = () => {
    if (isEdit == true) {
      return;
    }
    setOpen(!open);
  };
  const showEndDatePicker = () => {
    if (isEdit == true) {
      return;
    }
    setOpenEndDate(!openEndDate);
  };

  async function handleStartDateChange(propDate) {
    let currentDateObject = new Date();
    let currentDate = currentDateObject.toISOString().split('T')[0];
    let date = replaceSlashWithDash(propDate);
    const formattedDate = moment.tz(date, 'Asia/Kolkata').format();
    try {
      if (date >= currentDate) {
        // Academy availability date validation
        if (user?.data?.affiliationType === "academy" && academyAvailability?.startDate && academyAvailability?.endDate) {
          const selectedDate = new Date(date);
          const academyStartDate = new Date(academyAvailability.startDate.split('T')[0]);
          const academyEndDate = new Date(academyAvailability.endDate.split('T')[0]);
          
          if (selectedDate < academyStartDate) {
            Alert.alert(
              'Coach Availability',
              `Your availability starts from ${academyStartDate.toISOString().split('T')[0]}`,
            );
            setOpen(false);
            return;
          }
          
          if (selectedDate > academyEndDate) {
            Alert.alert(
              'Coach Availability',
              `Your availability ends at ${academyEndDate.toISOString().split('T')[0]}`,
            );
            setOpen(false);
            return;
          }
        }
        
        await formik.setFieldValue('dates.startDate', date);
        formik.validateField('dates.startDate');
        setCourseStartDate(formattedDate);
        formik.setFieldValue('dates.endDate', '');
        setCourseEndDate('');
        formik.setFieldValue('dates.startTime', '');
        formik.setFieldValue('dates.endTime', '');

        setOpen(false);
      } else {
        Alert.alert('Start date cannot be in the past ');
        setOpen(false);
      }
    } catch (error) {
      // Handle validation errors
      console.log(error);
      formik.setFieldError('dates.startDate', error.message);
      setOpen(false);
    }
  }

  async function handleEndDateChange(propDate) {
    let date = replaceSlashWithDash(propDate);
    const formattedDate = moment.tz(date, 'Asia/Kolkata').format();
    try {
      // Academy availability date validation for end date
      if (user?.data?.affiliationType === "academy" && academyAvailability?.startDate && academyAvailability?.endDate) {
        const selectedDate = new Date(date);
        const academyStartDate = new Date(academyAvailability.startDate.split('T')[0]);
        const academyEndDate = new Date(academyAvailability.endDate.split('T')[0]);
        
        if (selectedDate < academyStartDate) {
          Alert.alert(
            'Coach Availability',
            `Your availability starts from ${academyStartDate.toISOString().split('T')[0]}`,
          );
          setOpenEndDate(false);
          return;
        }
        
        if (selectedDate > academyEndDate) {
          Alert.alert(
            'Coach Availability',
            `Your availability ends at ${academyEndDate.toISOString().split('T')[0]}`,
          );
          setOpenEndDate(false);
          return;
        }
      }
      
      if (
        formik.values.dates.startDate !== '' &&
        date >= formik.values.dates.startDate
      ) {
        await formik.setFieldValue('dates.endDate', date);
        formik.validateField('dates.endDate');
        setCourseEndDate(formattedDate);
        formik.setFieldValue('dates.startTime', '');
        formik.setFieldValue('dates.endTime', '');

        setOpenEndDate(false);
      } else {
        Alert.alert('End date cannot be before start Date');
        setOpenEndDate(false);
      }
    } catch (error) {
      // Handle validation errors
      console.log(error);
      formik.setFieldError('dates.endDate', error.message);
      setOpenEndDate(false);
    }
  }

  function replaceSlashWithDash(dateString) {
    return dateString.replace(/\//g, '-');
  }

  const handleReset = () => {
    formik.resetForm(); // Reset the form
    setDescriptionImage('');
    setCourseId('');
    formik.setFieldValue('dates.days', []);
    setInitialDescription('');
    setInitalAmenities('');
    setInitalwhatYouHaveToBring('');
    setInitalcancellationPolicy('');
    setSelectedItems([]);
    setPageName('Course List');
    setIsEndDate('never');

    navigation.navigate('CourseListing');
  };

  // Handle key parameter for resetting form
  useEffect(() => {
    if (key) {
      // Reset all states in a batch
      editModeInitialized.current = false; // Reset the ref
      formik.resetForm();
      setDescriptionImage([]);
      formik.setFieldValue('dates.days', []);
      setInitialDescription('');
      setInitalAmenities('');
      setInitalwhatYouHaveToBring('');
      setInitalcancellationPolicy('');
      setCourseStartDate('');
      setCourseEndDate('');
      setSelectedItems([]);
      setCourseId('');
      setIsEdit(false);
      setIsFormEditable(true); // Reset to editable for new forms
      setLoading(false);
      setIsEndDate('never');
    }
  }, [key]);

  const deleteImageFiles = async (index, url) => {
    try {
      const updatedImages = [...descriptionImage];
      updatedImages.splice(index, 1);

      if (formik.values.customImage) {
        const formData = new FormData();
        formData.append('url', url);

        const response = await axios.post(
          `${API}/api/course/uploadImage`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          },
        );
        const resp = response?.data;
        setDescriptionImage(updatedImages);
        formik.setFieldValue('images', updatedImages);

        // Set customImage to false if no images remain
        if (updatedImages.length === 0) {
          formik.setFieldValue('customImage', false);
        }
      } else {
        setDescriptionImage(updatedImages);
        formik.setFieldValue('images', updatedImages);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleFormEdit = () => {
    const courseDate = new Date(courseList?.dates?.startDate.split('T')[0]);
    courseDate.setHours(0, 0, 0, 0);
    const todayDate = new Date();
    todayDate.setHours(0, 0, 0, 0);

    if (
      todayDate.getTime() >= courseDate?.getTime() ||
      Number(courseList?.playerEnrolled) !== 0
    ) {
      Alert.alert(
        'Cannot edit the course, Course start date passed or player enrolled in the course',
      );
    } else {
      setLoading(true);
      setIsEdit(false); // Exit edit mode
      setIsFormEditable(true); // Enable form editing
      setLoading(false);
    }
  };

  const handleQuillInputs = async (text, type) => {
    const formattedText = `<p>${text}</p>`;

    if (type == 'description') {
      // console.log("formatted text", formattedText)
      await formik.setFieldValue('description', formattedText);
      setInitialDescription(text);
    } else if (type == 'amenitiesProvided') {
      await formik.setFieldValue('amenitiesProvided', formattedText);
      setInitalAmenities(text);
    } else if (type == 'whatYouHaveToBring') {
      formik.setFieldValue('whatYouHaveToBring', formattedText);
      setInitalwhatYouHaveToBring(text);
    } else if (type == 'cancellationPolicy') {
      formik.setFieldValue('cancellationPolicy', formattedText);
      setInitalcancellationPolicy(text);
    }
  };

  const onSelectedItemsChange = async (selectedItems) => {
    if (isFormEditable) {
      await formik.setFieldValue('proficiency', selectedItems);
      formik.validateField('proficiency');
      setSelectedItems(selectedItems);
    }
  };

  const EmptyComponent = () => <View></View>;

  // Helper function to flatten nested error objects and find the first error field
  const getFirstErrorField = (errors) => {
    // Define the order of fields to check (top to bottom in the form)
    const fieldOrder = [
      { path: 'courseName', ref: 'courseName' },
      { path: 'description', ref: 'description' },
      { path: 'dates.startDate', ref: 'startDate' },
      { path: 'dates.endDate', ref: 'endDate' },
      { path: 'dates.days', ref: 'days' },
      { path: 'dates.startTime', ref: 'startTime' },
      { path: 'dates.endTime', ref: 'endTime' },
      { path: 'fees.feesCourse', ref: 'feesCourse' },
      { path: 'fees.fees30', ref: 'fees30' },
      { path: 'fees.fees60', ref: 'fees60' },
      { path: 'category', ref: 'category' },
      { path: 'facility', ref: 'facility' },
      { path: 'maxGroupSize', ref: 'maxGroupSize' },
      { path: 'proficiency', ref: 'proficiency' },
    ];

    // Check each field in order
    for (const field of fieldOrder) {
      const pathParts = field.path.split('.');
      let currentError = errors;

      // Navigate through nested error object
      let hasError = true;
      for (const part of pathParts) {
        if (currentError && typeof currentError === 'object' && currentError[part]) {
          currentError = currentError[part];
        } else {
          hasError = false;
          break;
        }
      }

      if (hasError && typeof currentError === 'string') {
        return field.ref;
      }
    }

    return null;
  };

  // Function to scroll to the first error field
  const scrollToFirstError = () => {
    const firstErrorField = getFirstErrorField(formik.errors);
    console.log("First error field found:", firstErrorField);
    console.log("Current formik errors:", formik.errors);

    if (firstErrorField && fieldRefs.current[firstErrorField] && scrollViewRef.current) {
      console.log("Scrolling to field:", firstErrorField);

      // Measure the position of the error field
      fieldRefs.current[firstErrorField].measureLayout(
        scrollViewRef.current,
        (x, y) => {
          // Scroll to the error field with some offset to show the label and error message
          const scrollOffset = Math.max(0, y - 100); // 100px offset to show label above
          console.log(`Scrolling to position: ${scrollOffset} (field at y: ${y})`);

          scrollViewRef.current.scrollTo({
            y: scrollOffset,
            animated: true, // Smooth scrolling
          });
        },
        (error) => {
          console.warn('Error measuring field position:', error);
        }
      );
    } else {
      console.warn('Cannot scroll to error:', {
        firstErrorField,
        hasFieldRef: !!fieldRefs.current[firstErrorField],
        hasScrollRef: !!scrollViewRef.current
      });
    }
  };
  useEffect(()=>{
    // Check if user has affiliationType and it's "academy"
    if (user?.data?.affiliationType === "academy") {
      const academyAvailabilityData = user.data.academyAvailability;
      setAcademyAvailability(academyAvailabilityData);
      console.log("Academy Availability Details:");
      console.log("Academy Start Date:", academyAvailabilityData?.startDate);
      console.log("Academy End Date:", academyAvailabilityData?.endDate);
      console.log("Academy Start Time:", academyAvailabilityData?.startTime);
      console.log("Academy End Time:", academyAvailabilityData?.endTime);
      console.log("Academy Days:", academyAvailabilityData?.days);
    }
  },[])

  useEffect(() => {
    if (formik.values.dates.days.length > 0 && !initialDaysSet) {
      formik.setFieldValue('dates.days', formik.values.dates.days);
      console.log('Syncing formik days with checkedDays:', formik.values.dates.days);
      setInitialDaysSet(true);
    }
  }, [formik.values.dates.days, initialDaysSet]);

  // Place this above the return statement in the Create component
  const today = new Date().toISOString().split('T')[0];
  const academyStart = academyAvailability?.startDate?.split('T')[0];
  const academyEnd = academyAvailability?.endDate?.split('T')[0];

  const minStartDate =
    user?.data?.affiliationType === "academy" && academyStart && academyStart > today
      ? academyStart
      : today;

  const maxStartDate =
    user?.data?.affiliationType === "academy" && academyEnd && academyEnd > today
      ? academyEnd
      : undefined;

  const minEndDate = minStartDate;
  const maxEndDate = maxStartDate;

  return (
    <SafeAreaView>
      {!googleToken ? (
        <View
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignContent: 'center',
            alignSelf: 'center',
            margin: 'auto',
          }}>
          <Text>
            {' '}
            <GoogleSignin />{' '}
          </Text>
        </View>
      ) : (
        <View>
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" />
            </View>
          ) : (
            <View
              style={{
                backgroundColor: 'lightblue',
                padding: '2%',
                borderWidth: 0,
              }}>
              <View
                style={{
                  backgroundColor: 'white',
                  paddingVertical: '4%',
                  paddingHorizontal: '5%',
                  borderWidth: 0,
                  elevation: 5,
                  borderRadius: 5,
                }}>
                <ScrollView
                  ref={scrollViewRef}
                  showsVerticalScrollIndicator={false}
                  showsHorizontalScrollIndicator={false}
                  keyboardShouldPersistTaps="never"
                  keyboardDismissMode={Platform.OS === 'ios' ? 'interactive' : 'on-drag'}>
                  <View
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      marginVertical: '4%',
                      borderWidth: 0,
                    }}>
                    {isEdit == true ? (
                      <TouchableOpacity
                        style={{alignSelf: 'flex-end'}}
                        onPress={handleFormEdit}>
                        <View
                          style={{
                            backgroundColor: '#4F46E5',
                            alignContent: 'center',
                            paddingHorizontal: 10,
                            paddingVertical: 5,
                            borderRadius: 6,
                          }}>
                          {/* <Svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon" class="-ml-0.5 h-5 w-5"><Path stroke-linecap="round" stroke-linejoin="round" d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"></Path></Svg> */}
                          <Text style={{color: 'white', fontWeight: '600'}}>
                            Edit Form
                          </Text>
                        </View>
                      </TouchableOpacity>
                    ) : (
                      <></>
                    )}

                    <View
                      style={styles.boxContainer}
                      ref={(ref) => fieldRefs.current.courseName = ref}
                    >
                      <Text style={styles.label}>Course Name</Text>
                      <TextInput               placeholderTextColor="#000"

                        keyboardType="default"
                        style={[styles.input, {opacity: isFormEditable ? 1 : 0.6}]}
                        onBlur={formik.handleBlur('courseName')}
                        value={formik.values.courseName}
                        onChangeText={async (text) => {
                          await formik.setFieldValue('courseName', text);
                          formik.validateField('courseName');
                        }}
                        placeholder="Enter Course Name"
                        editable={isFormEditable}
                      />
                      {formik.touched.courseName &&
                        formik.errors.courseName && (
                          <Text style={styles.error}>
                            {formik.errors.courseName}
                          </Text>
                        )}
                    </View>

                    {/* description */}
                    <View
                      style={styles.boxContainer}
                      ref={(ref) => fieldRefs.current.description = ref}
                    >
                      <Text style={styles.label}>Description</Text>

                        <QuillEditorComponent
                          initialValue={initalDescription}
                          variable={'description'}
                          formik={formik}
                          editable={isFormEditable}
                        />

                      {formik.touched.description &&
                        formik.errors.description && (
                          <Text style={[styles.error]}>
                            {formik.errors.description}
                          </Text>
                        )}
                    </View>

                    {/* image upload */}
                    <View style={{marginTop: 20}}>
                    <View style={styles.boxContainer}>
                      <View
                        style={{
                          width: '99%',
                          borderStyle: 'dashed',
                          borderWidth: 1,
                          // marginVertical: "5%",
                          marginHorizontal: '1%',
                          borderRadius: 6,
                          display: 'flex',
                          alignItems: 'center',
                        }}>
                        <View
                          style={{
                            flex: 1,
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}>
                          <Svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 -5 24 28"
                            strokeWidth="1"
                            stroke="grey"
                            width={100}
                            height={100}
                            className="w-8 h-8">
                            <Path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"></Path>
                          </Svg>
                        </View>
                        <View
                          style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            marginBottom: 10,
                          }}>

                          <TouchableOpacity
                            onPress={() => {
                              if (isFormEditable) {
                                handleImagePicker();
                              }
                            }}
                            disabled={!isFormEditable}>
                            <Text style={{color: isFormEditable ? 'blue' : 'gray'}}> Upload a file </Text>
                          </TouchableOpacity>
                          <Text>PNG, JPG, GIF up to 10MB</Text>
                        </View>

                        {/* Conditionally render images */}
                        {descriptionImage?.length > 0 && (
                          <View
                            style={{
                              flexDirection: 'row',
                              flexWrap: 'wrap',
                              justifyContent: 'flex-start',
                              alignItems: 'center',
                              marginBottom: 10,
                            }}>
                            {descriptionImage?.map((image, index) => (
                              <View
                                key={index}
                                style={{
                                  position: 'relative',
                                  width: '20%',
                                  height: 80,
                                  margin: 5,
                                  borderRadius: 5,
                                  overflow: 'hidden',
                                }}>
                                <Image
                                  source={{uri: image.url}}
                                  style={{
                                    width: '100%',
                                    height: '100%',
                                    borderRadius: 5,
                                  }}
                                />
                                {isFormEditable && (
                                  <TouchableOpacity
                                    onPress={() =>
                                      deleteImageFiles(index, image.url)
                                    }
                                    style={{
                                      position: 'absolute',
                                      top: 0,
                                      right: 3,
                                      zIndex: 1,
                                    }}>
                                    <Icon
                                      name="close"
                                      size={15}
                                      color="red"
                                      style={{fontWeight: 'bold'}}
                                    />
                                  </TouchableOpacity>
                                )}
                              </View>
                            ))}
                          </View>
                        )}
                      </View>
                      {formik.touched.images && formik.errors.images && (
                        <Text style={styles.error}>{formik.errors.images}</Text>
                      )}
                    </View>
                    </View>

                    {/* class type */}
                    <View style={styles.boxContainer}>
                      <RadioButton.Group
                        onValueChange={newValue => {
                          if (!isFormEditable) {
                            return;
                          }
                          formik.setFieldValue('classType', newValue);

                          // Set sessionType based on classType
                          if (newValue === 'course') {
                            formik.setFieldValue('sessionType', 'group');
                          } else if (newValue === 'class') {
                            formik.setFieldValue('sessionType', 'individual');
                          }

                          // Trigger validation after classType change
                          setTimeout(() => {
                            formik.validateForm();
                          }, 0);
                        }}
                        value={formik.values.classType}>
                        <View
                          style={{
                            //marginLeft:'10%',
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                          }}>
                          <RadioButton.Item label="Session" value="class" />
                          <RadioButton.Item label="Course" value="course" />
                        </View>
                      </RadioButton.Group>
                    </View>
                    {/* dateeeee */}
                    {/* start dateeee */}

                    <View
                      style={styles.boxContainer}
                      ref={(ref) => fieldRefs.current.startDate = ref}
                    >
                      <Text style={styles.label}> Start Date </Text>
                      <TouchableOpacity
                        onPress={isFormEditable ? showDatePicker : null}
                        style={[styles.calendarIcon, {opacity: isFormEditable ? 1 : 0.6}]}
                        disabled={!isFormEditable}>
                        <View style={styles.inputContainer}>
                          <TextInput               placeholderTextColor="#000"

                            style={[styles.textInput, {color: 'black'}]}
                            placeholder="Tap to select the start date"
                            name="startDate"
                            value={
                              courseStartDate &&
                              `${new Date(courseStartDate).getDate()}/${
                                new Date(courseStartDate).getMonth() + 1
                              }/${new Date(courseStartDate).getFullYear()}`
                            }
                            editable={false}
                          />
                          <Icon name="calendar" size={20} color="black" />
                        </View>
                      </TouchableOpacity>
                      {open && (
                        <DatePicker
                          mode="calendar"
                          name="startDate"
                          minimumDate={
                            minStartDate
                          }
                          maximumDate={
                            maxStartDate
                          }
                          selected={
                            formik.values.dates?.startDate || minStartDate
                          }
                          // onMonthChange={(month, year) =>
                          //   console.log(month, year)
                          // }
                          onDateChange={handleStartDateChange}
                        />
                      )}
                      {formik.touched.dates?.startDate &&
                        formik.errors.dates?.startDate && (
                          <Text style={styles.error}>
                            {formik.errors.dates?.startDate}
                          </Text>
                        )}
                    </View>
                    {/* daysss */}

                    <View
                      style={[styles.boxContainer, {marginVertical: '3%'}]}
                      ref={(ref) => fieldRefs.current.days = ref}
                    >
                      <Text style={[styles.label, {marginVertical: 6}]}>
                        {' '}
                        Select Days{' '}
                      </Text>
                      {/* Debug: Show current checkedDays */}
                      {/* {__DEV__ && (
                        <Text style={{color: 'red', fontSize: 10}}>
                          Debug - checkedDays: {JSON.stringify(checkedDays)}
                        </Text>
                      )} */}
                      <View style={{marginLeft: '2%', opacity: isFormEditable ? 1 : 0.6}}>
                        <View style={styles.dayContainer}>
                          <BouncyCheckbox
                            size={25}
                            fillColor="purple"
                            onPress={() => isFormEditable && toggleDay('Mon')}
                            isChecked={formik.values.dates.days.includes('Mon')}
                            disabled={!isFormEditable || (user?.data?.affiliationType === "academy" && !academyAvailability?.days?.includes('Mon'))}
                          />
                           <Text style={{ 
                             color: (!isFormEditable || (user?.data?.affiliationType === "academy" && !academyAvailability?.days?.includes('Mon'))) ? 'grey' : 'black' 
                           }}>Monday</Text>
                        </View>
                        <View style={styles.dayContainer}>
                          <BouncyCheckbox
                            size={25}
                            fillColor="purple"
                            onPress={() => isFormEditable && toggleDay('Tue')}
                            isChecked={formik.values.dates.days.includes('Tue')}
                            disabled={!isFormEditable || (user?.data?.affiliationType === "academy" && !academyAvailability?.days?.includes('Tue'))}
                          />
                           <Text style={{ 
                             color: (!isFormEditable || (user?.data?.affiliationType === "academy" && !academyAvailability?.days?.includes('Tue'))) ? 'grey' : 'black' 
                           }}>Tuesday</Text>
                        </View>
                        <View style={styles.dayContainer}>
                          <BouncyCheckbox
                            size={25}
                            fillColor="purple"
                            onPress={() => isFormEditable && toggleDay('Wed')}
                            isChecked={formik.values.dates.days.includes('Wed')}
                            disabled={!isFormEditable || (user?.data?.affiliationType === "academy" && !academyAvailability?.days?.includes('Wed'))}
                          />
                           <Text style={{ 
                             color: (!isFormEditable || (user?.data?.affiliationType === "academy" && !academyAvailability?.days?.includes('Wed'))) ? 'grey' : 'black' 
                           }}>Wednesday</Text>
                        </View>
                        <View style={styles.dayContainer}>
                          <BouncyCheckbox
                            size={25}
                            fillColor="purple"
                            onPress={() => isFormEditable && toggleDay('Thu')}
                            isChecked={formik.values.dates.days.includes('Thu')}
                            disabled={!isFormEditable || (user?.data?.affiliationType === "academy" && !academyAvailability?.days?.includes('Thu'))}
                          />
                           <Text style={{ 
                             color: (!isFormEditable || (user?.data?.affiliationType === "academy" && !academyAvailability?.days?.includes('Thu'))) ? 'grey' : 'black' 
                           }}>Thursday</Text>
                        </View>
                        <View style={styles.dayContainer}>
                          <BouncyCheckbox
                            size={25}
                            fillColor="purple"
                            onPress={() => isFormEditable && toggleDay('Fri')}
                            isChecked={formik.values.dates.days.includes('Fri')}
                            disabled={!isFormEditable || (user?.data?.affiliationType === "academy" && !academyAvailability?.days?.includes('Fri'))}
                          />
                           <Text style={{ 
                             color: (!isFormEditable || (user?.data?.affiliationType === "academy" && !academyAvailability?.days?.includes('Fri'))) ? 'grey' : 'black' 
                           }}>Friday</Text>
                        </View>
                        <View style={styles.dayContainer}>
                          <BouncyCheckbox
                            size={25}
                            fillColor="purple"
                            onPress={() => isFormEditable && toggleDay('Sat')}
                            isChecked={formik.values.dates.days.includes('Sat')}
                            disabled={!isFormEditable || (user?.data?.affiliationType === "academy" && !academyAvailability?.days?.includes('Sat'))}
                          />
                           <Text style={{ 
                             color: (!isFormEditable || (user?.data?.affiliationType === "academy" && !academyAvailability?.days?.includes('Sat'))) ? 'grey' : 'black' 
                           }}>Saturday</Text>
                        </View>
                        <View style={styles.dayContainer}>
                          <BouncyCheckbox
                            size={25}
                            fillColor="purple"
                            onPress={() => isFormEditable && toggleDay('Sun')}
                            isChecked={formik.values.dates.days.includes('Sun')}
                            disabled={!isFormEditable || (user?.data?.affiliationType === "academy" && !academyAvailability?.days?.includes('Sun'))}
                          />
                           <Text style={{ 
                             color: (!isFormEditable || (user?.data?.affiliationType === "academy" && !academyAvailability?.days?.includes('Sun'))) ? 'grey' : 'black' 
                           }}>Sunday</Text>
                        </View>
                      </View>
                      {formik.touched.dates?.days &&
                        formik.errors.dates?.days && (
                          <Text style={styles.error}>
                            {formik.errors.dates?.days}
                          </Text>
                        )}
                    </View>
                    {/* days end */}

                    {/* end date */}

                    <View
                      style={styles.boxContainer}
                      ref={(ref) => fieldRefs.current.endDate = ref}
                    >
                      {formik.values.classType === 'class' && (
                        <>
                          <Text style={styles.label}> End Date </Text>

                          <RadioButton.Group
                            onValueChange={newValue => {
                              if (!isFormEditable) {
                                return;
                              }
                              setIsEndDate(newValue);
                              // Clear end date when switching to 'never'
                              if (newValue === 'never') {
                                formik.setFieldValue('dates.endDate', '');
                                setCourseEndDate('');
                              }
                              // Trigger validation after isEndDate change
                              setTimeout(() => {
                                formik.validateForm();
                              }, 0);
                            }}
                            value={isEndDate}
                            // style={{flexDirection:'row'}}
                          >
                            <View
                              style={{
                                borderWidth: 0,
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                marginTop: 8,
                              }}>
                              <View
                                style={{
                                  borderWidth: 1,
                                  flex: 1,
                                  borderColor: '#e5e7eb',
                                  borderTopLeftRadius: 5,
                                  borderBottomLeftRadius: 5,
                                }}>
                                <RadioButton.Item value="never" label="Never" />
                              </View>
                              <View
                                style={{
                                  borderWidth: 1,
                                  flex: 1,
                                  borderColor: '#e5e7eb',
                                  borderTopRightRadius: 5,
                                  borderBottomRightRadius: 5,
                                }}>
                                <RadioButton.Item label="On" value="on" />
                              </View>
                            </View>
                          </RadioButton.Group>
                        </>
                      )}

                      {/* Show end date picker UI for courses (always) OR for classes when isEndDate is 'on' */}
                      {(formik.values.classType === 'course' || (formik.values.classType === 'class' && isEndDate === 'on')) && (
                        <>
                        <View style={styles.boxContainer}>
                          <Text style={styles.label}>End Date </Text>
                          <TouchableOpacity
                            onPress={isFormEditable && formik.values.dates.startDate ? showEndDatePicker : null}
                            style={[styles.calendarIcon, {
                              opacity: isFormEditable && formik.values.dates.startDate ? 1 : 0.6
                            }]}
                            disabled={!isFormEditable || !formik.values.dates.startDate}>
                            <View style={styles.inputContainer}>
                              <TextInput               placeholderTextColor="#000"

                                style={[styles.textInput, {color: 'black'}]}
                                placeholder={formik.values.dates.startDate ? "Tap to select the end date" : "Please select start date first"}
                                name="endDate"
                                //value={selectedDate ? selectedDate : ''}
                                value={
                                  courseEndDate &&
                                  `${new Date(courseEndDate).getDate()}/${
                                    new Date(courseEndDate).getMonth() + 1
                                  }/${new Date(courseEndDate).getFullYear()}`
                                }
                                editable={false}
                              />
                              <Icon name="calendar" size={20} color="black" />
                            </View>
                          </TouchableOpacity>

                          {openEndDate && (
                            <DatePicker
                              mode="calendar"
                              name="endDate"
                              minimumDate={
                                formik.values.dates?.startDate || minEndDate
                              } // Disable past dates
                              // selected={selectedDate}
                              selected={formik.values.dates?.endDate || formik.values.dates?.startDate || minEndDate}
                              // onMonthChange={(month, year) =>
                              //   console.log(month, year)
                              // }
                              onDateChange={handleEndDateChange}
                              maximumDate={maxEndDate}
                            />
                          )}
                        </View>
                        {formik.errors.dates?.endDate && (
                          <Text style={styles.error}>
                            {formik.errors.dates?.endDate}
                          </Text>
                        )}
                        </>
                      )}

          
                    </View>

                    {/* start time - end time */}

                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        borderWidth: 0,
                        marginVertical: '2%',
                      }}>
                      {/* 1-start */}
                      <View
                        style={styles.boxContainer}
                        ref={(ref) => fieldRefs.current.startTime = ref}
                      >
                        <Text style={styles.label}>Start Time: </Text>
                        <View
                          style={{
                            borderColor: '#e5e7eb',
                            borderWidth: 0,
                            borderRadius: 6,
                            flex: 1,
                            marginVertical: '5%',
                          }}>
                          <Button
                            title="Pick Start Time"
                            disabled={!isFormEditable || !formik.values.dates.startDate ||
                              (isEndDate === 'on' && !formik.values.dates.endDate)
                            }
                            onPress={() => showTimePicker('start')}
                          />

                          <TouchableOpacity
                            onPress={() => isFormEditable && showTimePicker('start')}
                            activeOpacity={0.8}
                            disabled={!isFormEditable || !formik.values.dates.startDate}
                          >
                            <TextInput
                              placeholderTextColor="#000"
                              keyboardType="default"
                              style={styles.input}
                              value={formik.values?.dates?.startTime}
                              editable={false}
                            />
                          </TouchableOpacity>

                          {formik.touched.dates?.startTime &&
                            formik.errors.dates?.startTime && (
                              <Text style={styles.error}>
                                {formik.errors.dates?.startTime}
                              </Text>
                            )}

                          <DateTimePickerModal
                            isVisible={isStartTimePickerVisible}
                            mode="time"
                            // minimumDate={new Date()}
                            onConfirm={time => handleConfirm(time, 'start')}
                            onCancel={() => hideTimePicker('start')}
                          />
                        </View>
                      </View>

                      {/* 2-end */}
                      <View
                        style={styles.boxContainer}
                        ref={(ref) => fieldRefs.current.endTime = ref}
                      >
                        <Text style={styles.label}>End Time: </Text>
                        <View
                          style={{
                            borderColor: '#e5e7eb',
                            borderWidth: 0,
                            borderRadius: 6,
                            flex: 1,
                            marginVertical: '5%',
                          }}>
                          <Button
                            title="Pick End Time"
                            disabled={
                              !isFormEditable ||
                              !formik.values.dates.startTime ||
                              (isEndDate === 'on' && !formik.values.dates.endDate)
                            }
                            onPress={() => {
                              showTimePicker('end');
                            }}
                          />

                          <TouchableOpacity
                            onPress={() => isFormEditable && showTimePicker('end')}
                            activeOpacity={0.8}
                            disabled={!isFormEditable || !formik.values.dates.startTime}
                          >
                            <TextInput
                              placeholderTextColor="#000"
                              keyboardType="default"
                              style={styles.input}
                              value={formik.values?.dates?.endTime}
                              editable={false}
                            />
                          </TouchableOpacity>
                          <DateTimePickerModal
                            isVisible={isEndTimePickerVisible}
                            mode="time"
                            onConfirm={time => handleConfirm(time, 'end')}
                            onCancel={() => hideTimePicker('end')}
                          />
                        </View>
                      </View>
                    </View>
                    <Text
                      style={{
                        color: 'black',
                        marginTop: '-2%',
                        fontSize: 8,
                        fontFamily:
                          'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                      }}>
                      (Start & end time are displayed in 24 hours format){' '}
                    </Text>
                    {/* ends */}

                    {/* booked info */}

                    {showBookedInfo ? (
                      <View style={{marginVertical: 5}}>
                        <Text
                          style={{
                            fontWeight: '400',
                            fontSize: 18,
                            color: 'red',
                            marginVertical: 10,
                            fontFamily:
                              'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                          }}>
                          Conflicting Courses
                        </Text>

                        <ScrollView horizontal>
                          <DataTable>
                            <DataTable.Header style={styles.header}>
                              <DataTable.Title style={styles.title}>
                                Start Date
                              </DataTable.Title>

                              <DataTable.Title style={styles.title}>
                                End Date
                              </DataTable.Title>

                              <DataTable.Title style={styles.title}>
                                Start Time
                              </DataTable.Title>
                              <DataTable.Title style={styles.title}>
                                End Time
                              </DataTable.Title>
                              <DataTable.Title style={styles.title}>
                                Days
                              </DataTable.Title>
                              <DataTable.Title
                                style={styles.title}></DataTable.Title>
                            </DataTable.Header>
                            {bookedSlots?.map((slot, index) => {
                              const formatdate = dateee => {
                                const dateString = dateee;
                                const date = moment(dateString);
                                // const formattedDate = date.format("ddd MMM DD YYYY");
                                const formattedDate = date.format('MMM-DD-YY');

                                return formattedDate;
                              };
                              return (
                                <DataTable.Row key={index}>
                                  <DataTable.Cell>
                                    {formatdate(slot.startDate)}
                                  </DataTable.Cell>
                                  <DataTable.Cell>
                                    {formatdate(slot.endDate)}
                                  </DataTable.Cell>
                                  <DataTable.Cell>
                                    {slot.conflictingStartTime}
                                  </DataTable.Cell>
                                  <DataTable.Cell>
                                    {slot.conflictingEndTime}
                                  </DataTable.Cell>
                                  <DataTable.Cell>
                                    {slot.conflictingDays.join(', ')}
                                  </DataTable.Cell>
                                  <DataTable.Cell>{}</DataTable.Cell>
                                </DataTable.Row>
                              );
                            })}
                          </DataTable>
                        </ScrollView>
                      </View>
                    ) : (
                      <></>
                    )}

                    {/* booked info ends */}

                    {/* fees */}
                    <View style={styles.boxContainer}>
                      {formik?.values?.classType == 'class' ? (
                        <>
                          {/* <Text style={{color: 'black', marginVertical: '5%',fontSize:16}}>Enter fees for Duration </Text> */}
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              marginVertical: 10,
                            }}>
                            {/* 1 */}
                            <View style={{flex: 1}} ref={(ref) => fieldRefs.current.fees30 = ref}>
                              <Text style={styles.label}>Price:</Text>
                              <Text style={styles.label}>30 Min</Text>
                              <TextInput placeholderTextColor="#000"
                                keyboardType="number-pad"
                                style={[styles.feesinput, {height: 50, opacity: isFormEditable ? 1 : 0.6}]}
                                onBlur={formik.handleBlur('fees.fees30')}
                                value={formik.values.fees?.fees30 ? formik.values.fees.fees30.toString() : ''}
                                onChangeText={(text) => {
                                  const numericValue = text === '' ? null : parseFloat(text);
                                  formik.setFieldValue('fees.fees30', numericValue);
                                }}
                                placeholder="₹0.00"
                                editable={isFormEditable}
                              />
                              {formik.touched.fees?.fees30 &&
                                formik.errors.fees?.fees30 && (
                                  <Text style={[styles.error, {marginTop: 4}]}>
                                    {formik.errors.fees?.fees30}
                                  </Text>
                                )}
                            </View>

                            {/* 3 */}
                            <View style={{flex: 1}} ref={(ref) => fieldRefs.current.fees60 = ref}>
                              <Text style={styles.label}>Price:</Text>
                              <Text style={styles.label}>60 Min</Text>
                              <TextInput placeholderTextColor="#000"
                                keyboardType="number-pad"
                                style={[styles.feesinput, {height: 50, opacity: isFormEditable ? 1 : 0.6}]}
                                onBlur={formik.handleBlur('fees.fees60')}
                                value={formik.values.fees?.fees60 ? formik.values.fees.fees60.toString() : ''}
                                onChangeText={(text) => {
                                  const numericValue = text === '' ? null : parseFloat(text);
                                  formik.setFieldValue('fees.fees60', numericValue);
                                }}
                                placeholder="₹0.00"
                                editable={isFormEditable}
                              />
                              {formik.touched.fees?.fees60 &&
                                formik.errors.fees?.fees60 && (
                                  <Text style={[styles.error, {marginTop: 4}]}>
                                    {formik.errors.fees?.fees60}
                                  </Text>
                                )}
                            </View>
                          </View>
                          {/* Display global fees error for class type */}
                          {formik.touched.fees && formik.errors.fees && typeof formik.errors.fees === 'string' && (
                            <Text style={[styles.error, {marginTop: 8}]}>
                              {formik.errors.fees}
                            </Text>
                          )}
                        </>
                      ) : (
                        <>
                          <View style={[styles.boxContainer, {flex: 1}]} ref={(ref) => fieldRefs.current.feesCourse = ref}>
                            <Text style={styles.label}>Price</Text>
                            <TextInput placeholderTextColor="#000"
                              keyboardType="numeric"
                              style={[styles.input, {height: 50, opacity: isFormEditable ? 1 : 0.6}]}
                              onBlur={formik.handleBlur('fees.feesCourse')}
                              value={formik.values.fees?.feesCourse ? formik.values.fees.feesCourse.toString() : ''}
                              onChangeText={(text) => {
                                const numericValue = text === '' ? null : parseFloat(text);
                                formik.setFieldValue('fees.feesCourse', numericValue);
                              }}
                              placeholder="₹0.00"
                              editable={isFormEditable}
                            />
                            {formik.touched.fees?.feesCourse &&
                              formik.errors.fees?.feesCourse && (
                                <Text style={[styles.error, {marginTop: 4}]}>
                                  {formik.errors.fees?.feesCourse}
                                </Text>
                              )}
                          </View>
                        </>
                      )}
                    </View>
                    {formik.values.classType === 'course' && (
                      <View style={styles.boxContainer}>
                      <Text style={[styles.label, {marginBottom: 6}]}>
                        Session Type{' '}
                      </Text>
                      <View
                        style={{
                          borderColor: '#e5e7eb',
                          borderWidth: 1,
                          borderRadius: 6,
                          opacity: isFormEditable ? 1 : 0.6,
                        }}>
                        <Picker
                          selectedValue={formik.values?.sessionType}
                          enabled={isFormEditable}
                          onValueChange={(itemValue, itemIndex) => {
                            if (!isFormEditable) {
                              return;
                            }
                            formik.setFieldValue('sessionType', itemValue);
                          }}  style={{ color: 'black' }}>
                          <Picker.Item label="Select a session" value="" />
                          <Picker.Item label="Group" value="group" />
                          <Picker.Item label="Individual" value="individual" />
                        </Picker>
                      </View>
                      {formik.touched.sessionType &&
                        formik.errors.sessionType && (
                          <Text style={styles.error}>
                            {formik.errors.sessionType}
                          </Text>
                        )}
                    </View>
                    )}
                    {/* session Type */}


                    {/* is campp  */}
                    {formik.values.classType === 'course' ? (
                      <>
                        <View style={styles.boxContainer}>
                          <RadioButton.Group
                            onValueChange={newValue => {
                              if (isFormEditable) {
                                formik.setFieldValue('camp', newValue);
                              }
                            }}
                            value={formik.values.camp}
                            // style={{flexDirection:'row'}}
                          >
                            <View
                              style={{
                                marginLeft: '3%',
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'flex-start',
                              }}>
                              <Text
                                style={{
                                  color: 'black',
                                  fontSize: 16,
                                  fontFamily:
                                    'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                                }}>
                                Is it a a camp
                              </Text>
                              <RadioButton.Item label="Yes" value={true} />
                              <RadioButton.Item label="No" value={false} />
                            </View>
                          </RadioButton.Group>
                          {formik.values.camp == true ? (
                            <>
                              <TextInput               placeholderTextColor="#000"

                                keyboardType="default"
                                style={[styles.input, {opacity: isFormEditable ? 1 : 0.6}]}
                                onBlur={formik.handleBlur('campName')}
                                value={formik.values.campName}
                                onChangeText={formik.handleChange('campName')}
                                placeholder="Enter Camp Name"
                                editable={isFormEditable}
                              />
                              {formik.touched.campName &&
                                formik.errors.campName && (
                                  <Text>{formik.errors.campName}</Text>
                                )}
                            </>
                          ) : (
                            <></>
                          )}
                        </View>
                      </>
                    ) : (
                      <></>
                    )}

                    {/* category Type */}
                    <View
                      style={styles.boxContainer}
                      ref={(ref) => fieldRefs.current.category = ref}
                    >
                      <Text style={[styles.label]}> Category </Text>
                      <View
                        style={{
                          borderColor: '#e5e7eb',
                          borderWidth: 1,
                          borderRadius: 6,
                          opacity: isFormEditable ? 1 : 0.6,
                        }}>
                        <Picker
                          selectedValue={formik.values.category}
                          enabled={isFormEditable}
                          onValueChange={async (itemValue, itemIndex) => {
                            if (!isFormEditable) {
                              return;
                            }
                            await formik.setFieldValue('category', itemValue);
                            formik.validateField('category');
                          }}  style={{ color: 'black' }}>
                          <Picker.Item label="Select the category" value="" />
                          {categories?.map((category, index) => (
                            <Picker.Item
                              label={`${category.name}`}
                              value={`${category.name}`}
                              key={index}
                            />
                          ))}
                        </Picker>
                      </View>

                      {formik.touched.category && formik.errors.category && (
                        <Text style={styles.error}>
                          {formik.errors.category}
                        </Text>
                      )}
                    </View>

                    {/* select facility */}
                    <View
                      style={styles.boxContainer}
                      ref={(ref) => fieldRefs.current.facility = ref}
                    >
                      <Text style={[styles.label]}>Select Facility </Text>
                      <View
                        style={{
                          borderColor: '#e5e7eb',
                          borderWidth: 1,
                          borderRadius: 6,
                          opacity: isFormEditable ? 1 : 0.6,
                        }}>
                        <Picker
                         style={{ color: 'black' }}
                          selectedValue={formik.values.facility?.name}
                          enabled={isFormEditable}
                          onValueChange={async (itemValue, itemIndex) => {
                            if (!isFormEditable) {
                              return;
                            }

                            // Mark field as touched when user interacts with it
                            formik.setFieldTouched('facility', true);

                            await formik.setFieldValue('facility.name', itemValue);
                            formik.validateField('facility');
                            const selectedFacility = facilities.find(
                              facilit => facilit.name === itemValue,
                            );
                            if (selectedFacility) {
                              // Set the entire selectedFacilit object to formik field
                              formik.setFieldValue(
                                'facility.name',
                                selectedFacility.name,
                              );
                              formik.setFieldValue(
                                'facility.addressLine1',
                                selectedFacility.addressLine1,
                              );
                              formik.setFieldValue(
                                'facility.addressLine2',
                                selectedFacility.addressLine2,
                              );
                              formik.setFieldValue(
                                'facility.city',
                                selectedFacility.city,
                              );
                              formik.setFieldValue(
                                'facility.country',
                                selectedFacility.country,
                              );
                              formik.setFieldValue(
                                'facility.state',
                                selectedFacility.state,
                              );
                              formik.setFieldValue(
                                'facility.pinCode',
                                selectedFacility.pinCode,
                              );
                              formik.setFieldValue(
                                'amenitiesProvided',
                                selectedFacility.amenities,
                              );
                              
                              setInitalAmenities(selectedFacility.amenities);
                            }
                          }}>
                          <Picker.Item label="Select the facility" value="" />
                          {facilities?.map((facilit, index) => {
                            return (
                              <Picker.Item
                                label={`${facilit.name}`}
                                value={`${facilit.name}`}
                                key={index}
                              />
                            );
                          })}
                        </Picker>
                      </View>
                      {formik.touched.facility && formik.errors.facility && (
                        <Text style={[styles.error, {marginTop: 4}]}>
                          {typeof formik.errors.facility === 'string'
                            ? formik.errors.facility
                            : formik.errors.facility?.name || 'Facility is required'}
                        </Text>
                      )}
                    </View>

                    {/* Maximum Group Size */}
                    <View
                      style={styles.boxContainer}
                      ref={(ref) => fieldRefs.current.maxGroupSize = ref}
                    >
                      <Text style={styles.label}>Maximum Group Size </Text>
                      <TextInput               placeholderTextColor="#000"

                        keyboardType="number-pad"
                        style={[styles.input, {opacity: isFormEditable ? 1 : 0.6}]}
                        onBlur={formik.handleBlur('maxGroupSize')}
                        value={formik.values.maxGroupSize ? formik.values.maxGroupSize.toString() : ''}
                        onChangeText={async (text) => {
                          // Handle empty input properly to avoid NaN
                          const numericValue = text === '' ? '' : text;
                          await formik.setFieldValue('maxGroupSize', numericValue);
                          formik.validateField('maxGroupSize');
                        }}
                        placeholder="Enter max group size"
                        editable={isFormEditable}
                      />

                      {formik.touched.maxGroupSize &&
                        formik.errors.maxGroupSize && (
                          <Text style={[styles.error, {marginTop: 4}]}>
                            {formik.errors.maxGroupSize}
                          </Text>
                        )}
                    </View>
                    {/* Proficiency */}

                    <View
                      style={styles.boxContainer}
                      ref={(ref) => fieldRefs.current.proficiency = ref}
                    >
                      <Text style={[styles.label]}>Proficiency Level </Text>
                      <View style={{opacity: isFormEditable ? 1 : 0.6}}>
                        <SectionedMultiSelect
                          items={ProficiencyOptions}
                          uniqueKey="name"
                          selectText="Select the Proficiency"
                          showDropDowns={false}
                          readOnlyHeadings={false}
                          headerComponent={
                            <Text style={{marginTop: 10}}></Text>
                          }
                          // hideSelect={true}
                          disabled={!isFormEditable}
                          // cancelIconComponent={EmptyComponent}
                          hideSearch={true}
                          onSelectedItemsChange={onSelectedItemsChange}
                          selectedItems={selectedItems}
                          IconRenderer={DropIcon}
                          removeAll={true}
                          styles={{
                            selectToggle: styles.selectToggle,
                            chipText: styles.chipText,
                          }}
                          searchIconComponent={
                            <DropIcon name="search" size={24} color="#000" />
                          }
                          selectedIconComponent={
                            <DropIcon name="check" size={24} color="#000" />
                          }
                          dropDownToggleIconUpComponent={
                            <DropIcon
                              name="arrow-drop-up"
                              size={24}
                              color="#000"
                            />
                          }
                          dropDownToggleIconDownComponent={
                            <DropIcon
                              name="arrow-drop-down"
                              size={24}
                              color="#000"
                            />
                          }
                        />
                        {/* <Picker
                          selectedValue={formik.values.proficiency}
                          onValueChange={(itemValue, itemIndex) => {
                            if (isEdit == true) {
                              return;
                            }
                            formik.setFieldValue('proficiency', itemValue);
                          }}>
                          <Picker.Item
                            label="Select the Proficiency level"
                            value=""
                          />
                          <Picker.Item label="Beginner" value="beginner" />
                          <Picker.Item
                            label="Intermediate"
                            value="intermediate"
                          />
                          <Picker.Item label="Advance" value="advance" />
                        </Picker> */}
                      </View>
                      {formik.touched.proficiency &&
                        formik.errors.proficiency && (
                          <Text style={[styles.error, {marginTop: 4}]}>
                            {formik.errors.proficiency}
                          </Text>
                        )}
                    </View>

                    {/* Amenities */}
                    <View style={styles.boxContainer}>
                      <Text style={styles.label}>Amenities </Text>

                      {
                        <QuillEditorComponent
                          initialValue={initalAmenities}
                          variable={'amenitiesProvided'}
                          formik={formik}
                          editable={isFormEditable}
                        />
                      }

                      {formik.touched.amenities && formik.errors.amenities && (
                        <Text style={[styles.error, {marginTop: 4}]}>
                          {formik.errors.amenities}
                        </Text>
                      )}
                    </View>
                    {/* things you have to carry */}

                    <View style={styles.boxContainer}>
                      <Text style={styles.label}>
                        Things have to Carry with
                      </Text>

                      {
                        <QuillEditorComponent
                          initialValue={initalwhatYouHaveToBring}
                          variable={'whatYouHaveToBring'}
                          formik={formik}
                          editable={isFormEditable}
                        />
                      }

                      {formik.touched.whatYouHaveToBring &&
                        formik.errors.whatYouHaveToBring && (
                          <Text style={[styles.error, {marginTop: 4}]}>
                            {formik.errors.whatYouHaveToBring}
                          </Text>
                        )}
                    </View>

                    {/* cancellation policy */}
                    <View style={styles.boxContainer}>
                      <Text style={styles.label}>Cancellation Policy</Text>

                      {
                        <QuillEditorComponent
                          initialValue={initalcancellationPolicy}
                          variable={'cancellationPolicy'}
                          formik={formik}
                          editable={isFormEditable}
                        />
                      }
                      {formik.touched.cancellationPolicy &&
                        formik.errors.cancellationPolicy && (
                          <Text style={[styles.error, {marginTop: 4}]}>
                            {formik.errors.cancellationPolicy}
                          </Text>
                        )}
                    </View>

                    {/* Show buttons only when form is editable or in create mode */}
                    {isFormEditable && (
                      <View style={{alignSelf: 'flex-end'}}>
                        <View
                          style={{
                            flexDirection: 'row',
                            justifyContent: 'space-around',
                            marginVertical: 10,
                            marginHorizontal: 20,
                          }}>
                          <TouchableOpacity onPress={handleReset}>
                            <Text
                              style={{
                                borderWidth: 1,
                                borderColor: 'grey',
                                padding: 10,
                                marginRight: 10,
                                fontWeight: '700',
                                color: 'black',
                                borderRadius: 10,
                                fontFamily:
                                  'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                              }}>
                              Cancel
                            </Text>
                          </TouchableOpacity>

                          <TouchableOpacity onPress={async ()=>{
                            console.log("Errors in course pressed: ", formik.errors);
                            // console.log("Values in on press: ", formik.values);
                            // console.log("ClassType: ", formik.values.classType);
                            // console.log("Fees: ", formik.values.fees);

                            // Validate the form first
                            const errors = await formik.validateForm();
                            console.log("Validation errors:", errors);

                            // If there are validation errors, scroll to the first error
                            if (Object.keys(errors).length > 0) {
                              // Mark all fields as touched to show error messages
                              const touchedFields = {
                                courseName: true,
                                description: true,
                                dates: {
                                  startDate: true,
                                  days: true,
                                  startTime: true,
                                  endTime: true,
                                },
                                fees: {
                                  feesCourse: true,
                                  fees30: true,
                                  fees60: true,
                                },
                                category: true,
                                facility: true,
                                maxGroupSize: true,
                                proficiency: true,
                              };

                              await formik.setTouched(touchedFields);

                              // Small delay to ensure error messages are rendered and DOM is updated
                              setTimeout(() => {
                                console.log("Attempting to scroll to first error...");
                                scrollToFirstError();
                              }, 200);

                              return; // Don't proceed with submission
                            }

                            // If no errors, proceed with normal submission
                            formik.handleSubmit();
                          }}
                          style={{
                            borderRadius: 10,
                            backgroundColor: 'lightskyblue',
                            borderColor: 'white',
                            borderWidth: 1,
                            padding: 10,
                          }}>
                            <Text
                              style={{
                                fontWeight: '700',
                                color: 'white',
                                fontFamily:
                                  'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                              }}>
                              {courseId ? 'Update' : 'Save'}
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    )}
                  </View>
                </ScrollView>
              </View>
            </View>
          )}
        </View>
      )}
    </SafeAreaView>
  );
};

export default Create;

const QuillEditorComponent = ({initialValue, variable, formik, editable = true}) => {
  const editorRef = useRef(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [lastFormikValue, setLastFormikValue] = useState('');
  const [isUserTyping, setIsUserTyping] = useState(false);

  const richTextHandle = async (descriptionText) => {
    if (editable) {
      setIsUserTyping(true); // Flag that this is user input

      if (descriptionText) {
        await formik.setFieldValue(variable, descriptionText);
        setLastFormikValue(descriptionText); // Track the value we just set
        formik.validateField(variable);
      } else {
        formik.setFieldValue(variable, '');
        setLastFormikValue('');
      }

      // Reset the flag after a short delay to allow for the formik update cycle
      setTimeout(() => {
        setIsUserTyping(false);
      }, 100);
    }
  };

  // Initialize editor content only once with initialValue
  useEffect(() => {
    if (editorRef.current && !isInitialized && initialValue !== undefined) {
      // Set the content in the editor only on first load
      editorRef.current.setContentHTML(initialValue || '');
      // Also update formik value only if it's empty
      if (!formik.values[variable]) {
        formik.setFieldValue(variable, initialValue || '');
      }
      setLastFormikValue(initialValue || '');
      setIsInitialized(true);
    }
  }, [initialValue, variable, isInitialized]);

  // Only sync when formik value changes from external sources (not from user input)
  useEffect(() => {
    if (editorRef.current && isInitialized && !isUserTyping) {
      const currentFormikValue = formik.values[variable] || '';

      // Only update editor if:
      // 1. The formik value changed from an external source (not from user input)
      // 2. The value is different from what we last set
      // 3. User is not currently typing
      if (currentFormikValue !== lastFormikValue) {
        editorRef.current.setContentHTML(currentFormikValue);
        setLastFormikValue(currentFormikValue);
      }
    }
  }, [formik.values[variable], isInitialized, lastFormikValue, isUserTyping]);

  return (
    <View
      style={{
        borderWidth: 1,
        borderRadius: 0,
        borderColor: '#ccc',
        height: 100,
        marginBottom: 50,
        opacity: editable ? 1 : 0.6,
      }}>
      <RichEditor
        ref={editorRef}
        initialContentHTML={initialValue || ''}
        onChange={richTextHandle}
        placeholder="Write your cool content here :)"
        androidHardwareAccelerationDisabled={true}
        style={styles.richTextEditorStyle}
        initialHeight={100}
        disabled={!editable}
      />
      <RichToolbar
        editor={editorRef}
        selectedIconTint="#873c1e"
        iconTint="#312921"
        actions={[
          // actions.insertImage,
          actions.setBold,
          actions.setItalic,
          actions.insertBulletsList,
          actions.insertOrderedList,
          actions.insertLink,
          actions.setStrikethrough,
          actions.setUnderline,
        ]}
        style={[styles.richTextToolbarStyle, {opacity: editable ? 1 : 0.6}]}
        disabled={!editable}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    height: '100%',

    justifyContent: 'center',
    alignItems: 'center',
  },
  boxContainer: {
    marginTop: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  input: {
    marginTop: 4,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#ccc',
    color: '#333',
    fontSize: 14,
  },
  error: {
    fontSize: 12,
    color: 'red',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },

  feesinput: {
    borderWidth: 1,
    borderColor: '#e5e7eb',
    paddingHorizontal: 12,
    marginHorizontal: '1%',
    //marginVertical: '5%',
    height: 40,
    flex: 1,
    //width:80,
    borderRadius: 6,
    color:"#000"
  },
  dayContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 5,
  },

  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    //marginTop: '2%',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 6,
    paddingVertical: '1%',
    paddingHorizontal: '6%',
  },
  textInput: {
    flex: 1,
    height: 50,
  },
  multiInput: {
    marginTop: 4,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#ccc',
    color: '#333',
    fontSize: 14,

    //height: 80,
  },
  selectToggle: {
    padding: 12,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
  },
  chipText: {
    maxWidth: 200,
  },

  header: {
    backgroundColor: '#f2f2f2',
  },
  title: {
    fontWeight: 'bold',
    paddingHorizontal: 12,
  },
  cell: {
    paddingHorizontal: 11,
  },
  richTextEditorStyle: {
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
    borderWidth: 1,
    borderColor: '#ccaf9b',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.23,
    shadowRadius: 2.62,
    elevation: 4,
    fontSize: 20,
  },

  richTextToolbarStyle: {
    backgroundColor: '#c6c3b3',
    borderColor: '#c6c3b3',
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    borderWidth: 1,
  },
});