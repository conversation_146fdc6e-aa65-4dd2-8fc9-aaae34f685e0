import {
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {Badge, DataTable} from 'react-native-paper';
import moment from 'moment';
import {Image} from 'react-native';
import axios from 'axios';
import {API} from '@env';
import {useRef} from 'react';
import {useAuth} from '../../components/Auth/AuthContext';
import {useNavigation} from '@react-navigation/native';
import {TextInput} from 'react-native';
import {Picker} from '@react-native-picker/picker';
import Icon from 'react-native-vector-icons/FontAwesome';
import { LocalSvg } from 'react-native-svg';
import { useFocusEffect } from '@react-navigation/native';

const Listing1 = () => {
  const [courseDetails, setCourseDetails] = useState([]);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const navigation = useNavigation();
  const {user, setPageName} = useAuth();
  const [classType, setClassType] = useState('');
  const [statusType, setStatusType] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [arrayLength,setArrayLength] = useState('')

  useFocusEffect(
    React.useCallback(() => {
      const fetchCourseDetails = async () => {
        setLoading(true);
        try {
          await getCourseDetails();
        } catch (error) {
          console.error('Error fetching course details:', error);
        } finally {
          setLoading(false);
        }
      };
  
      fetchCourseDetails();
  
      return () => {
        // Cleanup function if needed
      };
    }, [])
  );

  useEffect(() => {
    setLoading(true)
    getCourseDetails();
    setClassType('');
    setStatusType('');
    setSearchQuery('');

    
  }, []);

  const getCourseDetails = async () => {
    try {
      setLoading(true);
      const result = await axios.get(
        // `${API}/api/course/coach/${user?.data?._id}`,
         `${API}/api/course/?coach_id=${user?.data?._id}`,
      );
      //const result = await axios.get(`${API}/api/course/coach/${user?.data?._id}?page=${page}`);
      //console.log("resultttt dataa in list",result?.data?.data )
      setCourseDetails(result?.data?.data);
      const finalResult = await result?.data?.data
      
      setArrayLength(finalResult?.length)
      //setCourseDetails((prevData) =>[...prevData, result?.data?.data])
      setPage(prevPage => prevPage + 1);

      setTimeout(() => {
        setLoading(false)
      },2000);
      
    } catch (error) {
      console.log('error', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLoadMore = () => {
    if (!loading) {
      getCoachDetails();
    }
  };

  const ListEndLoader = () => {
    if (loading) {
      return <ActivityIndicator size={'large'} />;
    }
  };

  const renderItem = ({item, index}) => {
    var startDateMoment = moment(item?.dates?.startDate);
    var formattedDateStart = startDateMoment.format('Do MMMM YYYY');
   
    const endDateMoment = moment(item?.dates?.endDate.split("T")[0]); // Parse the date string to a Moment object
var formattedDateEnd = endDateMoment.format('Do MMMM YYYY');


    return (
      <ScrollView>
        <View style={{flex: 1, borderBottomWidth: 1, borderColor: '#DFDFDF'}}>
          <TouchableOpacity
            onPress={() => {
              //navigation.navigate('ListingDetails',{listing:item})
              setPageName('Create Course');
              navigation.push('CourseCreate', {
                source: 'course-listing',
                listing: item,
              });
            }}
            key={item.id}>
            <View
              style={{
                borderColor: '#DFDFDF',
                paddingVertical: '2%',
                // borderTopWidth: 1,
                paddingHorizontal: '2%',
              }}
              key={item.id}>
              <DataTable>
                <DataTable.Row style={styles.row}>
                  <DataTable.Cell>
                    <Text style={styles.cell}>Course Name :</Text>
                  </DataTable.Cell>
                  <DataTable.Cell>
                    <Text style={styles.value}>{item?.courseName}</Text>
                  </DataTable.Cell>
                </DataTable.Row>

                <DataTable.Row style={styles.row}>
                  <DataTable.Cell>
                    <Text style={styles.cell}>Start Date :</Text>
                  </DataTable.Cell>
                  <DataTable.Cell>
                    <Text style={styles.value}>{formattedDateStart}</Text>
                  </DataTable.Cell>
                </DataTable.Row>

                <DataTable.Row style={styles.row}>
                  <DataTable.Cell>
                    <Text style={styles.cell}>End Date :</Text>
                  </DataTable.Cell>
                  <DataTable.Cell>
                    <Text style={styles.value}>{formattedDateEnd}</Text>
                  </DataTable.Cell>
                </DataTable.Row>

                <DataTable.Row style={styles.row}>
                  <DataTable.Cell>
                    <Text style={styles.cell}>Facility Name :</Text>
                  </DataTable.Cell>
                  <DataTable.Cell>
                    <Text style={styles.value}>{item?.facility?.name}</Text>
                  </DataTable.Cell>
                </DataTable.Row>
              </DataTable>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginVertical: '5%',
                }}>
                <View style={{flexDirection: 'row', marginLeft: 12}}>
                  <View style={styles.buttonV}>
                    <Text style={styles.buttonT}>
                      {(item?.classType).charAt(0).toUpperCase() +
                        (item?.classType).slice(1)}
                    </Text>
                  </View>
                  {console.log(item.status)}
                  <View
                    style={{
                      borderWidth: 1,
                      borderColor:
                        item?.status?.toLowerCase() == 'active'
                          ? '#4CAF50'
                          : 'red',
                      borderRadius: 6,
                      marginLeft: 5,
                    }}>
                    <Text
                      style={{
                        paddingHorizontal: 10,
                        paddingVertical: 5,
                        color:
                          item?.status?.toLowerCase() == 'active'
                            ? '#4CAF50'
                            : 'red',
                        fontWeight: '500',
                        fontFamily:
                          'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                      }}>
                      {(item?.status).charAt(0).toUpperCase() +
                        (item?.status).slice(1)}
                    </Text>
                  </View>
                </View>

                <View
                  style={{
                    flexDirection: 'row',
                    marginRight: '3%',
                    alignItems: 'center',
                  }}>
                  <View>
                    <Image
                      source={require('../../assets/list.png')}
                      style={{
                        height: 30,
                        width: 30,
                        resizeMode: 'cover',
                        borderRadius: 50,
                        marginRight: 5,
                      }}
                    />
                  </View>
                  <View>
                    <Text
                      style={{
                        fontSize: 16,
                        fontWeight: '500',
                        color: 'black',
                        fontFamily:
                          'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                      }}>{`${item?.playerEnrolled}/${item?.maxGroupSize}`}</Text>
                  </View>
                </View>
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </ScrollView>
    );
  };


const handleSearchFilter = async ({search,clas,status}) => {
    
    try {
      console.log('shdakjshkjsf', searchQuery, classType, status);

      let queryString = '';
      if (search) {
        console.log('first', search);
        queryString = `courseName=${search}`;
      }
      if (clas) {
        queryString += `${queryString ? '&' : ''}classType=${clas}`;
      }
      if (status) {
        queryString += `${queryString ? '&' : ''}status=${status}`;
      }
      console.log('after query is made', queryString);
      console.log(`${API}/api/course/?coach_id=${user?.data?._id}&${queryString ? `${queryString}` : ""}`)
      const response = await axios.get(
        `${API}/api/course/?coach_id=${user?.data?._id}&${
          queryString ? `${queryString}` : ''
        }`,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${user?.data?.token}`,
          },
        },
      );
      console.log(response.data, "pppppp");
      const finalResult = await response?.data?.data;
      setCourseDetails([]);
      setCourseDetails(finalResult);
      
    } catch (error) {
      console.error('error', error);
      setClassType('');
      setStatusType('');
      setSearchQuery('');
    }
  };

  const seachFilterComp = () => {
    return (
      <View style={{ borderColor: '#DFDFDF',
      paddingVertical: '2%',
       borderBottomWidth: 1,}}>
        <View style={styles.boxContainer}>
          <Text style={[styles.label, {paddingHorizontal: '4%',}]}>
            All Courses
          </Text>
        </View>
        <View style={styles.search}>
          <TouchableOpacity 
           onPress={()=>{handleSearchFilter({
            search:searchQuery,
            clas:classType,
            status:statusType})
           }}
          >
            <Icon name="search" size={20} color="grey" />
          </TouchableOpacity>

          <TextInput               placeholderTextColor="#000"

            keyboardType="default"
            style={styles.input}
            placeholder="Search"
            onChangeText={text =>{ setSearchQuery(text);
            //console.log("in search", text)
                if (
                    text.length >= 3 ||
                    text.length == 0
                  )  
               { handleSearchFilter({
                    search:text,
                    clas:classType,
                    status:statusType})
               }
            }}
            value={searchQuery}
          />
        </View>

        <View style={styles.dropdownContainer}>
          <View
            style={{
              flex: 1,
              borderColor: '#e5e7eb',
              borderWidth: 1,
              borderRadius: 6,
              marginRight: 6,
    color:"#000"

            }}>
            <Picker
              selectedValue={classType}
              onValueChange={(itemValue, itemIndex) => {
                setClassType(itemValue),
                //   setTimeout(() => {
                    handleSearchFilter({
                        search:searchQuery,
                        clas:itemValue,
                        status:statusType})
                //   }, 1000);
              }}
              style={styles.dropdown}
              placeholder="Class Type">
              {/* <Picker.Item label="Class Type" value="" /> */}
              <Picker.Item label="Class" value="class" />
              <Picker.Item label="Course" value="course" />
              <Picker.Item label="All" value="" />
              <Picker.Item label="Class Type" value="" />
            </Picker>
          </View>

          <View
            style={{
              flex: 1,
              borderColor: '#e5e7eb',
              borderWidth: 1,
              borderRadius: 6,
            }}>
            <Picker
              selectedValue={statusType}
              onValueChange={(itemValue, itemIndex) => {
                setStatusType(itemValue),
                // setTimeout(() => {
                    handleSearchFilter({
                        search:searchQuery,
                        clas:classType,
                        status:itemValue})
                //   }, 1000)
              }}
              style={styles.dropdown}>
              {/* <Picker.Item label="Status Type" value="" /> */}
              <Picker.Item label="Archive" value="inactive" />
              <Picker.Item label="Active" value="active" />
              <Picker.Item label="All" value="" />
              <Picker.Item label="Status Type" value="" />
            </Picker>
          </View>
        </View>
      </View>
    );
  };

  return (
    <ScrollView>
 {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" />
        </View>
      ) :

      <View
        style={{
          backgroundColor: '#F5F8FC',
        }}>
        <View style={{backgroundColor: '#F5F8FC'}}>{seachFilterComp()}</View>

        { (arrayLength === 0 || arrayLength === undefined ) ?  <View style={{flex:1,padding:5,alignItems:'center',backgroundColor: 'white',}}>
       <Text style={{color:'black',fontSize:19,padding:4,fontFamily:'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',}}>No Listings Found</Text>
     </View> : 


        <View style={{borderWidth: 0, backgroundColor: 'white'}}>
          <FlatList
            data={courseDetails}
            keyExtractor={(item, index) => index.toString()}
            renderItem={renderItem}
            //  onEndReached={handleLoadMore}
            //  onEndReachedThreshold={0.8}
            //  ListFooterComponent={ListEndLoader}
          />
        </View>
}
      </View>
}
    </ScrollView>
  );
};

export default Listing1;

const styles = StyleSheet.create({

  loadingContainer: {
     
    height:'100%',

    justifyContent: 'center',
    alignItems: 'center',
  },
  // search comp

  search: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: '3%',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 6,
    paddingVertical: '1%',
    paddingHorizontal: '7%',
    marginHorizontal: 10,
  },
  input: {
    height: 40,
    width: '95%',
    //borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
    paddingHorizontal: 10,
    alignSelf: 'center',
    color:"#000"

    //  marginRight: 10,
  },
  dropdownContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: '2%',
    marginHorizontal: '3%',
    borderWidth: 0,
  },

  boxContainer: {
    marginTop: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
    fontFamily:'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  //   table - card

  cell: {
    fontSize: 14,
    fontWeight: '600',
    borderBottomWidth: 0, // This will remove the bottom border from all cells
    color: 'black',
    borderWidth: 0,
    alignItems: 'flex-start',
    fontFamily:'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  row: {
    borderBottomWidth: 0,
    marginVertical: '-2.5%',
  },
  value: {
    color: '#626262',
    fontSize: 14,
    fontWeight: '400',
    borderWidth: 0,
    alignItems: 'center',
    //justifyContent:'center',
    flexWrap: 'wrap',
    marginLeft: '-25%',
    fontFamily:'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },

  buttonV: {
    borderWidth: 1,
    borderColor: '#0068FF',
    borderRadius: 6,
  },
  dropdown:{color:"#000"},

  buttonT: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    fontFamily:'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
    color: '#0068FF',
    fontWeight: '500',
  },
});