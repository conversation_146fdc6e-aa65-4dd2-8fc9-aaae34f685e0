import {
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  View,
  TouchableOpacity,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Platform,
  Keyboard,
  Button,
  Image,
  Alert,
  useWindowDimensions,
  ActivityIndicator,
  SafeAreaView,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import BouncyCheckbox from 'react-native-bouncy-checkbox';
import {Picker} from '@react-native-picker/picker';
import axios from 'axios';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {RadioButton, configureFonts} from 'react-native-paper';
import {launchImageLibrary} from 'react-native-image-picker';
import Svg, {Path} from 'react-native-svg';
import DatePicker from 'react-native-modern-datepicker';
import Icon from 'react-native-vector-icons/FontAwesome';
import {API} from '@env';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {getLoginToken} from '../../helpers';
import {useAuth} from '../../components/Auth/AuthContext';
import {DataTable} from 'react-native-paper';
import {useRoute} from '@react-navigation/native';
import moment from 'moment';
import QuillEditor, {QuillToolbar} from 'react-native-cn-quill';

const Create1 = () => {
  const route = useRoute();
  const {source} = route.params || {source: 'direct'};

  const {width} = useWindowDimensions();

  const _editorCancellationPolicy = React.createRef();
  const _editorAmenities = React.createRef();
  const _editorDescription = React.createRef();
  const _editorwhatYouHaveToBring = React.createRef();

  const [open, setOpen] = useState(false);
  const [openEndDate, setOpenEndDate] = useState(false);
  const [descriptionImage, setDescriptionImage] = useState([]);
  const [checkedDays, setCheckedDays] = useState([]);
  const [isEndDate, setIsEndDate] = useState('never');
  const [isStartTimePickerVisible, setStartTimePickerVisibility] =
    useState(false);
  const [isEndTimePickerVisible, setEndTimePickerVisibility] = useState(false);
  const [categories, setCategories] = useState([]);
  const [facilities, setFacilities] = useState([]);
  const [bookedSlots, setBookedSlots] = useState([]);
  const [showBookedInfo, setBookedInfo] = useState(false);
  const [courseId, setCourseId] = useState();
  const [initalDescription, setInitialDescription] = useState('<p></p>');
  const [initalAmenities, setInitalAmenities] = useState('<p></p>');
  const [initalwhatYouHaveToBring, setInitalwhatYouHaveToBring] =
    useState('<p></p>');
  const [initalcancellationPolicy, setInitalcancellationPolicy] =
    useState('<p></p>');
  const [isEdit, setIsEdit] = useState(false);
  const [loading, setLoading] = useState(false);
  const [courseList, setCourseList] = useState();

  const {user} = useAuth();

  //console.log("user in creTE", user)

  useEffect(() => {
    getCategories();
    getFacilities();

    const {source} = route.params || {source: 'direct'};

    if (
      source === 'course-listing' ||
      source === 'custom-calendar' ||
      source === 'dashboard'
    ) {
      setIsEdit(true);
      setLoading(true);
      const {listing} = route.params;
      //console.log("course listing in create", listing)
      setCourseList(listing);
      fillCourseDetails(listing);
      //setLoading(false)
      setTimeout(() => {
        setLoading(false);
      }, 3000);
    }
  }, []);

  const fillCourseDetails = details => {
    // Object.keys(details).forEach((key) => {
    // Check if the key exists in the initialValues to avoid setting undefined values
    //formik.setValues({...formik.values, ...details})

    // if (formik.initialValues.hasOwnProperty(key))

    setCourseId(details?._id);
    {
      // formik.setFieldValue(key, details[key]);
      formik.setValues({...formik.values, ...details});
    }
    if (details?.proficiency[0]) {
      formik.setFieldValue('proficiency', details?.proficiency[0]);
    }
    if (details?.dates?.startDate) {
      const startDateMoment = moment(details?.dates?.startDate);
      formattedDateStart = startDateMoment.format('YYYY-MM-DD');
      // console.log("second",formattedDateStart)
      formik.setFieldValue('dates.startDate', formattedDateStart);
    }

    if (details?.dates?.endDate) {
      setIsEndDate('on');
      const endDateMoment = moment(details?.dates?.endDate);
      formattedDateEnd = endDateMoment.format('YYYY-MM-DD');
      // console.log("second",formattedDateEnd)
      formik.setFieldValue('dates.endDate', formattedDateEnd);
    }
    // if(details?.fees){

    // if(details?.fees?.feesCourse !== null ){

    //    formik.setFieldValue('fees.feesCourse',details?.fees?.feesCourse)
    // }
    // else {
    //   formik.setFieldValue('fees.fees30',details?.fees?.fees30)
    //   formik.setFieldValue('fees.fees60',details?.fees?.fees60)
    //   formik.setFieldValue('fees.fees90',details?.fees?.fees90)
    // }

    // formik.setFieldValue('maxGroupSize',details?.maxGroupSize)

    setInitialDescription(details?.description);
    //  formik.setFieldValue('description',details?.description)

    //   setInitalAmenities(details?.amenitiesProvided)
    //    formik.setFieldValue('amenitiesProvided',details?.amenitiesProvided)

    //   setInitalwhatYouHaveToBring(details?.whatYouHaveToBring)
    //     formik.setFieldValue('whatYouHaveToBring',details?.whatYouHaveToBring)

    //   setInitalcancellationPolicy(details?.cancellationPolicy)
    //    formik.setFieldValue('cancellationPolicy',details?.cancellationPolicy)

    if (details?.images) {
      const imageUrls = details.images.map(image => ({url: image.url}));
      //console.log('images',imageUrls)
      setDescriptionImage(imageUrls);
      formik.setFieldValue('images', imageUrls);

      // setDescriptionImage([...descriptionImage,{url: details?.images[0]?.url}]);
      // formik.setFieldValue('images',details?.images?.url)
    }

    //console.log("inside fill", details?.dates?.days)
    setCheckedDays(details?.dates?.days);
    formik.setFieldValue('dates.days', details?.dates?.days);

    // });
  };

  const getCategories = async () => {
    try {
      let response = await axios.get(`${API}/api/category`);
      //console.log(response.data?.data, "oo get categoriessssss");
      setCategories(response?.data?.data);
    } catch (error) {
      console.log(error);
    }
  };

  const getFacilities = async () => {
    // try {
    //   const response = await axios.get(`${API}/api/coach/65cc6bac72786f4993a05c70`);
    //    //console.log("response facilities",response.data)
    //   // console.log(result,"8888")

    //   if (!response.error) {
    //     setFacilities(response.data.linkedFacilities);
    //   }
    // } catch (error) {
    //   console.log(error);
    // }
    // if(coachData){
    //   setFacilities(coachData?.data?.linkedFacilities)
    // }
    if (user) {
      setFacilities(user?.data?.linkedFacilities);
    }
  };

  const checkAvailableSlots = async () => {
    try {
      let lastDateOfYear = getLastDateOfCurrentYear();
      console.log('lastttt ', lastDateOfYear);

      const startDateTime = `${formik.values.dates.startDate}T${formik.values.dates.startTime}:00`;
      const endDateTime = `${
        formik.values.dates.endDate
          ? formik.values.dates.endDate
          : lastDateOfYear
      }T${formik.values.dates.endTime}:00`;

      let obj = {
        dates: {
          startDate: startDateTime,
          endDate: endDateTime,
          startTime: formik.values.dates.startTime,
          endTime: formik.values.dates.endTime,
          days: formik.values.dates.days,
          courseId: courseId ? courseId : null,
        },
      };

      console.log('datessss', obj);

      const response = await axios.post(
        `${API}/api/course/availableSlots/${user?.data?.id}`,
        obj,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${user?.data?.token}`,
          },
        },
      );

      console.log(response?.data, 'result');
      //console.log(response?.data, "oooo");
      setBookedSlots(response?.data?.conflictingDates);

      if (response?.data?.message == 'Slots available') {
        setBookedInfo(false);
        return true;
      } else {
        setBookedInfo(true);
        return false;
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleConfirm = async (time, type) => {
    if (type === 'start') {
      if (isTimeValid(time) && formik.values.dates.startDate !== '') {
        formik.setFieldValue('dates.startTime', '');
        const finalTime = formatTime(time);
        formik.setFieldValue('dates.startTime', finalTime);
        setStartTimePickerVisibility(false);
      } else {
        // Handle invalid time selection
        alert(
          'Please select a start date to select the start time or Please select the start time after the current time.',
        );
        setStartTimePickerVisibility(false);
      }
    } else if (type === 'end') {
      const finalTime = formatTime(time);
      const currentTime = new Date();

      let currentDate = currentTime.toISOString().split('T')[0];
      //console.log("first current dateee",currentDate)
      //const isSlotValid = await checkAvailableSlots(finalTime)

      //  if(formik.values.dates.startTime == ""){
      //   Alert.alert("Please select the start time first")
      //  }

      if (
        formik.values.dates.endDate === formik.values.dates.startDate ||
        formik.values.endDate == ''
      ) {
        //console.log("111111")
        if (
          finalTime > formik.values.dates.startTime &&
          formik.values.dates.startTime !== ''
        ) {
          formik.setFieldValue('dates.endTime', finalTime);

          setEndTimePickerVisibility(false);
        } else {
          // Handle invalid time selection
          alert('Please select the end time after the start time.');
          setEndTimePickerVisibility(false);
        }
      } else {
        console.log('inside else');
        formik.setFieldValue('dates.endTime', finalTime);
      }
    }
  };

  const formatTime = time => {
    // console.log("time inside format time",time)
    // time = time.toLocaleTimeString()
    //   const timeString = time; // Example time string
    // const timeParts = timeString.split(':'); // Split the time string by ':'
    // const hoursAndMinutes = timeParts.slice(0, 2); // Get the hours and minutes parts
    // const newTimeString = hoursAndMinutes.join(':'); // Join hours and minutes with ':'

    // console.log(newTimeString); // Output: '7:14 pm'

    // const timeString = time; // Example time string
    // const [timeOne, ampm] = timeString.split(' '); // Split the time string by space to separate time and AM/PM
    // const [hours, minutes] = timeOne.split(':'); // Split the time by ':'
    // const date = new Date(); // Create a new Date object with current date
    // date.setHours(parseInt(hours) + (ampm === "pm" && parseInt(hours) !== 12 ? 12 : 0)); // Set hours (adjust for PM)
    // date.setMinutes(parseInt(minutes)); // Set minutes

    // const formattedTime = date.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit', hour12: true });
    // console.log(formattedTime); // Output: "7:14 PM"
    //  return formattedTime

    // Convert the given date string to a Date object
    const givenTime = new Date(time);
    //console.log("given time ", givenTime)
    // Extract the time components (hours and minutes) from the given date
    const givenHours = givenTime.getHours();
    const givenMinutes = givenTime.getMinutes();

    const finalTime = `${givenHours}:${givenMinutes}`;
    //console.log("final timeee",finalTime)

    // Format the time as needed (e.g., to a string with leading zeros)
    const formattedTime = `${givenHours
      .toString()
      .padStart(2, '0')}:${givenMinutes.toString().padStart(2, '0')}`;

    //console.log("final time in format time ",formattedTime)
    return formattedTime;
  };

  const isTimeValid = dateTimeString => {
    // Get the current time
    const currentTime = new Date();

    let currentDate = currentTime.toISOString().split('T')[0];
    //console.log("first current dateee",currentDate)

    // Extract the time components (hours and minutes) from the current time
    const currentHours = currentTime.getHours();
    const currentMinutes = currentTime.getMinutes();

    // Convert the given date string to a Date object
    const givenTime = new Date(dateTimeString);
    //console.log("given time ", givenTime)
    // Extract the time components (hours and minutes) from the given date
    const givenHours = givenTime.getHours();
    const givenMinutes = givenTime.getMinutes();

    const finalTime = `${givenHours}:${givenMinutes}`;
    //console.log("givenHours ", givenHours,"givenMinutes",givenMinutes)

    if (formik.values.dates.startDate === currentDate) {
      // Compare the time components
      if (
        givenHours > currentHours ||
        (givenHours === currentHours && givenMinutes >= currentMinutes)
      ) {
        return true; // Given time is greater than or equal to current time
      } else {
        return false; // Given time is less than current time
      }
    } else {
      return true;
    }
  };

  const showTimePicker = type => {
    if (type == 'start') {
      if (isEdit == true) {
        return;
      }
      setStartTimePickerVisibility(true);
    } else if (type == 'end') {
      if (isEdit == true) {
        return;
      }
      setEndTimePickerVisibility(true);
    }
  };

  const hideTimePicker = type => {
    if (type == 'start') {
      setStartTimePickerVisibility(false);
    } else if (type == 'end') {
      setEndTimePickerVisibility(false);
    }
  };

  const toggleDay = day => {
    if (isEdit == true) {
      return;
    }
    let updatedDays;
    // Check if the day is already in the array
    if (checkedDays.includes(day)) {
      // Remove the day if it's already checked
      updatedDays = checkedDays.filter(item => item !== day);
      setCheckedDays(updatedDays);
    } else {
      // Add the day if it's not already checked
      updatedDays = [...checkedDays, day];
      setCheckedDays(updatedDays);
    }
    // Update the Formik field with the updated days
    formik.setFieldValue('dates.days', updatedDays);
    //console.log("first",formik.values?.dates?.days)
  };

  function daysDifference(startDate, endDate, daysArray) {
    const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const start = new Date(startDate);
    const end = new Date(endDate);

    let count = 0;
    let currentDate = new Date(start);

    while (currentDate <= end) {
      if (daysArray.includes(daysOfWeek[currentDate.getDay()])) {
        count++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return count;
  }

  const validationSchema = Yup.object().shape({
    courseName: Yup.string().required('Course Name is required'),
    campName:
      formik?.values?.camp == 'yes'
        ? Yup.string().required('Camp Name is required')
        : Yup.string(),

    description: Yup.string().required('Description is required'),
    // images:Yup.array()
    // .min(1,"atlest one image must be uploaded")
    // .required("atlest one image must be uploaded"),

    dates: Yup.object().shape({
      startDate: Yup.date().required('Start Date is required'),
      // .min(new Date(), 'Date selected should not be before the current date'),

      startTime: Yup.string().required('Start time is required'),
      endTime: Yup.string(),
      days: Yup.array()
        .min(1, 'At least one day must be selected')
        .required('At least one day must be selected'),
    }),

    fees: Yup.object().shape({
      feesCourse:
        formik?.values?.classType == 'course'
          ? Yup.number().required('field is required')
          : Yup.number(),
      fees30:
        formik?.values?.classType == 'class'
          ? Yup.number().required('field is required')
          : Yup.number(),
      fees45:
        formik?.values?.classType == 'class'
          ? Yup.number().required('field is required')
          : Yup.number(),
      fees60:
        formik?.values?.classType == 'class'
          ? Yup.number().required('field is required')
          : Yup.number(),
    }),

    category: Yup.string().required('category is required'),
    sessionType: Yup.string().required('session-type is required'),
    //facility:Yup.string().required('facility is required'),
    proficiency: Yup.string().required('proficiency is required'),
    maxGroupSize: Yup.number().required(' maximum-group-size  is required'),
    amenitiesProvided: Yup.string().required('amenities-provided are required'),
    whatYouHaveToBring: Yup.string(),
    cancellationPolicy: Yup.string(),
  });

  const formik = useFormik({
    initialValues: {
      courseName: '',
      camp: false,
      campName: '',
      description: '',
      images: [{url: ''}],
      classType: 'class',
      dates: {
        startDate: '',
        endDate: '',
        startTime: '',
        endTime: '',
        days: [],
      },

      fees: {
        feesCourse: '',
        fees30: '',
        fees45: '',
        fees60: '',
      },
      category: '',
      sessionType: '',
      facility: '',
      proficiency: '',
      maxGroupSize: '1',
      amenitiesProvided: '',
      whatYouHaveToBring: '',
      cancellationPolicy: '',
    },

    //validationSchema : validationSchema,
    onSubmit: async value => {
      try {
        // Call checkAvailableSlots function and wait for its result
        const slotsAvailable = await checkAvailableSlots();

        const isDaysExist = await daysDifference(
          value.startDate,
          value.endDate,
          value?.dates?.days,
        );

        if (isDaysExist == 0) {
          alert('Selected days are not in between the selected dates.');
          return;
        }

        // Proceed further only if slots are available
        if (slotsAvailable) {
          let startDateTime;
          let endDateTime;

          if (
            formik.values.dates.startDate !== '' &&
            formik.values.dates.endDate == ''
          ) {
            //console.log("first inside iffff codnnnn")
            let defaultEndDate = getLastDateOfCurrentYear();
            //console.log("defaultEndDate",defaultEndDate)
            formik.setFieldValue('dates.endDate', defaultEndDate);

            if (defaultEndDate) {
              //console.log("formik.values.dates.startDate",formik.values.dates.startDate,formik.values.dates.endDate)

              startDateTime = `${formik.values.dates.startDate}T${formik.values.dates.startTime}`;
              endDateTime = `${defaultEndDate}T${formik.values.dates.endTime}`;

              // await formik.setFieldValue("dates.startDate",startDateTime)
              // await formik.setFieldValue("dates.endDate",endDateTime)
              console.log(
                'startDateTime',
                startDateTime,
                'endDateTime',
                endDateTime,
              );
            }
          } else if (
            formik.values.dates.startDate !== '' &&
            formik.values.dates.endDate !== ''
          ) {
            console.log('inside else for datetime format');
            startDateTime = `${formik.values.dates.startDate}T${formik.values.dates.startTime}`;
            endDateTime = `${formik.values.dates.endDate}T${formik.values.dates.endTime}`;

            //  await formik.setFieldValue("dates.startDate",startDateTime)
            //  await formik.setFieldValue("dates.endDate",endDateTime)
            console.log(
              'startDateTime',
              startDateTime,
              'endDateTime',
              endDateTime,
            );
          }

          // if(formik.values.amenitiesProvided == "" || formik.values.cancellationPolicy == "" ||
          // formik.values.whatYouHaveToBring == "" || formik.values.description == ""){

          // }
          //console.log("value.fees", value.fees)

          value = {
            ...value,
            ...value?.fees,
            fees: {
              fees:
                formik.values.classType === 'course'
                  ? formik.values.fees.feesCourse
                  : formik.values.fees.fees30,
              feesCourse:
                formik.values.classType == 'class'
                  ? ''
                  : formik.values.fees.feesCourse,
              fees30:
                formik.values.classType == 'class'
                  ? formik.values.fees.fees30
                  : '',
              fees45:
                formik.values.classType == 'class'
                  ? formik.values.fees.fees45
                  : '',
              fees60:
                formik.values.classType == 'class'
                  ? formik.values.fees.fees60
                  : '',
            },
          };

          //console.log("values 2222", value)

          // let obj = { ...value, coach_id: user?.data?._id,
          // coachName: `${user?.data?.firstName} ${user?.data?.lastName}`,
          // value.dates.startDate:startDateTime,
          // value.dates.endDate:endDateTime,
          // coachEmail:user?.data?.email,
          // };

          let obj = {
            ...value,
            coach_id: user?.data?._id,
            coachName: `${user?.data?.firstName} ${user?.data?.lastName}`,
            coachEmail: user?.data?.email,
            value: {
              ...value,
              dates: {
                startDate: startDateTime,
                endDate: endDateTime,
              },
            },
          };
          console.log('values 2222', obj);
          // const response = await axios.post(`${API}/api/course/`, obj, {
          //   headers: {
          //     "Content-Type": "application/json",
          //     Authorization: `Bearer ${user?.data?.token}`,
          //   },
          // });

          // console.log(response.data, "result");
          // if (!response.error) {

          //   await updateUserDetails()
          //   setTimeout(() => {
          //     setLoading(false);
          //     alert(
          //       'Course is saved sucessfully',
          //     );
          //     navigation.navigate('Calendar');
          //   }, 3000);

          // }
        } else {
          Alert.alert(
            'Slots Not Available',
            'Please choose different dates or times.',
            [{text: 'OK'}],
          );
        }
      } catch (error) {
        console.log('error', error);
      }
    },
  });

  function getLastDateOfCurrentYear() {
    var today = new Date(); // Get current date
    var currentYear = today.getFullYear(); // Get current year
    var lastDate = `${currentYear}-12-31`; // Set to December 31st of the current year
    return lastDate;
    //return localDateString;
  }

  async function handleImagePicker() {
    if (isEdit == true) {
      return;
    }
    const options = {
      mediaType: 'photo',
      multiple: true, // Allow multiple selection
    };

    try {
      const result = await launchImageLibrary(options);

      // Check if the user cancelled the picker
      if (result.didCancel) {
        console.log('User cancelled image picker');
      } else if (result.error) {
        console.error('ImagePicker Error:', result.error);
      } else {
        // Handle the selected images
        // const selectedImages = result.assets.map(asset => ({
        //   url: asset.uri
        // }));
        console.log('Selected Image:', result.assets[0].uri);
        //setDescriptionImage([...descriptionImage,{url: result.assets[0].uri}]);
        const formData = new FormData();
        formData.append('image', {
          uri: `${result.assets[0].uri}`,
          type: 'image/jpg',
          name: `${result.assets[0].fileName}`,
        });

        console.log('first');
        const response = await axios.post(
          `${API}/api/course/uploadImage`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          },
        );

        const url = response?.data?.url;
        console.log('urllllll', url);

        // const getPreviewUrl = await axios.post(
        //   `${API}/api/coach/download`,
        //   { location: url },
        //   {
        //     headers: {
        //       "Content-Type": "application/json",
        //     },
        //   }
        // );
        // const previewUrl = getPreviewUrl.data.url;
        // console.log("previewurllllll",previewUrl)

        //Update descriptionImage state
        //setDescriptionImage(prevImages => [...prevImages, ...selectedImages]);
        setDescriptionImage([...descriptionImage, {url: url}]);

        //Update formik values
        const updatedImages = [...formik.values.images, {url: url}];
        formik.setFieldValue('images', updatedImages);
      }
    } catch (error) {
      formik.setFieldError('images', error.message);
      console.error('Error during image selection:', error);
    }
  }
  const showDatePicker = () => {
    if (isEdit == true) {
      return;
    }
    setOpen(!open);
  };
  const showEndDatePicker = () => {
    if (isEdit == true) {
      return;
    }
    setOpenEndDate(!openEndDate);
  };
  function handleStartDateChange(propDate) {
    let currentDateObject = new Date();
    let currentDate = currentDateObject.toISOString().split('T')[0];
    //console.log("current date", currentDate)
    let date = replaceSlashWithDash(propDate);
    try {
      if (date >= currentDate) {
        formik.setFieldValue('dates.startDate', date);
        formik.setFieldValue('dates.startTime', '');
        formik.setFieldValue('dates.endTime', '');
        setOpen(false);
      } else {
        alert('Start date cannot be in the past ');
        setOpen(false);
      }
    } catch (error) {
      // Handle validation errors
      console.log('erorrrrrrr');
      formik.setFieldError('dates.startDate', error.message);
      setOpen(false);
    }
  }

  function handleEndDateChange(propDate) {
    let date = replaceSlashWithDash(propDate);
    try {
      if (
        formik.values.dates.startDate !== '' &&
        date >= formik.values.dates.startDate
      ) {
        formik.setFieldValue('dates.endDate', date);
        formik.setFieldValue('dates.startTime', '');
        formik.setFieldValue('dates.endTime', '');
        setOpenEndDate(false);
      } else {
        alert('End date cannot be before start Date');
        setOpenEndDate(false);
      }
    } catch (error) {
      // Handle validation errors
      console.log('erorrrrrrr');
      formik.setFieldError('dates.endDate', error.message);
      setOpenEndDate(false);
    }
  }

  function replaceSlashWithDash(dateString) {
    return dateString.replace(/\//g, '-');
  }
  const handleReset = () => {
    formik.resetForm(); // Reset the form
    setDescriptionImage('');
    setCheckedDays([]);
    //setInitialDescription("<p></p>")
  };

  const deleteImageFiles = async (index, url) => {
    try {
      console.log('urll at index', index, url);
      const updatedImages = [...descriptionImage];
      updatedImages.splice(index, 1);

      console.log('image deleted n updayted');

      const formData = new FormData();
      formData.append('url', url);

      const response = await axios.post(
        `${API}/api/course/uploadImage`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      );
      const resp = response?.data;
      setDescriptionImage(updatedImages);
      formik.setFieldValue('images', updatedImages);
      // console.log(resp);
    } catch (error) {
      console.log(error);
    }
  };

  const handleFormEdit = () => {
    let currentDate = new Date();

    const currentMoment = moment(currentDate);
    const startMoment = moment(courseList?.dates?.startDate);
    const formattedCurrentDate = currentMoment.format('DD-MM-YY');
    const formattedStartDate = startMoment.format('DD-MM-YY');
    console.log('current date', currentDate);
    console.log(
      'listing obj',
      courseList?.dates?.startDate,
      courseList?.playerEnrolled,
    );

    if (
      formattedStartDate > formattedCurrentDate &&
      courseList?.playerEnrolled == 0
    ) {
      setLoading(true);
      console.log('condition passed');
      setIsEdit(false);
      setLoading(false);
    } else {
      alert(
        'Cannot edit the course,Course start date passed or player enrolled in the course',
      );
    }
  };

  return (
    <SafeAreaView>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" />
        </View>
      ) : (
        <View
          style={{backgroundColor: 'lightblue', padding: '2%', borderWidth: 0}}>
          <View
            style={{
              backgroundColor: 'white',
              paddingVertical: '4%',
              paddingHorizontal: '5%',
              borderWidth: 0,
              elevation: 5,
              borderRadius: 5,
            }}>
            <ScrollView
              showsVerticalScrollIndicator={false}
              showsHorizontalScrollIndicator={false}>
              {/* <View style={{alignItems:'center',paddingVertical:"5%"}}>
        <Text style={{fontSize:20,color:'black',fontWeight:'bold'}}>Courses</Text>
        </View>   */}

              <View
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  marginVertical: '4%',
                  borderWidth: 0,
                }}>
                {isEdit == true ? (
                  <TouchableOpacity
                    style={{alignSelf: 'flex-end'}}
                    onPress={handleFormEdit}>
                    <View
                      style={{
                        backgroundColor: '#4F46E5',
                        alignContent: 'center',
                        paddingHorizontal: 10,
                        paddingVertical: 5,
                        borderRadius: 6,
                      }}>
                      {/* <Svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon" class="-ml-0.5 h-5 w-5"><Path stroke-linecap="round" stroke-linejoin="round" d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"></Path></Svg> */}
                      <Text style={{color: 'white', fontWeight: '600'}}>
                        Edit Form
                      </Text>
                    </View>
                  </TouchableOpacity>
                ) : (
                  <></>
                )}

                <View style={styles.boxContainer}>
                  <Text style={styles.label}>Course Name</Text>
                  <TextInput
                    keyboardType="default"
                    style={styles.input}
                    onBlur={formik.handleBlur('courseName')}
                    value={formik.values.courseName}
                    onChangeText={formik.handleChange('courseName')}
                    placeholder="Enter Course Name"
                    editable={!isEdit}
                  />
                  {formik.touched.courseName && formik.errors.courseName && (
                    <Text style={styles.error}>{formik.errors.courseName}</Text>
                  )}
                </View>

                {/* description */}
                <View style={styles.boxContainer}>
                  <Text style={styles.label}>Description </Text>

                  {/* <TextInput
                    style={styles.multiInput}
                    multiline
                    numberOfLines={5}
                    onChangeText={formik.handleChange('description')}
                    value={formik?.values?.description}
                  /> */}

                  <View
                    style={{
                      borderWidth: 1,
                      borderRadius: 0,
                      borderColor: '#ccc',
                      height: 175,
                    }}>
                    <QuillEditor
                      //value={formik?.values?.description}
                      value={initalDescription}
                      autoSize
                      ref={_editorDescription}
                      quill={{theme: 'snow', placeholder: 'write here '}}
                      webview={{
                        scrollEnabled: true,
                        style: {
                          borderWidth: 1,
                          borderRadius: 6,
                          borderColor: '#ccc',
                        },
                        nestedScrollEnabled: true,
                        showsHorizontalScrollIndicator: true,
                        showsVerticalScrollIndicator: true,
                      }}
                      onHtmlChange={e => {
                        console.log('inside is edit descp', isEdit);
                        if (isEdit == true) {
                          return;
                        }
                        console.log('from html change description', e),
                          formik.setFieldValue('description', e);
                        console.log(
                          'in desp quill',
                          formik?.values?.description,
                        );
                      }}
                      onTextChange={html => handleEditorChange(html, 1)}
                      //onHtmlChange={(html) => handleEditorChange(html, 1)}

                      initialHtml={formik?.values?.description}
                    />
                  </View>

                  {formik.touched.description && formik.errors.description && (
                    <Text style={styles.error}>
                      {formik.errors.description}
                    </Text>
                  )}
                </View>

                {/* image upload */}
                <View style={styles.boxContainer}>
                  <View
                    style={{
                      width: '99%',
                      borderStyle: 'dashed',
                      borderWidth: 1,
                      // marginVertical: "5%",
                      marginHorizontal: '1%',
                      borderRadius: 6,
                      display: 'flex',
                      alignItems: 'center',
                    }}>
                    <View
                      style={{
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}>
                      <Svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 -5 24 28"
                        strokeWidth="1"
                        stroke="grey"
                        width={100}
                        height={100}
                        className="w-8 h-8">
                        <Path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"></Path>
                      </Svg>
                    </View>
                    <View
                      style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        marginBottom: 10,
                      }}>
                      <TouchableOpacity onPress={() => handleImagePicker()}>
                        <Text style={{color: 'blue'}}> Upload a file </Text>
                      </TouchableOpacity>
                      <Text>PNG, JPG, GIF up to 10MB</Text>
                    </View>

                    {/* Conditionally render images */}
                    {descriptionImage?.length > 0 && (
                      <View
                        style={{
                          flexDirection: 'row',
                          flexWrap: 'wrap',
                          justifyContent: 'flex-start',
                          alignItems: 'center',
                          marginBottom: 10,
                        }}>
                        {descriptionImage?.map((image, index) => (
                          <View
                            key={index}
                            style={{
                              position: 'relative',
                              width: '20%',
                              height: 80,
                              margin: 5,
                              borderRadius: 5,
                              overflow: 'hidden',
                            }}>
                            <Image
                              source={{uri: image.url}}
                              style={{
                                width: '100%',
                                height: '100%',
                                borderRadius: 5,
                              }}
                            />
                            <TouchableOpacity
                              onPress={() => deleteImageFiles(index, image.url)}
                              style={{
                                position: 'absolute',
                                top: 0,
                                right: 3,
                                zIndex: 1,
                              }}>
                              <Icon
                                name="close"
                                size={15}
                                color="red"
                                style={{fontWeight: 'bold'}}
                              />
                            </TouchableOpacity>
                          </View>
                        ))}
                      </View>
                    )}
                  </View>
                  {formik.touched.images && formik.errors.images && (
                    <Text style={styles.error}>{formik.errors.images}</Text>
                  )}
                </View>

                {/* class type */}
                <View style={styles.boxContainer}>
                  <RadioButton.Group
                    onValueChange={newValue => {
                      if (isEdit == true) {
                        return;
                      }
                      formik.setFieldValue('classType', newValue);
                    }}
                    value={formik.values.classType}>
                    <View
                      style={{
                        //marginLeft:'10%',
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                      }}>
                      <RadioButton.Item label="Session" value="class" />
                      <RadioButton.Item label="Course" value="course" />
                    </View>
                  </RadioButton.Group>
                </View>
                {/* dateeeee */}
                {/* start dateeee */}
                <View style={styles.boxContainer}>
                  <Text style={styles.label}> Start Date </Text>
                  <View style={styles.inputContainer}>
                    <TextInput
                      style={styles.textInput}
                      placeholder="yyyy-mm-dd"
                      name="startDate"
                      value={formik.values.dates.startDate}
                      editable={false}
                    />

                    <TouchableOpacity
                      onPress={showDatePicker}
                      style={styles.calendarIcon}>
                      <Icon name="calendar" size={20} color="black" />
                    </TouchableOpacity>
                  </View>

                  {open && (
                    <DatePicker
                      mode="calendar"
                      name="startDate"
                      minDate={new Date()}
                      selected={formik.values.dates?.startDate}
                      onMonthChange={(month, year) => console.log(month, year)}
                      onDateChange={handleStartDateChange}
                    />
                  )}
                  {formik.touched.dates?.startDate &&
                    formik.errors.dates?.startDate && (
                      <Text style={styles.error}>
                        {formik.errors.dates?.startDate}
                      </Text>
                    )}
                </View>
                {/* daysss */}

                <View style={[styles.boxContainer, {marginVertical: '3%'}]}>
                  <Text style={[styles.label, {marginVertical: 6}]}>
                    {' '}
                    Select Days{' '}
                  </Text>
                  <View style={{marginLeft: '2%'}}>
                    <View style={styles.dayContainer}>
                      <BouncyCheckbox
                        size={25}
                        fillColor="purple"
                        onPress={() => toggleDay('Mon')}
                        isChecked={checkedDays.includes('Mon')}
                      />
                      <Text>Monday</Text>
                    </View>
                    <View style={styles.dayContainer}>
                      <BouncyCheckbox
                        size={25}
                        fillColor="purple"
                        onPress={() => toggleDay('Tue')}
                        isChecked={checkedDays.includes('Tue')}
                      />
                      <Text>Tuesday</Text>
                    </View>
                    <View style={styles.dayContainer}>
                      <BouncyCheckbox
                        size={25}
                        fillColor="purple"
                        onPress={() => toggleDay('Wed')}
                        isChecked={checkedDays.includes('Wed')}
                      />
                      <Text>Wednesday</Text>
                    </View>
                    <View style={styles.dayContainer}>
                      <BouncyCheckbox
                        size={25}
                        fillColor="purple"
                        onPress={() => toggleDay('Thu')}
                        isChecked={checkedDays.includes('Thu')}
                      />
                      <Text>Thursday</Text>
                    </View>
                    <View style={styles.dayContainer}>
                      <BouncyCheckbox
                        size={25}
                        fillColor="purple"
                        onPress={() => toggleDay('Fri')}
                        isChecked={checkedDays.includes('Fri')}
                      />
                      <Text>Friday</Text>
                    </View>
                    <View style={styles.dayContainer}>
                      <BouncyCheckbox
                        size={25}
                        fillColor="purple"
                        onPress={() => toggleDay('Sat')}
                        isChecked={checkedDays.includes('Sat')}
                      />
                      <Text>Saturday</Text>
                    </View>
                    <View style={styles.dayContainer}>
                      <BouncyCheckbox
                        size={25}
                        fillColor="purple"
                        onPress={() => toggleDay('Sun')}
                        isChecked={checkedDays.includes('Sun')}
                      />
                      <Text>Sunday</Text>
                    </View>
                  </View>
                  {formik.touched.dates?.days && formik.errors.dates?.days && (
                    <Text style={styles.error}>
                      {formik.errors.dates?.days}
                    </Text>
                  )}
                </View>
                {/* days end */}

                {/* end dateee */}

                <View style={styles.boxContainer}>
                  <Text style={styles.label}> End Date </Text>
                  <RadioButton.Group
                    onValueChange={newValue => {
                      if (isEdit == true) {
                        return;
                      }
                      setIsEndDate(newValue);
                    }}
                    value={isEndDate}
                    // style={{flexDirection:'row'}}
                  >
                    <View
                      style={{
                        borderWidth: 0,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        marginTop: 8,
                      }}>
                      <View
                        style={{
                          borderWidth: 1,
                          flex: 1,
                          borderColor: '#e5e7eb',
                          borderTopLeftRadius: 5,
                          borderBottomLeftRadius: 5,
                        }}>
                        <RadioButton.Item value="never" label="Never" />
                      </View>
                      <View
                        style={{
                          borderWidth: 1,
                          flex: 1,
                          borderColor: '#e5e7eb',
                          borderTopRightRadius: 5,
                          borderBottomRightRadius: 5,
                        }}>
                        <RadioButton.Item label="On" value="on" />
                      </View>
                    </View>
                  </RadioButton.Group>
                  {isEndDate == 'on' ? (
                    <>
                      <View style={styles.boxContainer}>
                        <Text style={styles.label}>End Date </Text>
                        <View style={styles.inputContainer}>
                          <TextInput
                            style={styles.textInput}
                            placeholder="yyyy-mm-dd"
                            name="endDate"
                            //value={selectedDate ? selectedDate : ''}
                            value={formik.values.dates.endDate}
                            editable={false}
                          />
                          {formik.touched?.dates?.endDate &&
                            formik.errors?.dates?.endDate && (
                              <Text>{formik.errors?.dates?.endDate}</Text>
                            )}
                          <TouchableOpacity
                            onPress={showEndDatePicker}
                            style={styles.calendarIcon}>
                            <Icon name="calendar" size={20} color="black" />
                          </TouchableOpacity>
                        </View>
                        {formik.touched.dates?.endDate &&
                          formik.errors.dates?.endDate && (
                            <Text style={styles.error}>
                              {formik.errors.dates?.endDate}
                            </Text>
                          )}

                        {openEndDate && (
                          <DatePicker
                            mode="calendar"
                            name="endDate"
                            // selected={selectedDate}
                            selected={formik.values.dates?.endDate}
                            onMonthChange={(month, year) =>
                              console.log(month, year)
                            }
                            onDateChange={handleEndDateChange}
                          />
                        )}
                        {formik.touched.dates?.endDate &&
                          formik.errors.dates?.endDate && (
                            <Text style={styles.error}>
                              {formik.errors.dates?.endDate}
                            </Text>
                          )}
                      </View>
                    </>
                  ) : (
                    <></>
                  )}
                </View>
                {/* start time - end time */}

                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    borderWidth: 0,
                    marginVertical: '2%',
                  }}>
                  {/* 1-start */}
                  <View style={styles.boxContainer}>
                    <Text style={styles.label}>Start Time: </Text>
                    <View
                      style={{
                        borderColor: '#e5e7eb',
                        borderWidth: 0,
                        borderRadius: 6,
                        flex: 1,
                        marginVertical: '5%',
                      }}>
                      <Button
                        title="Pick Start Time"
                        onPress={() => showTimePicker('start')}
                      />

                      <TextInput
                        keyboardType="default"
                        style={styles.input}
                        value={formik.values?.dates?.startTime}
                      />

                      {formik.touched.dates?.startTime &&
                        formik.errors.dates?.startTime && (
                          <Text style={styles.error}>
                            {formik.errors.dates?.startTime}
                          </Text>
                        )}

                      <DateTimePickerModal
                        isVisible={isStartTimePickerVisible}
                        mode="time"
                        onConfirm={time => handleConfirm(time, 'start')}
                        onCancel={() => hideTimePicker('start')}
                      />
                    </View>
                  </View>

                  {/* 2-end */}
                  <View style={styles.boxContainer}>
                    <Text style={styles.label}>End Time: </Text>
                    <View
                      style={{
                        borderColor: '#e5e7eb',
                        borderWidth: 0,
                        borderRadius: 6,
                        flex: 1,
                        marginVertical: '5%',
                      }}>
                      <Button
                        title="Pick End Time"
                        onPress={() => showTimePicker('end')}
                      />

                      <TextInput
                        keyboardType="default"
                        style={styles.input}
                        value={formik.values?.dates?.endTime}
                      />
                      <DateTimePickerModal
                        isVisible={isEndTimePickerVisible}
                        mode="time"
                        onConfirm={time => handleConfirm(time, 'end')}
                        onCancel={() => hideTimePicker('end')}
                      />
                    </View>
                  </View>
                </View>
                <Text style={{color: 'black', marginTop: '-2%', fontSize: 8}}>
                  (Start & end time are displayed in 24 hours format){' '}
                </Text>
                {/* ends */}

                {/* booked info */}

                {showBookedInfo ? (
                  <View style={{marginVertical: 5}}>
                    <Text
                      style={{
                        fontWeight: '400',
                        fontSize: 18,
                        color: 'red',
                        marginVertical: 10,
                      }}>
                      Conflicting Courses
                    </Text>

                    <ScrollView horizontal>
                      <DataTable>
                        <DataTable.Header style={styles.header}>
                          <DataTable.Title style={styles.title}>
                            Start Date
                          </DataTable.Title>

                          <DataTable.Title style={styles.title}>
                            End Date
                          </DataTable.Title>

                          <DataTable.Title style={styles.title}>
                            Start Time
                          </DataTable.Title>
                          <DataTable.Title style={styles.title}>
                            End Time
                          </DataTable.Title>
                          <DataTable.Title style={styles.title}>
                            Days
                          </DataTable.Title>
                          <DataTable.Title
                            style={styles.title}></DataTable.Title>
                        </DataTable.Header>
                        {bookedSlots?.map((slot, index) => {
                          const formatdate = dateee => {
                            const dateString = dateee;
                            const date = moment(dateString);
                            // const formattedDate = date.format("ddd MMM DD YYYY");
                            const formattedDate = date.format('MMM-DD-YY');

                            return formattedDate;
                          };
                          return (
                            <DataTable.Row key={index}>
                              <DataTable.Cell>
                                {formatdate(slot.startDate)}
                              </DataTable.Cell>
                              <DataTable.Cell>
                                {formatdate(slot.endDate)}
                              </DataTable.Cell>
                              <DataTable.Cell>
                                {slot.conflictingStartTime}
                              </DataTable.Cell>
                              <DataTable.Cell>
                                {slot.conflictingEndTime}
                              </DataTable.Cell>
                              <DataTable.Cell>
                                {slot.conflictingDays.join(', ')}
                              </DataTable.Cell>
                              <DataTable.Cell>{}</DataTable.Cell>
                            </DataTable.Row>
                          );
                        })}
                      </DataTable>
                    </ScrollView>
                  </View>
                ) : (
                  <></>
                )}

                {/* booked info ends */}

                {/* fees */}
                <View style={styles.boxContainer}>
                  {formik?.values?.classType == 'class' ? (
                    <>
                      {/* <Text style={{color: 'black', marginVertical: '5%',fontSize:16}}>Enter fees for Duration </Text> */}
                      <View
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          marginVertical: 10,
                        }}>
                        {/* 1 */}
                        <View style={{flex: 1}}>
                          <Text style={styles.label}>Price:</Text>
                          <Text style={styles.label}>30 Min</Text>
                          <TextInput
                            keyboardType="number-pad"
                            style={styles.feesinput}
                            onBlur={formik.handleBlur('fees.fees30')}
                            value={formik.values.fees?.fees30?.toString()}
                            onChangeText={formik.handleChange('fees.fees30')}
                            placeholder="₹0.00"
                            editable={!isEdit}
                          />
                          {formik.touched.fees?.fees30 &&
                            formik.errors.fees?.fees30 && (
                              <Text style={styles.error}>
                                {formik.errors.fees?.fees30}
                              </Text>
                            )}
                        </View>

                        {/* 2 */}
                        <View style={{flex: 1}}>
                          <Text style={styles.label}>Price:</Text>
                          <Text style={styles.label}>45 Min</Text>
                          <TextInput
                            keyboardType="number-pad"
                            style={styles.feesinput}
                            onBlur={formik.handleBlur('fees.fees45')}
                            value={formik.values.fees?.fees45?.toString()}
                            onChangeText={formik.handleChange('fees.fees45')}
                            placeholder="₹0.00"
                            editable={!isEdit}
                          />
                          {formik.touched.fees?.fees45 &&
                            formik.errors.fees?.fees45 && (
                              <Text>{formik.errors.fees?.fees45}</Text>
                            )}
                        </View>

                        {/* 3 */}
                        <View style={{flex: 1}}>
                          <Text style={styles.label}>Price:</Text>
                          <Text style={styles.label}>60 Min</Text>
                          <TextInput
                            keyboardType="number-pad"
                            style={styles.feesinput}
                            onBlur={formik.handleBlur('fees.fees60')}
                            value={formik?.values?.fees?.fees60?.toString()}
                            onChangeText={formik.handleChange('fees.fees60')}
                            placeholder="₹0.00"
                            editable={!isEdit}
                          />
                          {formik.touched.fees?.fees60 &&
                            formik.errors.fees?.fees60 && (
                              <Text>{formik.errors.fees?.fees60}</Text>
                            )}
                        </View>
                      </View>
                    </>
                  ) : (
                    <>
                      <View style={[styles.boxContainer, {flex: 1}]}>
                        <Text style={styles.label}>Price</Text>
                        <TextInput
                          keyboardType="numeric"
                          style={styles.input}
                          onBlur={formik.handleBlur('fees.feesCourse')}
                          value={formik.values.fees?.feesCourse?.toString()}
                          onChangeText={formik.handleChange('fees.feesCourse')}
                          placeholder="₹0.00"
                          editable={!isEdit}
                        />
                        {formik.touched.fees?.feesCourse &&
                          formik.errors.fees?.feesCourse && (
                            <Text style={styles.error}>
                              {formik.errors.fees?.feesCourse}
                            </Text>
                          )}
                      </View>
                    </>
                  )}
                </View>

                {/* session Type */}
                <View style={styles.boxContainer}>
                  <Text style={[styles.label, {marginBottom: 6}]}>
                    Session Type{' '}
                  </Text>
                  <View
                    style={{
                      borderColor: '#e5e7eb',
                      borderWidth: 1,
                      borderRadius: 6,
                    }}>
                    <Picker
                      selectedValue={formik.values?.sessionType}
                      onValueChange={(itemValue, itemIndex) => {
                        if (isEdit == true) {
                          return;
                        }
                        formik.setFieldValue('sessionType', itemValue);
                      }}>
                      <Picker.Item label="Select a session" value="" />
                      <Picker.Item label="Group" value="group" />
                      <Picker.Item label="Individual" value="individual" />
                    </Picker>
                  </View>
                  {formik.touched.sessionType && formik.errors.sessionType && (
                    <Text style={styles.error}>
                      {formik.errors.sessionType}
                    </Text>
                  )}
                </View>

                {/* is campp  */}
                {formik.values.classType === 'course' ? (
                  <>
                    <View style={styles.boxContainer}>
                      <RadioButton.Group
                        onValueChange={newValue => {
                          formik.setFieldValue('camp', newValue);
                        }}
                        value={formik.values.camp}
                        // style={{flexDirection:'row'}}
                      >
                        <View
                          style={{
                            marginLeft: '3%',
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                          }}>
                          <Text style={{fontSize: 16}}>Is it a a camp</Text>
                          <RadioButton.Item label="Yes" value={true} />
                          <RadioButton.Item label="No" value={false} />
                        </View>
                      </RadioButton.Group>
                      {formik.values.camp == true ? (
                        <>
                          <TextInput
                            keyboardType="default"
                            style={styles.input}
                            onBlur={formik.handleBlur('campName')}
                            value={formik.values.campName}
                            onChangeText={formik.handleChange('campName')}
                            placeholder="Enter Camp Name"
                          />
                          {formik.touched.campName &&
                            formik.errors.campName && (
                              <Text>{formik.errors.campName}</Text>
                            )}
                        </>
                      ) : (
                        <></>
                      )}
                    </View>
                  </>
                ) : (
                  <></>
                )}

                {/* category Type */}
                <View style={styles.boxContainer}>
                  <Text style={[styles.label]}> Category </Text>
                  <View
                    style={{
                      borderColor: '#e5e7eb',
                      borderWidth: 1,
                      borderRadius: 6,
                    }}>
                    <Picker
                      selectedValue={formik.values.category}
                      onValueChange={(itemValue, itemIndex) => {
                        if (isEdit == true) {
                          return;
                        }
                        formik.setFieldValue('category', itemValue);
                      }}>
                      <Picker.Item label="Select the category" value="" />
                      {categories?.map((category, index) => (
                        <Picker.Item
                          label={`${category.name}`}
                          value={`${category.name}`}
                          key={index}
                        />
                      ))}
                    </Picker>
                  </View>

                  {formik.touched.category && formik.errors.category && (
                    <Text style={styles.error}>{formik.errors.category}</Text>
                  )}
                </View>

                {/* select facility */}
                <View style={styles.boxContainer}>
                  <Text style={[styles.label]}>Select Facility </Text>
                  <View
                    style={{
                      borderColor: '#e5e7eb',
                      borderWidth: 1,
                      borderRadius: 6,
                    }}>
                    <Picker
                      selectedValue={formik.values.facility}
                      onValueChange={(itemValue, itemIndex) => {
                        if (isEdit == true) {
                          return;
                        }
                        formik.setFieldValue('facility', itemValue);
                      }}>
                      <Picker.Item label="Select the facility" value="" />
                      {facilities?.map((facility, index) => (
                        <Picker.Item
                          label={`${facility.name}`}
                          value={`${facility.name}`}
                          key={index}
                        />
                      ))}
                    </Picker>
                  </View>
                  {formik.touched.facility && formik.errors.facility && (
                    <Text style={styles.error}>{formik.errors.facility}</Text>
                  )}
                </View>

                {/* Maximum Group Size */}
                <View style={styles.boxContainer}>
                  <Text style={styles.label}>Maximum Group Size </Text>
                  <TextInput
                    keyboardType="number-pad"
                    style={styles.input}
                    onBlur={formik.handleBlur('maxGroupSize')}
                    value={formik.values.maxGroupSize.toString()}
                    onChangeText={formik.handleChange('maxGroupSize')}
                    placeholder="Enter max group size"
                    editable={!isEdit}
                  />

                  {formik.touched.maxGroupSize &&
                    formik.errors.maxGroupSize && (
                      <Text style={styles.error}>
                        {formik.errors.maxGroupSize}
                      </Text>
                    )}
                </View>
                {/* select facility */}

                <View style={styles.boxContainer}>
                  <Text style={[styles.label]}>Proficiency Level </Text>
                  <View
                    style={{
                      borderColor: '#e5e7eb',
                      borderWidth: 1,
                      borderRadius: 6,
                    }}>
                    <Picker
                      selectedValue={formik.values.proficiency}
                      onValueChange={(itemValue, itemIndex) => {
                        if (isEdit == true) {
                          return;
                        }
                        formik.setFieldValue('proficiency', itemValue);
                      }}>
                      <Picker.Item
                        label="Select the Proficiency level"
                        value=""
                      />
                      <Picker.Item label="Beginner" value="beginner" />
                      <Picker.Item label="Intermediate" value="intermediate" />
                      <Picker.Item label="Advance" value="advance" />
                    </Picker>
                  </View>
                  {formik.touched.proficiency && formik.errors.proficiency && (
                    <Text style={styles.error}>
                      {formik.errors.proficiency}
                    </Text>
                  )}
                </View>

                {/* ameneties */}
                <View style={styles.boxContainer}>
                  <Text style={styles.label}>Ameneties </Text>
                  {/* <TextInput
                    style={styles.multiInput}
                    multiline
                    numberOfLines={5}
                    onChangeText={formik.handleChange('amenitiesProvided')}
                    value={formik?.values?.amenitiesProvided}
                  /> */}

                  <View
                    style={{
                      borderWidth: 1,
                      borderRadius: 0,
                      borderColor: '#ccc',
                      height: 175,
                    }}>
                    <QuillEditor
                      value={formik?.values?.amenitiesProvided}
                      autoSize
                      ref={_editorAmenities}
                      quill={{theme: 'snow', placeholder: 'write here '}}
                      webview={{
                        scrollEnabled: true,
                        style: {
                          borderWidth: 1,
                          borderRadius: 6,
                          borderColor: '#ccc',
                        },
                        nestedScrollEnabled: true,
                        showsHorizontalScrollIndicator: true,
                        showsVerticalScrollIndicator: true,
                      }}
                      onHtmlChange={html => {
                        console.log('from html change amenities', html),
                          formik.setFieldValue('amenitiesProvided', html.html);
                      }}
                      initialHtml={formik?.values?.amenitiesProvided}
                    />
                  </View>

                  {formik.touched.amenitiesProvided &&
                    formik.errors.amenitiesProvided && (
                      <Text style={styles.error}>
                        {formik.errors.amenitiesProvided}
                      </Text>
                    )}
                </View>
                {/* things you have to carry */}

                <View style={styles.boxContainer}>
                  <Text style={styles.label}>Things have to Carry with </Text>
                  {/* <TextInput
                    style={styles.multiInput}
                    multiline
                    numberOfLines={5}
                    onChangeText={formik.handleChange('whatYouHaveToBring')}
                    value={formik?.values?.whatYouHaveToBring}
                  /> */}
                  <View
                    style={{
                      borderWidth: 1,
                      borderRadius: 0,
                      borderColor: '#ccc',
                      height: 175,
                    }}>
                    <QuillEditor
                      value={formik?.values?.whatYouHaveToBring}
                      autoSize
                      ref={_editorwhatYouHaveToBring}
                      quill={{theme: 'snow', placeholder: 'write here '}}
                      webview={{
                        scrollEnabled: true,
                        style: {
                          borderWidth: 1,
                          borderRadius: 6,
                          borderColor: '#ccc',
                        },
                        nestedScrollEnabled: true,
                        showsHorizontalScrollIndicator: true,
                        showsVerticalScrollIndicator: true,
                      }}
                      onHtmlChange={e => {
                        if (isEdit == true) {
                          return;
                        }
                        console.log('from html change what you ', e),
                          formik.setFieldValue('whatYouHaveToBring', e);
                      }}
                      initialHtml={formik?.values?.whatYouHaveToBring}
                    />
                  </View>

                  {formik.touched.whatYouHaveToBring &&
                    formik.errors.whatYouHaveToBring && (
                      <Text style={styles.error}>
                        {formik.errors.whatYouHaveToBring}
                      </Text>
                    )}
                </View>

                {/* cancellation policy */}
                <View style={styles.boxContainer}>
                  <Text style={styles.label}>Cancellation Policy</Text>
                  {/* <TextInput
                    style={styles.multiInput}
                    multiline
                    numberOfLines={5}
                    onChangeText={formik.handleChange('cancellationPolicy')}
                    value={formik?.values?.cancellationPolicy}
                  /> */}
                  <View
                    style={{
                      borderWidth: 1,
                      borderRadius: 0,
                      borderColor: '#ccc',
                      height: 175,
                    }}>
                    <QuillEditor
                      value={formik?.values?.cancellationPolicy}
                      autoSize
                      ref={_editorCancellationPolicy}
                      quill={{theme: 'snow', placeholder: 'write here '}}
                      webview={{
                        scrollEnabled: true,
                        style: {
                          borderWidth: 1,
                          borderRadius: 6,
                          borderColor: '#ccc',
                        },
                        nestedScrollEnabled: true,
                        showsHorizontalScrollIndicator: true,
                        showsVerticalScrollIndicator: true,
                      }}
                      onHtmlChange={e => {
                        if (isEdit == true) {
                          return;
                        }
                        console.log('from html change cancel', e.html),
                          formik.setFieldValue('cancellationPolicy', e.html);
                      }}
                      initialHtml={formik?.values?.cancellationPolicy}
                    />
                  </View>

                  {formik.touched.cancellationPolicy &&
                    formik.errors.cancellationPolicy && (
                      <Text style={styles.error}>
                        {formik.errors.cancellationPolicy}
                      </Text>
                    )}
                </View>

                {isEdit ? (
                  <></>
                ) : (
                  <>
                    <View style={{alignSelf: 'flex-end'}}>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-around',
                          marginVertical: 10,
                          marginHorizontal: 20,
                        }}>
                        <TouchableOpacity onPress={handleReset}>
                          <Text
                            style={{
                              borderWidth: 1,
                              borderColor: 'grey',
                              padding: 10,
                              marginRight: 10,
                              fontWeight: '700',
                              color: 'black',
                              borderRadius: 10,
                            }}>
                            Cancel
                          </Text>
                        </TouchableOpacity>

                        <TouchableOpacity onPress={formik.handleSubmit}>
                          <Text
                            style={{
                              borderWidth: 1,
                              borderColor: 'white',
                              padding: 10,
                              fontWeight: '700',
                              color: 'white',
                              borderRadius: 10,
                              backgroundColor: 'lightskyblue',
                            }}>
                            Save
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </>
                )}
              </View>
            </ScrollView>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

export default Create1;

const styles = StyleSheet.create({
  loadingContainer: {
    height: '100%',

    justifyContent: 'center',
    alignItems: 'center',
  },
  boxContainer: {
    marginTop: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  input: {
    marginTop: 4,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#ccc',
    color: '#333',
    fontSize: 14,
  },
  error: {
    fontSize: 12,
    color: 'red',
  },

  feesinput: {
    borderWidth: 1,
    borderColor: '#e5e7eb',
    paddingHorizontal: 12,
    marginHorizontal: '1%',
    //marginVertical: '5%',
    height: 40,
    flex: 1,
    //width:80,
    borderRadius: 6,
  },
  dayContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 5,
  },

  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    //marginTop: '2%',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 6,
    paddingVertical: '1%',
    paddingHorizontal: '6%',
  },
  textInput: {
    flex: 1,
    height: 50,
  },
  multiInput: {
    marginTop: 4,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#ccc',
    color: '#333',
    fontSize: 14,

    //height: 80,
  },

  header: {
    backgroundColor: '#f2f2f2',
  },
  title: {
    fontWeight: 'bold',
    paddingHorizontal: 12,
  },
  cell: {
    paddingHorizontal: 11,
  },
});