import { FlatList, StyleSheet, Text, TouchableOpacity, View ,ActivityIndicator, ScrollView, } from 'react-native'
import React, { useEffect, useState } from 'react'
import {Badge,DataTable} from 'react-native-paper';
import moment from 'moment';
import { Image } from 'react-native';
import axios from 'axios';
import {API} from '@env';
import { useRef } from "react";
import { useAuth } from '../../components/Auth/AuthContext';
import { useNavigation } from '@react-navigation/native';
import { TextInput } from 'react-native';
import { Picker } from '@react-native-picker/picker';
import Icon from 'react-native-vector-icons/FontAwesome'; 

const Listing = () => {
const [courseDetails,setCourseDetails] = useState([])
const [page, setPage] = useState(1);
const [loading, setLoading] = useState(false);
const navigation = useNavigation();
const {user, setPageName} = useAuth();
const [classType, setClassType] = useState('');
const [status, setStatus] = useState('');
const [searchQuery, setSearchQuery] = useState('');

 useEffect(()=>{
     getCoachDetails() 
     setClassType('')
     setStatus('')
     setSearchQuery('')
 },[])
 
 
    const getCoachDetails = async() => {
    try{
    setLoading(true)
      const result = await axios.get(`${API}/api/course/coach/${user?.data?._id}`)
       //const result = await axios.get(`${API}/api/course/coach/${user?.data?._id}?page=${page}`);
      //console.log("resultttt dataa",result?.data?.data ) 
      setCourseDetails(result?.data?.data)
      //setCourseDetails((prevData) =>[...prevData, result?.data?.data])
      setPage(prevPage => prevPage + 1);
    }
    catch(error){
      console.log("error",error)
    }
    finally{
      setLoading(false)
    }
    }

    const handleLoadMore = () => {
      if(!loading){
        getCoachDetails()
      }
    }

  const ListEndLoader = () => {
    if(loading){
      return <ActivityIndicator size={"large"} />
    }
   
  }  
   
    const renderItem = ({item,index}) => {

      var startDateMoment = moment(item?.dates?.startDate);
      var formattedDateStart = startDateMoment.format('ddd, MM-DD-YYYY');
      var endDateMoment = moment(item?.dates?.endDate);
      var formattedDateEnd = endDateMoment.format('ddd, MM-DD-YYYY');
     
        return(
          <ScrollView>
            <TouchableOpacity 
            onPress={() => {
              //navigation.navigate('ListingDetails',{listing:item})
              setPageName('Create Course');
              navigation.push('CourseCreate', { source: 'course-listing', listing: item });
            }}
            key={item.id}
            >
    <View style={{backgroundColor:'white',margin:10,borderRadius:20,elevation:2}} 
//  style={{flex:1,alignSelf:'center'}}
  key={item.id}
    >
    
        <View style={styles.cardContainer} >
          {/* image */}
          <View style={styles.imageContainer}>
            <TouchableOpacity
              style={{borderRadius:5}}
              onPress={() => {
                console.log('image clicked');
              }}>

              <Image 
               source={{uri:item?.images[0]?.url}} 
              //source = {{uri:"data:image/jpeg;base64,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"}}
              style={styles.image2} />
            </TouchableOpacity>
  
            <View style={{position:'absolute',alignSelf:'flex-end',top:8,right:1}}>
              {console.log(item?.status,"status here")}

{/* <Badge theme={{ colors : { primary : "green"}}} children={item?.status === "active" ? "Active" : 'Inactive'} visible={true} /> */}

    <Text style={[{flexShrink:1,marginRight:1,color:'black',fontSize:10,borderWidth:0,padding:3,borderRadius:5},
  {backgroundColor: item?.status === "active" ? "green" : 'red'},
  {color: item?.status === "active" ? "green" : 'red'}
  ]}>{item?.status === "active" ? "Active" : 'Inactive'}</Text>
    </View>
    
          </View>

          {/* course data */}
          {/* 1 */}
<View style={{marginTop:10,flexDirection:'',justifyContent:'flex-start',}}>
{/* 1 */}
<View style={{flexDirection:'row',alignItems:'center'}}>
<Text style={{flexShrink:1,marginRight:1,color:'black',fontSize:18}} >Course Name-</Text>
<Text style={{flexShrink:1,marginRight:1,color:'blue',fontSize:16}}>{item?.courseName}</Text>
</View>

{/* 2 */}
<View style={{flexDirection:'row',alignItems:'center'}}>
<Text style={{flexShrink:1,marginRight:1,color:'black',fontSize:16}}>Class Type- </Text>
<Text style={{flexShrink:1,marginRight:1,color:'black',fontSize:16}}>{item?.classType}</Text>
</View>
</View>

{/* 2 */}
<View style={{marginTop:1,flexDirection:'',justifyContent:'flex-start'}}>
{/* 1 */}
<View style={{flexDirection:'row',alignItems:'center'}}>
<Text style={{flexShrink:1,marginRight:1,color:'black',fontSize:16}} >Start Date-</Text>
<Text style={{flexShrink:1,marginRight:1,color:'black',fontSize:16}}>{formattedDateStart}</Text>
</View>

{/* 2 */}
<View style={{flexDirection:'row',alignItems:'center'}}>
<Text style={{flexShrink:1,marginRight:1,color:'black',fontSize:16}}>End Date- </Text>
<Text style={{flexShrink:1,marginRight:1,color:'black',fontSize:16}}>{formattedDateEnd}</Text>
</View>
</View>

{/* 3 */}
<View style={{marginTop:1,flexDirection:'',justifyContent:'flex-start'}}>
{/* 1 */}
<View style={{flexDirection:'row',alignItems:'center'}}>
<Text style={{flexShrink:1,marginRight:1,color:'black',fontSize:16}}>Facility Name-</Text>
<Text style={{flexShrink:1,marginRight:1,color:'black',fontSize:16}}>{item?.facility?.name}</Text>
</View>

{/* 2 */}
<View style={{flexDirection:'row',alignItems:'center'}}>
{/* <Text style={{flexShrink:1,marginRight:1,color:'black',fontSize:16}}>Status- </Text>
<Text style={[{flexShrink:1,marginRight:1,color:'black',fontSize:16,borderWidth:1,padding:3,borderRadius:5},
{borderColor: item?.status === "active" ? "green" : 'red'},
{color: item?.status === "active" ? "green" : 'red'}
]}>{item?.status === "active" ? "Active" : 'Inactive'}</Text> */}
</View>
</View>

</View> 

</View>
</TouchableOpacity>
</ScrollView>
    )}

const handleSearchFilter = async() => {
  try{
console.log("shdakjshkjsf",searchQuery,classType,status)
let queryString = "";
  if (searchQuery) {
    console.log("first",searchQuery)
     queryString =  `q=${searchQuery}`;
  }
  if (classType) {
    queryString += `${queryString ? "&" : ""}classType=${classType}`;
  }
  if (status) {
    queryString += `${queryString ? "&" : ""}status=${status}`;
  }
  console.log("after query is made", queryString)
  console.log( `${API}/api/course/filter?coachEmail=${user?.data?.email}&${
    queryString ? `${queryString}` : ""}`)
  const response = await axios.get(
    `${API}/api/course/filter?coachEmail=${user?.data?.email}&${
      queryString ? `${queryString}` : ""}`,
    {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${user?.data?.token}`,
      },
    }
  );
   //console.log(response.data, "pppppp");
   const finalResult = await response?.data
   setCourseDetails([])
   setCourseDetails(finalResult)
   setClassType('')
     setStatus('')
     setSearchQuery('')
  }
  catch(error){
    console.error("error", error);
    setClassType('')
     setStatus('')
     setSearchQuery('')
  }

}

    const seachFilterComp = () => {
      return (
    
        <View style={styles.container}>
          <View style={styles.search}>
        <TextInput               placeholderTextColor="#000"

        keyboardType="default"
          style={styles.input}
          placeholder="Search..."
          onChangeText={text => setSearchQuery(text)}
          value={searchQuery}
        />
              <TouchableOpacity
                onPress={handleSearchFilter}
                // style={styles.calendarIcon}
                >
                <Icon name="search" size={20} color="black" />
              </TouchableOpacity>
            </View>
           
        <View style={styles.dropdownContainer}>
          <Picker
            selectedValue={classType}
            onValueChange={(itemValue, itemIndex) => setClassType(itemValue)}
            style={styles.dropdown}
            placeholder='Class Type'
          >
            <Picker.Item label="Select Class Type" value="" />
            <Picker.Item label="Class" value="class" />
            <Picker.Item label="Course" value="course" />
          </Picker>

          <Picker
            selectedValue={status}
            onValueChange={(itemValue, itemIndex) => setStatus(itemValue)}
            style={styles.dropdown}
          >
          <Picker.Item label="Select Status" value="" />
            <Picker.Item label="Archive" value="inactive" />
            <Picker.Item label="Active" value="active" />
          </Picker>
        </View>
      </View>
      )
    }  

  return (
  //   <ScrollView 
  //   showsVerticalScrollIndicator={false}
  //       showsHorizontalScrollIndicator={false}
  // >

  <>
  <View style={{backgroundColor:'white'}}>{seachFilterComp()}</View> 
    <View style={{backgroundColor:'white',flex:1,paddingBottom:20,}}>
  
      {/* <Text style={{fontSize:18,alignSelf:'center',color:'black',marginVertical:5,textDecorationLine:'underline'}}>All Courses Listing</Text> */}
     
      <View style={{paddingVertical:10}}>       
       <FlatList 
       data={courseDetails}
       keyExtractor={(item, index) => index.toString()}
       renderItem={renderItem}
      //  onEndReached={handleLoadMore}
      //  onEndReachedThreshold={0.8}
      //  ListFooterComponent={ListEndLoader}
       />
</View>


    </View>
    </>
    // </ScrollView>
  )
}

export default Listing

const styles = StyleSheet.create({

    cardContainer: {
        // width: '100%', // Adjust the card width as needed
         height: 380,
        margin: 5,
        borderWidth: 0,
        borderColor: 'blue',  
        overflow: 'hidden',
        padding:5,
       
      },
      imageContainer: {

        paddingTop:6,      
         width: '100%',
         height: 200, // Allows the image to adjust its height based on the aspect ratio
        overflow: 'hidden',
        borderWidth: 0,
        borderColor: 'blue',
      },
      image2: {
        width: '100%',
        height: '100%', 
        resizeMode: 'cover',
        borderRadius: 10,
        
      },

          // search comp
    container: {
      //flexDirection: 'row',
      //alignItems: 'center',
      // paddingHorizontal: 10,
      // marginTop: 5,
    },
    search:{
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: '3%',
      borderWidth: 1,
      borderColor: '#e5e7eb',
      borderRadius: 10,
      paddingVertical: '1%',
      paddingHorizontal: '7%',
      marginHorizontal:10,
    },
    input: {
      
      height: 40,
      width:'95%',
      //borderWidth: 1,
      borderColor: '#ccc',
      borderRadius: 5,
      paddingHorizontal: 10,
      alignSelf:'center'
      //  marginRight: 10,
    },
    dropdownContainer: {
      flexDirection: 'row',
      alignItems: 'center',   
    },
    dropdown: {
      height: 40,
      flex:1,
    },

})