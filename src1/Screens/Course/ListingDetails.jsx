import { ScrollView, StyleSheet, Text, View } from 'react-native'
import React, { useEffect, useState } from 'react';
import { DataTable } from 'react-native-paper'; 
import { useRoute } from '@react-navigation/native';
import axios from 'axios';
import {API} from '@env';
import moment from 'moment';
import { Image } from 'react-native';

const ListingDetails = () => {
const route = useRoute()
const { listing } = route.params
console.log("course listing",listing)

const formatTimeDetails = (time) => {
    //console.log("timeeeee",time)
    const[hours,minutes] = time.split(':')
    let hours12 = parseInt(hours, 10) % 12;
   hours12 = hours12 === 0 ? 12 : hours12;
 
   // Determine whether it's AM or PM
   const period = parseInt(hours, 10) < 12 ? 'AM' : 'PM';
 
   // Return the time in 12-hour format
   return `${hours12}:${minutes} ${period}`;
 }


var startDateMoment = moment(listing?.dates[0]?.startDate);
var formattedDateStart = startDateMoment.format('ddd, MM-DD-YYYY');
var endDateMoment = moment(listing?.dates[0]?.endDate);
var formattedDateEnd = endDateMoment.format('ddd, MM-DD-YYYY');


return (
    <ScrollView>
    <View style={styles.container}>
    <View>
    <Text style={{fontWeight:'bold',fontSize:18,color:'black',paddingVertical:5,fontFamily:'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',}}>Course Listing Details</Text>
     <View style={{height:200,width:'100%',borderRadius:10}}>  
     <Image
     souce={{uri:listing?.images[0].url}}
    
     style={{height:'100%',width:'100%',resizeMode:'cover',borderRadius:2}}
     />
     </View> 
        <DataTable>
          <DataTable.Header>
            <DataTable.Title style={styles.headerCell}>
                {/* <Text style={{fontWeight:'bold',fontSize:18,color:'black'}}>Booking Details</Text> */}
                </DataTable.Title>
          </DataTable.Header>
    
          <DataTable.Row>
            <DataTable.Cell style={styles.cell}>Course Name</DataTable.Cell>
            <DataTable.Cell style={styles.cell}>{listing?.courseName}</DataTable.Cell>
          </DataTable.Row>
    
          <DataTable.Row>
            <DataTable.Cell style={styles.cell}>Start Date</DataTable.Cell>
            <DataTable.Cell style={styles.cell}>{formattedDateStart}</DataTable.Cell>
          </DataTable.Row>
    
          <DataTable.Row>
            <DataTable.Cell style={styles.cell}>End Date</DataTable.Cell>
            <DataTable.Cell style={styles.cell}>{formattedDateEnd}</DataTable.Cell>
          </DataTable.Row>
    
          <DataTable.Row>
            <DataTable.Cell style={styles.cell}>Proficiency</DataTable.Cell>
            <DataTable.Cell style={styles.cell}>{listing?.proficiency}</DataTable.Cell>
          </DataTable.Row>
    
          <DataTable.Row>
            <DataTable.Cell style={styles.cell}>Session Type</DataTable.Cell>
            <DataTable.Cell style={styles.cell}>{listing?.sessionType}</DataTable.Cell>
          </DataTable.Row>

          <DataTable.Row>
            <DataTable.Cell style={styles.cell}>Class Type</DataTable.Cell>
            <DataTable.Cell style={styles.cell}>{listing?.classType}</DataTable.Cell>
          </DataTable.Row>

          <DataTable.Row>
            <DataTable.Cell style={styles.cell}>Category</DataTable.Cell>
            <DataTable.Cell style={styles.cell}>{listing?.category}</DataTable.Cell>
          </DataTable.Row>
    
          <DataTable.Row>
            <DataTable.Cell style={styles.cell}>Start Time</DataTable.Cell>
            <DataTable.Cell style={styles.cell}>{formatTimeDetails(listing?.dates?.startTime)}</DataTable.Cell>
          </DataTable.Row>
          
          <DataTable.Row>
            <DataTable.Cell style={styles.cell}>End Time</DataTable.Cell>
            <DataTable.Cell style={styles.cell}>{formatTimeDetails(listing?.dates?.endTime)}</DataTable.Cell>
          </DataTable.Row>
    
          <DataTable.Row>
            <DataTable.Cell style={styles.cell}>Days included</DataTable.Cell>
            <DataTable.Cell style={styles.cell}>
                {listing?.dates?.days.map((item,index)=>(
                    <Text key={index}>{`${item}, `}</Text>
                ))}
                                                
                </DataTable.Cell>
          </DataTable.Row>   
        </DataTable>
    
        </View>
      </View>
      </ScrollView>
      )
}

export default ListingDetails

const styles = StyleSheet.create({

    container: {
         flex: 1,
        padding: 10,
        backgroundColor: '#fff',
        
      },
      headerCell: {
        fontWeight: 'bold',
        justifyContent: 'left',
        alignItems: 'left',
        fontFamily:'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
        // marginBottom:1,
      },
      cell: {
        justifyContent: 'left',
        alignItems: 'left',
        marginVertical:5,
        fontSize:18,
        fontFamily:'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
      },


})