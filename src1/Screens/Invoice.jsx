import React, {useEffect, useState, useRef} from 'react';
import {
  Text,
  View,
  ScrollView,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  PermissionsAndroid,
  Alert,
  Modal,
  Platform,
  SafeAreaView,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useNavigation} from '@react-navigation/native';
import {useRoute} from '@react-navigation/native';
import axios from 'axios';
import {useAuth} from '../components/Auth/AuthContext';
import RNHTMLtoPDF from 'react-native-html-to-pdf';
import RNFS from 'react-native-fs';
import * as ScopedStorage from 'react-native-scoped-storage';
import {convertDateIntoIndianFormat, numberToWords} from '../helpers';
import Share from 'react-native-share';
const Invoice = ({singleReport, open, setOpen, singleReportIndex}) => {
  const route = useRoute();
  const [isLoading, setIsLoading] = useState(true);
  const [thankyouDetails, setThankyouDetails] = useState();
  const {user, setPageName} = useAuth();

  const coach = user.data;

  const requestStoragePermission = async () => {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        {
          title: 'Storage Permission Needed',
          message: 'This app needs the storage permission to save PDF files',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        },
      );
      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        console.log('You can use the storage');
      } else {
        console.log('Storage permission denied');
      }
    } catch (err) {
      console.warn(err);
    }
  };

  useEffect(() => {
    requestStoragePermission();
  }, []);

  const handlePrint = async () => {
    await requestStoragePermission();
    const htmlContent = `
      <html>
        <head>
          <style>
            .container {
              width: 100%;
              margin: 0 auto;
              padding: 20px;
              font-family: Arial, sans-serif;
              font-size: 10px;
            }
            .header {
              font-size: 16px;
              display: flex;
            }
            .headerHighlight {
              color: #fda4af;
              margin-left: 8px;
            }
            .infoSection, .descriptionSection, .amountSection, .paymentDetails, .regardsSection {
              margin-top: 20px;
            }
            .infoTextBold {
              font-weight: bold;
            }
            .infoText {
              margin-top: 4px;
            }
            .descriptionHeader, .descriptionRow {
              display: flex;
              justify-content: space-between;
            }
            .infoTextLight {
              font-weight: 300;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              ${coach?.firstName} <span class="headerHighlight">${
      coach?.lastName
    }</span>
            </div>
            <div class="infoSection">
              <div>
                <p class="infoTextBold">To,</p>
                <p class="infoTextBold">Umn Khel Shiksha Private Limited</p>
                <p class="infoText">Vasant Vihar</p>
                <p class="infoText">Basant Lok Complex, Road 21</p>
                <p class="infoText">New Delhi-110057</p>
                <p class="infoText">GST ID: 07AADCU2822L1Z8</p>
              </div>
              <div class="invoiceDetails">
                <div class="infoRow">
                  <p class="infoTextBold">Date:</p>
                  <p class="infoText">${convertDateIntoIndianFormat(
                    new Date(singleReport?.date.split('T')[0]),
                  )}</p>
                </div>
                <div class="infoRow">
                  <p class="infoTextBold">Invoice No.</p>
                  <p class="infoText">${singleReport?.bookingId}_${singleReportIndex + 1}</p>
                </div>
              </div>
            </div>
            <div class="descriptionSection">
              <div class="descriptionHeader">
                <p class="infoTextLight">DESCRIPTION</p>
                <p class="infoTextLight">AMOUNT</p>
              </div>
              <div class="descriptionRow">
                <p class="infoText">${singleReport?.courseName}</p>
                <p class="infoText">₹${singleReport?.classFees.toFixed(2)}</p>
              </div>
              <div class="descriptionRow">
                <p class="infoText">TDS Deduction(10%)</p>
                <p class="infoText">₹${singleReport?.tds.toFixed(2)}</p>
              </div>
              <div class="descriptionRow">
                <p class="infoTextLight">Total:</p>
                <p class="infoTextLight">₹${singleReport?.amountReceived.toFixed(
                  2,
                )}</p>
              </div>
            </div>
            <div class="amountSection">
              <p class="infoText">Amount (in words): Rupees ${numberToWords(
                singleReport?.amountReceived.toFixed(2),
              )} only.</p>
            </div>
            <div class="paymentDetails">
              <p class="infoTextBold">Payment Details:</p>
              <p class="infoText">Pan card no - ${
                coach?.kycDocuments?.documentNumber
              }</p>
              <p class="infoText">Beneficiary name - ${
                coach?.bankDetails?.accountHolderName
              },</p>
              <p class="infoText">Account no - ${
                coach?.bankDetails?.accountNumber
              },</p>
              <p class="infoText">IFSC code - ${coach?.bankDetails?.ifsc}</p>
            </div>
            <div class="regardsSection">
              <p class="infoText">Regards,</p>
              <p class="infoText">${coach?.firstName} ${coach?.lastName}</p>
            </div>
          </div>
        </body>
      </html>
    `;

    if (Platform.OS === 'android') {
      try {
          const options = {
              html: htmlContent,
              fileName: 'invoice',
              base64: true,
          };

          const file = await RNHTMLtoPDF.convert(options);
          let dir = await ScopedStorage.openDocumentTree(true);
          const timestamp = new Date().getTime();
          const fileName = `invoice_${timestamp}.pdf`;
          const mimeType = "application/pdf";
          const encoding = "base64";
          await ScopedStorage.writeFile(dir.uri, file.base64, fileName, mimeType, encoding);
          await AsyncStorage.setItem('userMediaDirectory', JSON.stringify(dir));
          Alert.alert('PDF Created', `PDF file has been created and saved in the selected directory.`);
      } catch (error) {
          console.error(error);
          Alert.alert('Error', 'An error occurred while creating the PDF.');
      }
  } else if (Platform.OS === 'ios') {
    try {
      const options = {
        html: htmlContent,
        fileName: 'invoice',
        base64: true,
      };

      const file = await RNHTMLtoPDF.convert(options);
      const path = `${RNFS.DocumentDirectoryPath}/invoice_${new Date().getTime()}.pdf`;
      await RNFS.writeFile(path, file.base64, 'base64');

      const shareOptions = {
        title: 'Share Invoice',
        url: `file://${path}`,
        type: 'application/pdf',
      };

      await Share.open(shareOptions);
      Alert.alert('PDF Created', `PDF file has been created and ready for sharing.`);
    } catch (error) {
      console.error(error);
      // Alert.alert('Error', 'An error occurred while creating the PDF.');
    }
  } else {
    console.log("Unsupported platform");
  }
  };

  return (
    <Modal
      visible={open}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setOpen(!open)}>
      <View style={styles.overlay}>
        <SafeAreaView style={{flex:1}}>
        <View style={styles.container}>
          <View style={styles.modalContent}>
            <ScrollView
              contentContainerStyle={styles.invoiceContent}
              //   ref={invoiceRef}
            >
              <Text style={styles.header}>
                {coach?.firstName}{' '}
                <Text style={styles.headerHighlight}>{coach?.lastName}</Text>
              </Text>

              <View style={styles.infoSection}>
                <View>
                  <Text style={styles.infoTextBold}>To,</Text>
                  <Text style={styles.infoTextBold}>
                    Umn Khel Shiksha Private Limited
                  </Text>
                  <Text style={styles.infoText}>Vasant Vihar</Text>
                  <Text style={styles.infoText}>
                    Basant Lok Complex, Road 21
                  </Text>
                  <Text style={styles.infoText}>New Delhi-110057</Text>
                  <Text style={styles.infoText}>GST ID: 07AADCU2822L1Z8</Text>
                </View>
                <View style={styles.invoiceDetails}>
                  <View style={styles.infoRow}>
                    <Text style={styles.infoTextBold}>Date:</Text>
                    <Text style={styles.infoText}>
                      {convertDateIntoIndianFormat(
                        new Date(singleReport?.date.split('T')[0]),
                      )}
                    </Text>
                  </View>
                  <View style={styles.infoRow}>
                    <Text style={styles.infoTextBold}>Invoice No.</Text>
                    <Text style={styles.infoText}>{singleReport?.bookingId}_{singleReportIndex+1}</Text>
                  </View>
                </View>
              </View>

              <View style={styles.descriptionSection}>
                <View style={styles.descriptionHeader}>
                  <Text style={styles.infoTextLight}>DESCRIPTION</Text>
                  <Text style={styles.infoTextLight}>AMOUNT</Text>
                </View>
                <View style={styles.descriptionRow}>
                  <Text style={styles.infoText}>
                    {singleReport?.courseName}
                  </Text>
                  <Text style={styles.infoText}>
                    ₹{singleReport?.classFees.toFixed(2)}
                  </Text>
                </View>
                <View style={styles.descriptionRow}>
                  <Text style={styles.infoText}>TDS Deduction(10%)</Text>
                  <Text style={styles.infoText}>
                    ₹{singleReport?.tds.toFixed(2)}
                  </Text>
                </View>
                <View style={styles.descriptionRow}>
                  <Text style={styles.infoTextLight}>Total:</Text>
                  <Text style={styles.infoTextLight}>
                    ₹{singleReport?.amountReceived.toFixed(2)}
                  </Text>
                </View>
              </View>

              <View style={styles.amountSection}>
                <Text style={styles.infoText}>
                  Amount (in words): Rupees{' '}
                  {numberToWords(singleReport?.amountReceived.toFixed(2))} only.
                </Text>
              </View>

              <View style={styles.paymentDetails}>
                <Text style={styles.infoTextBold}>Payment Details:</Text>
                <Text style={styles.infoText}>
                  Pan card no - {coach?.kycDocuments?.documentNumber}
                </Text>
                <Text style={styles.infoText}>
                  Beneficiary name - {coach?.bankDetails?.accountHolderName},
                </Text>
                <Text style={styles.infoText}>
                  Account no - {coach?.bankDetails?.accountNumber},
                </Text>
                <Text style={styles.infoText}>
                  IFSC code - {coach?.bankDetails?.ifsc}
                </Text>
              </View>

              <View style={styles.regardsSection}>
                <Text style={styles.infoText}>Regards,</Text>
                <Text style={styles.infoText}>
                  {coach?.firstName} {coach?.lastName}
                </Text>
              </View>
            </ScrollView>
          </View>

          <TouchableOpacity onPress={handlePrint} style={styles.printButton}>
            <Text style={styles.printButtonText}>Print invoice →</Text>
          </TouchableOpacity>

          <TouchableOpacity onPress={handlePrint} style={styles.printButton}>
            <Text style={styles.printButtonText}>Print invoice →</Text>
          </TouchableOpacity>
        </View>
        </SafeAreaView>
      </View>
    </Modal>
  );
};

export default Invoice;

const styles = {
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(107, 114, 128, 0.75)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    width: '90%',
    backgroundColor: 'white',
    borderRadius: 8,
    overflow: 'hidden',
    padding: 20,
    alignItems: 'center',
  },
  modalContent: {
    alignItems: 'center',
  },
  printButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    padding: 10,
  },
  printButtonText: {
    color: '#4f46e5',
    fontSize: 10,
  },
  invoiceContent: {
    width: '100%',
    marginTop: 20,
  },
  header: {
    fontSize: 10,
    flexDirection: 'row',
  },
  headerHighlight: {
    color: '#fda4af',
    marginLeft: 8,
  },
  infoSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  infoTextBold: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  infoText: {
    fontSize: 10,
  },
  invoiceDetails: {
    flexDirection: 'column',
    marginLeft: 11,
  },
  infoRow: {
    flexDirection: 'row',
    marginTop: 10,
  },
  descriptionSection: {
    marginTop: 20,
  },
  descriptionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  descriptionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  infoTextLight: {
    fontSize: 10,
    fontWeight: '300',
  },
  amountSection: {
    marginTop: 20,
  },
  paymentDetails: {
    marginTop: 20,
  },
  regardsSection: {
    marginTop: 20,
  },
};
