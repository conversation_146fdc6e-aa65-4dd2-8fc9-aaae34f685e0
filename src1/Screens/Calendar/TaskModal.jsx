import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import {RadioButton, configureFonts} from 'react-native-paper';
import {Formik, useFormik} from 'formik';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import DatePicker from 'react-native-modern-datepicker';
import Icon from 'react-native-vector-icons/FontAwesome';

import BouncyCheckbox from 'react-native-bouncy-checkbox';
// AsyncStorage is not needed anymore as we're using the user context
import {Button} from 'react-native';
import * as Yup from 'yup';
import {API} from '@env';
import axios from 'axios';
import {useAuth} from '../../components/Auth/AuthContext';
import moment from 'moment-timezone';

const TaskModal = ({setTaskCreated, isVisible, closeModal}) => {
  const [open, setOpen] = useState(false);
  const [openEndDate, setOpenEndDate] = useState(false);
  const [isEndDate, setIsEndDate] = useState('never');
  const [checkedDays, setCheckedDays] = useState([]);
  const [isStartTimePickerVisible, setStartTimePickerVisibility] =
    useState(false);
  const [isEndTimePickerVisible, setEndTimePickerVisibility] = useState(false);
  const [processing, setProcessing] = useState(false);

  const {user} = useAuth();

  const toggleDay = day => {
    let updatedDays;
    // Check if the day is already in the array
    if (checkedDays.includes(day)) {
      // Remove the day if it's already checked
      updatedDays = checkedDays.filter(item => item !== day);
      setCheckedDays(updatedDays);
    } else {
      // Add the day if it's not already checked
      updatedDays = [...checkedDays, day];
      setCheckedDays(updatedDays);
    }
    // Update the Formik field with the updated days
    formik.setFieldValue('days', updatedDays);
  };

  const showDatePicker = () => {
    setOpen(!open);
  };

  const showEndDatePicker = () => {
    setOpenEndDate(!openEndDate);
  };

  function handleStartDateChange(propDate) {
    let currentDateObject = new Date();
    let currentDate = currentDateObject.toISOString().split('T')[0];
    let date = replaceSlashWithDash(propDate);
    try {
      if (date >= currentDate) {
        formik.setFieldValue('startDate', date);
        formik.setFieldValue('endDate', '');
        formik.setFieldValue('fromTime', '');
        formik.setFieldValue('toTime', '');
        setOpen(false);
      } else {
        alert('Start date cannot be in the past ');
        setOpen(false);
      }
    } catch (error) {
      // Handle validation errors
      console.log(error, 'erorrrrrrr');
      formik.setFieldError('startDate', error.message);
      setOpen(false);
    }
  }

  function handleEndDateChange(propDate) {
    let date = replaceSlashWithDash(propDate);
    try {
      if (formik.values.startDate !== '' && date >= formik.values.startDate) {
        formik.setFieldValue('endDate', date);
        // formik.setFieldValue('fromTime', '');
        // formik.setFieldValue('toTime', '');
        setOpenEndDate(false);
      } else {
        alert('End date cannot be before start Date');
        setOpenEndDate(false);
      }
    } catch (error) {
      // Handle validation errors
      console.log(error, 'erorrrrrrr');
      formik.setFieldError('endDate', error.message);
      setOpenEndDate(false);
    }
  }
  function replaceSlashWithDash(dateString) {
    return dateString.replace(/\//g, '-');
  }
  const formatTime = time => {
    // Convert the given date string to a Date object
    const givenTime = new Date(time);

    const givenHours = givenTime.getHours();
    const givenMinutes = givenTime.getMinutes();

    const finalTime = `${givenHours}:${givenMinutes}`;

    // Format the time as needed (e.g., to a string with leading zeros)
    const formattedTime = `${givenHours
      .toString()
      .padStart(2, '0')}:${givenMinutes.toString().padStart(2, '0')}`;

    return formattedTime;
  };
  const isTimeValid = dateTimeString => {
    // Get the current time
    const currentTime = new Date();

    let currentDate = currentTime.toISOString().split('T')[0];

    // Extract the time components (hours and minutes) from the current time
    const currentHours = currentTime.getHours();
    const currentMinutes = currentTime.getMinutes();

    // Convert the given date string to a Date object
    const givenTime = new Date(dateTimeString);
    // Extract the time components (hours and minutes) from the given date
    const givenHours = givenTime.getHours();
    const givenMinutes = givenTime.getMinutes();

    const finalTime = `${givenHours}:${givenMinutes}`;

    if (formik.values.startDate === currentDate) {
      // Compare the time components
      if (
        givenHours > currentHours ||
        (givenHours === currentHours && givenMinutes >= currentMinutes)
      ) {
        return true; // Given time is greater than or equal to current time
      } else {
        return false; // Given time is less than current time
      }
    } else {
      return true;
    }
  };

  function getLastDateOfCurrentYear() {
    var today = new Date(); // Get current date
    var currentYear = today.getFullYear(); // Get current year
    var lastDate = `${currentYear}-12-31`; // Set to December 31st of the current year
    return lastDate;
    //return localDateString;
  }

  function getLastDateOfStartYear(startDate) {
    if (!startDate) {
      // Fallback to current year if no start date provided
      return getLastDateOfCurrentYear();
    }

    // Extract year from start date (format: YYYY-MM-DD)
    const startYear = new Date(startDate).getFullYear();
    const lastDate = `${startYear}-12-31`; // Set to December 31st of the start year
    return lastDate;
  }
  function daysDifference() {
    const millisecondsInDay = 24 * 60 * 60 * 1000; // Number of milliseconds in a day
    // Calculate the difference in days, including both start and end days
    const daysDifference = Math.ceil(
      (new Date(formik.values.endDate) - new Date(formik.values.startDate)) /
        millisecondsInDay,
    );

    return daysDifference;
  }
  function daysRemainingInYear() {
    const now = new Date();
    const currentYear = now.getFullYear();
    const endOfYear = new Date(currentYear + 1, 0, 1); // January 1st of the next year
    const millisecondsInDay = 24 * 60 * 60 * 1000; // Number of milliseconds in a day
    // Calculate the difference in days
    const daysRemaining = Math.floor((endOfYear - now) / millisecondsInDay);
    return daysRemaining;
  }

  const validationSchema = Yup.object().shape({
    summary: Yup.string().required('task name is required'),
    startDate: Yup.date().required('Start Date is required'),
    fromTime: Yup.string().required('Start Time is required'),
    toTime: Yup.string().required('end Time is required'),
    endDate: Yup.date(),
    days: Yup.array()
      .min(1, 'At least one day must be selected')
      .required('At least one day must be selected'),
    // startDateTime: Yup.string().required('start date time is required'),
    // endDateTime: Yup.string().required('end date time is required'),
    daysCount: Yup.number().required('Days count is required'),
  });

  const formik = useFormik({
    initialValues: {
      summary: '',
      description: '',
      startDate: '',
      fromTime: '',
      toTime: '',
      endDate: '',
      days: '',
      attendees: '',
      startDateTime: '',
      endDateTime: '',
      daysCount: 0,
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      try {
        setProcessing(true);

        // --- 1.  Build a working copy we can mutate ----------------------
        const updatedValues = { ...values };

        const defaultEndDate = getLastDateOfStartYear(formik.values.startDate);      // "YYYY-12-31"
        const startDateStr    = formik.values.startDate;        // yyyy-mm-dd
        const endDateStr      =
          formik.values.isEndDate === 'never' || !formik.values.endDate
            ? defaultEndDate
            : formik.values.endDate;

        // Always keep the raw dates on updatedValues for the API
        updatedValues.endDate = endDateStr;

        // --- 2.  Validate the list of days selected ----------------------
        const isDaysExist = daysInclude(
          startDateStr,
          endDateStr,
          formik.values.days,
        );
        if (isDaysExist === 0) {
          alert('Selected days are not in between the selected dates.');
          setProcessing(false);
          return;
        }

        // --- 3.  Build zoned ISO strings with moment-timezone -----------
        //         (NO nested moment() call!)
        const startDateTime = moment
          .tz(`${startDateStr}T${formik.values.fromTime}`, 'Asia/Kolkata')
          .format();                    // e.g. "2025-05-29T09:00:00+05:30"

        const endDateTime = moment
          .tz(`${endDateStr}T${formik.values.toTime}`, 'Asia/Kolkata')
          .format();

        updatedValues.startDateTime = startDateTime;
        updatedValues.endDateTime   = endDateTime;

        // --- 4.  Day-count helpers --------------------------------------
        updatedValues.daysCount =
          endDateStr === defaultEndDate
            ? daysRemainingInYear()      // your helper
            : daysDifference();          // your helper

        // --- 5.  Send to API --------------------------------------------
        const response = await axios.post(
          `${API}/api/events?coachId=${user?.data?.id}`,
          {
            ...updatedValues,
            colorId: '1',
          },
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${user?.data?.token}`,
            },
          },
        );

        if (response) {
          alert('Event saved successfully');
          setTaskCreated(true);
          formik.resetForm();
        }
      } catch (err) {
        console.error('onSubmit error →', err);
        alert('Something went wrong. Please check your inputs and try again.');
      } finally {
        setProcessing(false);
        closeModal();        // close even on error? keep your UX decision
      }
    }

    // }
  });

  function daysInclude(startDate, endDate, daysArray) {
    const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const start = new Date(startDate);
    const end = new Date(endDate);

    let count = 0;
    let currentDate = new Date(start);

    while (currentDate <= end) {
      if (daysArray.includes(daysOfWeek[currentDate.getDay()])) {
        count++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }
    return count;
  }

  const handleConfirm = (time, type) => {
    if (type === 'start') {
      if (isTimeValid(time) && formik.values.startDate !== '') {
        formik.setFieldValue('fromTime', '');
        const finalTime = formatTime(time);
        formik.setFieldValue('fromTime', finalTime);
        setStartTimePickerVisibility(false);
      } else {
        // Handle invalid time selection
        alert(
          'Please select a start date to select the start time or Please select the start time after the current time.',
        );
        setStartTimePickerVisibility(false);
      }
    } else if (type === 'end') {
      const finalTime = formatTime(time);

      if (
        formik.values.endDate === formik.values.startDate ||
        formik.values.endDate == ''
      ) {
        if (
          finalTime > formik.values.fromTime &&
          formik.values.fromTime !== ''
        ) {
          formik.setFieldValue('toTime', finalTime);
          setEndTimePickerVisibility(false);
        } else {
          // Handle invalid time selection
          alert('Please select the end time after the start time.');
          setEndTimePickerVisibility(false);
        }
      } else {
        formik.setFieldValue('toTime', finalTime);
      }
    }
  };
  const hideTimePicker = type => {
    if (type == 'start') {
      setStartTimePickerVisibility(false);
    } else if (type == 'end') {
      setEndTimePickerVisibility(false);
    }
  };
  const showTimePicker = type => {
    if (type == 'start') {
      setStartTimePickerVisibility(true);
    } else if (type == 'end') {
      setEndTimePickerVisibility(true);
    }
  };
  const handleReset = () => {
    closeModal();
    formik.resetForm(); // Reset the form
    setCheckedDays([]);
  };

  return (
    <Modal
      animationType="slide"
      visible={isVisible}
      style={styles.modal}
      onRequestClose={closeModal}
      animationIn="slideInUp"
      animationOut="slideOutDown">
      <ScrollView>
        <View style={styles.modalContent}>
          <View>
            {/* style={{flexDirection:'row',justifyContent:'space-between',alignItems:'center'}} */}
            <TouchableOpacity
              style={{alignSelf: 'flex-end'}}
              onPress={handleReset}>
              <Icon name="close" size={24} color="black" />
            </TouchableOpacity>

            <View>
              <Text
                style={{
                  color: 'black',
                  marginTop: '5%',
                  fontFamily:
                    'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                }}>
                Enter Task Name
              </Text>
              <TextInput               placeholderTextColor="#000"

                keyboardType="default"
                style={[styles.input, {color: 'black'}]}
                onBlur={formik.handleBlur('summary')}
                value={formik.values.summary}
                onChangeText={formik.handleChange('summary')}
                placeholder="Task Name"
              />
              {formik.touched.summary && formik.errors.summary && (
                <Text style={styles.error}>{formik.errors.summary}</Text>
              )}

              {/* start dateeee */}
              <Text
                style={{
                  color: 'black',
                  marginTop: 20,
                  fontFamily:
                    'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                }}>
                {' '}
                Start Date{' '}
              </Text>
              <TouchableOpacity
                onPress={showDatePicker}
                style={styles.calendarIcon}>
                <View style={styles.inputContainer}>
                  <TextInput               placeholderTextColor="#000"

                    style={[styles.input, {color: 'black'}]}
                    placeholder="Tap to select the start date"
                    name="startDate"
                    value={
                      formik.values.startDate &&
                      `${new Date(formik.values.startDate).getDate()}/${
                        new Date(formik.values.startDate).getMonth() + 1
                      }/${new Date(formik.values.startDate).getFullYear()}`
                    }
                    editable={false}
                  />

                  <Icon name="calendar" size={20} color="black" />
                </View>
              </TouchableOpacity>

              {open && (
                <DatePicker
                  mode="calendar"
                  name="startDate"
                  minimumDate={new Date().toISOString().split('T')[0]} // Disable past dates
                  selected={
                    formik.values?.startDate
                      ? formik.values?.startDate
                      : new Date().toISOString().split('T')[0]
                  }
                  onDateChange={handleStartDateChange}
                />
              )}
              {formik.touched.startDate && formik.errors.startDate && (
                <Text style={styles.error}>{formik.errors.startDate}</Text>
              )}

              {/* from time  */}

              <Text
                style={{
                  color: 'black',
                  marginTop: 20,
                  fontFamily:
                    'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                }}>
                {' '}
                From{' '}
              </Text>
                      <TouchableOpacity
                  onPress={() => showTimePicker('start')}
                  style={styles.calendarIcon}>
              <View style={styles.inputContainer}>
                <TextInput               placeholderTextColor="#000"

                  style={[styles.input, {color: 'black'}]}
                  placeholder="--:--"
                  name="fromTime"
                  value={formik.values?.fromTime}
                  editable={false}
                />
                  <Icon name="hourglass" size={20} color="black" />


              </View>
                </TouchableOpacity>

              <DateTimePickerModal
                isVisible={isStartTimePickerVisible}
                mode="time"
                onConfirm={time => handleConfirm(time, 'start')}
                onCancel={() => hideTimePicker('start')}
              />

              {formik.touched.fromTime && formik.errors.fromTime && (
                <Text style={styles.error}>{formik.errors.fromTime}</Text>
              )}

              {/* to time */}
   <TouchableOpacity
                  onPress={() => showTimePicker('end')}
                  style={styles.calendarIcon}>
              <Text
                style={{
                  color: 'black',
                  marginTop: 20,
                  fontFamily:
                    'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                }}>
                {' '}
                To{' '}
              </Text>
              <View style={styles.inputContainer}>
                <TextInput               placeholderTextColor="#000"

                  style={[styles.input, {color: 'black'}]}
                  placeholder="--:--"
                  name="toTime"
                  value={formik.values.toTime}
                  editable={false}
                />


                  <Icon name="hourglass" size={20} color="black" />
              </View>
                </TouchableOpacity>

              <DateTimePickerModal
                isVisible={isEndTimePickerVisible}
                mode="time"
                onConfirm={time => handleConfirm(time, 'end')}
                onCancel={() => hideTimePicker('end')}
              />

              {formik.touched.toTime && formik.errors.toTime && (
                <Text style={styles.error}>{formik.errors.toTime}</Text>
              )}

              <Text
                style={{
                  color: 'black',
                  marginTop: '1%',
                  fontSize: 10,
                  fontFamily:
                    'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                }}>
                (From & To time are displayed in 24 hours format){' '}
              </Text>
              {/* end dateee */}

              <Text
                style={{
                  color: 'black',
                  marginTop: 20,
                  fontFamily:
                    'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                }}>
                {' '}
                End Date{' '}
              </Text>
              <RadioButton.Group
                onValueChange={newValue => setIsEndDate(newValue)}
                value={isEndDate}
                // style={{flexDirection:'row'}}
              >
                <View
                  style={{
                    marginLeft: '3%',
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'flex-start',
                  }}>
                  <RadioButton.Item label="Never" value="never" />
                  <RadioButton.Item label="On" value="on" />
                </View>
              </RadioButton.Group>
              {isEndDate == 'on' ? (
                <>
                  <TouchableOpacity
                    onPress={showEndDatePicker}
                    style={styles.calendarIcon}>
                    <View style={styles.inputContainer}>
                      <TextInput               placeholderTextColor="#000"

                        style={[styles.input, {color: 'black'}]}
                        placeholder="Tap to select the end date"
                        name="endDate"
                        //value={selectedDate ? selectedDate : ''}
                        value={
                          formik.values.endDate &&
                          `${new Date(formik.values.endDate).getDate()}/${
                            new Date(formik.values.endDate).getMonth() + 1
                          }/${new Date(formik.values.endDate).getFullYear()}`
                        }
                        editable={false}
                      />
                      {formik.touched?.endDate && formik.errors?.endDate && (
                        <Text>{formik.errors?.endDate}</Text>
                      )}

                      <Icon name="calendar" size={20} color="black" />
                    </View>
                  </TouchableOpacity>
                  {formik.touched.dates?.endDate &&
                    formik.errors.dates?.endDate && (
                      <Text style={styles.error}>
                        {formik.errors.dates?.endDate}
                      </Text>
                    )}
                  {openEndDate && (
                    <DatePicker
                      mode="calendar"
                      name="endDate"
                      minimumDate={
                        formik.values?.startDate
                          ? new Date(formik.values?.startDate)
                              .toISOString()
                              .split('T')[0]
                          : new Date().toISOString().split('T')[0]
                      }
                      selected={formik.values.endDate}
                      onDateChange={handleEndDateChange}
                    />
                  )}
                  {formik.touched.endDate && formik.errors.endDate && (
                    <Text style={styles.error}>{formik.errors.endDate}</Text>
                  )}
                </>
              ) : (
                <></>
              )}

              <View>
                {/* daysss */}
                <Text
                  style={{
                    color: 'black',
                    marginTop: '8%',
                    marginBottom: '3%',
                    fontSize: 16,
                    fontFamily:
                      'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                  }}>
                  {' '}
                  Select the Days{' '}
                </Text>
                <View style={{marginLeft: '2%'}}>
                  <View style={styles.dayContainer}>
                    <BouncyCheckbox
                      size={25}
                      fillColor="purple"
                      onPress={() => toggleDay('Mon')}
                      isChecked={checkedDays.includes('Mon')}
                    />
                    <Text style={{ color: 'black' }}>Monday</Text>
                  </View>
                  <View style={styles.dayContainer}>
                    <BouncyCheckbox
                      size={25}
                      fillColor="purple"
                      onPress={() => toggleDay('Tue')}
                      isChecked={checkedDays.includes('Tue')}
                    />
                    <Text  style={{ color: 'black' }}>Tuesday</Text>
                  </View>
                  <View style={styles.dayContainer}>
                    <BouncyCheckbox
                      size={25}
                      fillColor="purple"
                      onPress={() => toggleDay('Wed')}
                      isChecked={checkedDays.includes('Wed')}
                    />
                    <Text  style={{ color: 'black' }}>Wednesday</Text>
                  </View>
                  <View style={styles.dayContainer}>
                    <BouncyCheckbox
                      size={25}
                      fillColor="purple"
                      onPress={() => toggleDay('Thu')}
                      isChecked={checkedDays.includes('Thu')}
                    />
                    <Text  style={{ color: 'black' }}>Thursday</Text>
                  </View>
                  <View style={styles.dayContainer}>
                    <BouncyCheckbox
                      size={25}
                      fillColor="purple"
                      onPress={() => toggleDay('Fri')}
                      isChecked={checkedDays.includes('Fri')}
                    />
                    <Text  style={{ color: 'black' }}>Friday</Text>
                  </View>
                  <View style={styles.dayContainer}>
                    <BouncyCheckbox
                      size={25}
                      fillColor="purple"
                      onPress={() => toggleDay('Sat')}
                      isChecked={checkedDays.includes('Sat')}
                    />
                    <Text  style={{ color: 'black' }}>Saturday</Text>
                  </View>
                  <View style={styles.dayContainer}>
                    <BouncyCheckbox
                      size={25}
                      fillColor="purple"
                      onPress={() => toggleDay('Sun')}
                      isChecked={checkedDays.includes('Sun')}
                    />
                    <Text  style={{ color: 'black' }}>Sunday</Text>
                  </View>
                </View>
                {formik.touched.days && formik.errors.days && (
                  <Text style={styles.error}>{formik.errors.days}</Text>
                )}

                {/* days end */}
              </View>
              <View style={{alignSelf: 'flex-end'}}>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-around',
                    marginTop: 30,
                    marginHorizontal: 20,
                  }}>
                  <TouchableOpacity
                    onPress={handleReset}
                    disabled={processing}
                  >
                    <Text
                      style={{
                        borderWidth: 1,
                        borderColor: processing ? '#ccc' : 'grey',
                        padding: 10,
                        marginRight: 10,
                        fontWeight: '700',
                        color: processing ? '#999' : 'black',
                        borderRadius: 10,
                        fontFamily:
                          'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                      }}>
                      Cancel
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={formik.handleSubmit}
                    disabled={processing}
                  >
                    {processing ? (
                      <View style={{
                        borderWidth: 1,
                        borderColor: 'white',
                        padding: 10,
                        borderRadius: 10,
                        backgroundColor: 'gray',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                        <ActivityIndicator size="small" color="white" />
                      </View>
                    ) : (
                      <Text
                        style={{
                          borderWidth: 1,
                          borderColor: 'white',
                          padding: 10,
                          fontWeight: '700',
                          color: 'white',
                          borderRadius: 10,
                          backgroundColor: 'lightskyblue',
                          fontFamily:
                            'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                        }}>
                        {' '}
                        {'Save'}
                      </Text>
                    )}
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    height: '100%',

    justifyContent: 'center',
    alignItems: 'center',
  },
  error: {
    fontSize: 12,
    color: 'red',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },

  modalContent: {
    width: '100%',
    height: '100%',
    backgroundColor: 'white',
    padding: 20,
    borderWidth: 0,
    //borderRadius: 10,
  },
  input: {
    borderWidth: 1,
    borderColor: '#e5e7eb',
    marginVertical: '5%',
    height: 50,
    borderRadius: 10,
    backgroundColor: '#e5e7eb',
  },
  inputContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: '5%',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 10,
    paddingVertical: '1%',
    paddingHorizontal: '7%',
    backgroundColor: '#e5e7eb',
  },
  dayContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 5,
  },
});

export default TaskModal;