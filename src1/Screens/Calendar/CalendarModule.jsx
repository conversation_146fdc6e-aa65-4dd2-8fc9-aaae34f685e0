import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Button,
  TouchableOpacity,
  Image,
} from 'react-native';
// import {
//   GoogleSignin,
//   statusCodes,
//   GoogleSigninButton
// } from '@react-native-google-signin/google-signin';
import {Calendar,} from 'react-native-calendars';
import {Agenda} from 'react-native-calendars';
import Icon from 'react-native-vector-icons/FontAwesome';
import moment from 'moment';
import TaskModal from './TaskModal';
import axios from 'axios';
import { API, ANDROID_CLIENT_ID, WEB_CLIENT_ID, IOS_CLIENT_ID } from '@env';
import {getLoginToken} from '../../helpers';
//import { authorize, refresh, revoke  } from 'react-native-app-auth';
import { authorize } from 'react-native-app-auth';
import { useAuth } from '../../components/Auth/AuthContext';

// import { Platform } from 'react-native';
// import * as AddCalendarEvent from 'react-native-add-calendar-event';
// import * as Permissions from 'react-native-permissions';

// GoogleSignin.configure({
//   scopes: ['https://www.googleapis.com/auth/calendar'],
//   webClientId: '************-8lsb6eclcme8v62cr0cld5er4sfle1kq.apps.googleusercontent.com',
//   offlineAccess: true, // if you need to refresh token manually
// });


const CalendarModule = () => {

  const [selectedDate, setSelectedDate] = useState(moment().format('YYYY-MM-DD'));
  const [allEvents, setAllEvents] = useState([]);
  const [showCalendar, setShowCalendar] = useState(true);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [googleToken, setGoogleToken] = useState(true);
  const { user } = useAuth()
  //console.log("userrrin calendar",user)
  
  useEffect(() => {
    if(user?.data?.refreshToken){
     setGoogleToken(true)
    }
    googleEvents();
  }, [selectedDate, setSelectedDate, closeModal]);

  // useEffect(() => {
  //   GoogleSignin.configure({
  //     scopes: ['https://www.googleapis.com/auth/calendar'],
  //     webClientId: '************-8lsb6eclcme8v62cr0cld5er4sfle1kq.apps.googleusercontent.com',
  //     offlineAccess: true, // if you need to refresh token manually
  //   });
  // }, []);

  // const config = {
  //   issuer: 'https://accounts.google.com',
  //   clientId: '************-8lsb6eclcme8v62cr0cld5er4sfle1kq.apps.googleusercontent.com',
  //   redirectUrl: `${API}/api/calendar/google/redirect`,
  //   scopes: ['https://www.googleapis.com/auth/calendar'],

  //   //scopes: ['openid', 'profile', 'email'], // Scopes for the access token
  //   serviceConfiguration: {
  //     authorizationEndpoint: 'https://accounts.google.com/o/oauth2/v2/auth',
  //     tokenEndpoint: 'https://oauth2.googleapis.com/token',
  //   },
  // };
 

  const googleEvents = async (selectedStartDate,selectedEndDate) => {
    try {
      if(user?.data?.refreshToken){
        setGoogleToken(true)
       }

    const nextDate = moment(selectedDate).add(1, 'day').format('YYYY-MM-DD'); 
    let data = {
      startDate: selectedDate,
      endDate: nextDate,
      //email: '<EMAIL>',
      email:user?.data?.email,

    };
   
 
      const result = await axios.post(
        `${API}/api/calendar/get_event_list`,
        data,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${user?.data?.token}`,
          },
        },
      );
      let tempEvents = [];

    
      if (!result.error) {
        result?.data?.data &&
          result?.data?.data?.length > 0 &&
          result?.data?.data?.map(x => {
            // obj = {start:thgiu,endtime'lfskjnfd sukndffnkj cpokpod[]}
            if (
              selectedDate ==
              formatDateToYYYYMMDD(new Date(x?.start?.dateTime?.split('+')[0]))
            ) {
              //console.log(x);
              let startTime = formatTime(x.start?.dateTime);
              let endTime = formatTime(x.end?.dateTime);
              let title = x?.summary;
              let boxColor = x?.colorId ? x.colorId : '5'
            
              let obj = {startTime: startTime, endTime: endTime, title: title,boxColor:boxColor};

              tempEvents?.push(obj);
            }
          });
        //console.log(tempEvents, "000");
        setAllEvents(tempEvents);
      }
      if (!result) {
        window.location.reload();
      }
    } catch (error) {
      console.log(error, 'error');
    }
  };

  const formatTime = dateString => {
    let date = dateString?.split('+')[0];
    let hours = String(new Date(date).getHours()).padStart(2, '0');
    let minutes = String(new Date(date).getMinutes()).padStart(2, '0');
    let formattedTime = `${hours}:${minutes}`;
    const number = moment(formattedTime, ['HH.mm']).format('hh:mm a');
    //console.log(number);
    //console.log("formatted timeee", formattedTime);
    return number;
  };

  function formatDateToYYYYMMDD(dateString) {
    //console.log("datee in formatt",dateString)
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = `0${date.getMonth() + 1}`.slice(-2); // Add leading zero if needed
    const day = `0${date.getDate()}`.slice(-2); // Add leading zero if needed
    //console.log("datee in formatt",`${year}-${month}-${day}`)
    return `${year}-${month}-${day}`;
  }

  const openModal = () => {
    setIsModalVisible(true);
  };

  const closeModal = () => {
    setIsModalVisible(false);
  };


  const handleDateSelectDay = date => {
    setSelectedDate(date.dateString)
    //setShowCalendar(false)
  }



  const renderEvents = () => {
    const timeSlots = [];
    for (let i = 0; i <= 23; i++) {
      const time = moment().startOf('day').add(i, 'hours').format('h A');
      // const eventsForTimeSlot = dummyEvents?.filter(event => {
      //   const eventHour = moment(event.time, 'h:mm A').format('HH');
      //   return eventHour === i.toString().padStart(2, '0');
      // });

      const eventsForTimeSlot = allEvents?.filter(event => {
        const eventHour = moment(event.startTime, 'h:mm A').format('HH');
        //console.log('Event:', event.title, 'Event Hour:', eventHour, 'Current Hour:', i);
        return eventHour === i.toString().padStart(2, '0');
      });
      //console.log('Events for Time Slot:', eventsForTimeSlot);
      const eventsRows = eventsForTimeSlot.map((event, index) => {
        return (
          <View
            key={index}
            style={[{
              alignItems: 'center',
              margin: 2,
              borderRadius: 10,
              padding: 2,
              
            },
            //{backgroundColor : event?.boxColor === "1" ? {} : {backgroundColor: 'blue'} }
            {backgroundColor : (event?.boxColor  === "1" &&
            'red') ||(event?.boxColor === "2" &&'yellow') || (event?.boxColor === "3" && 'green') ||
          (event?.boxColor === "4" &&
            "grey") ||
            (event?.boxColor === "5" &&
            "blue")
        }
            
            
            
            ]}>
            <View key={index} style={[styles.eventRow]}>
              <Text
                style={{
                  fontSize: 10,
                  color: 'white',
                  fontWeight: '500',
                  fontFamily:'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                }}>{`${event.startTime} - ${event.endTime}`}</Text>
              <Text style={{fontSize: 12, color: 'white', fontWeight: '500',fontFamily:'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',}}>
                {event.title}
              </Text>
            </View>
          </View>
        );
      });
      timeSlots.push(
        <View
          key={i}
          style={[
            styles.timeSlotRow,
            // {height:`${height1}`}
            // { top: height1, height: height2 }
          ]}>
          <View style={styles.timeSlot2}>
            <Text>{}</Text>
          </View>
          <View style={styles.eventsContainer}>{eventsRows}</View>
        </View>,
      );
    }
    return timeSlots;
  };
  
  const renderTimeSlots = () => {
    const timeSlots = [];
    for (let i = 0; i <= 23; i++) {
      const time = moment().startOf('day').add(i, 'hours').format('h A');
      timeSlots.push(
        <View
          key={i}
          style={[
            styles.timeSlot1,
            // {top: height1, height: height2 }
          ]}>
          <Text style={{fontFamily:'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',}}>{time}</Text>
        </View>,
      );
    }
    return timeSlots;
  };

  const handleSignIn = async () => {
    // try {
    //   const result = await authorize(config);
    //   console.log('Authorization result:', result);
    //   // Handle successful authorization
    // } catch (error) {
    //   console.error('Authorization error:', error);
    //   // Handle authorization error
    // }
  
    // try {
    //   await GoogleSignin.hasPlayServices();
    //   const [accessToken, idToken] = await GoogleSignin.signIn();
    //   console.log(accessToken,idToken,"auth")
    //   console.log({userInfo}, 'infoGoogle111');
    //   const userInfo = await GoogleSignin.signIn();
    //   console.log(userInfo);
    //   // Now you can use userInfo to access user's information or make API calls to Google Calendar.
    // } catch (error) {
    //   console.log('Error in sign in: ', error);
    //   if (error.code === statusCodes.SIGN_IN_CANCELLED) {
    //     // user cancelled the login flow
    //     Alert.alert('Cancelled', 'Sign in cancelled');
    //   } else if (error.code === statusCodes.IN_PROGRESS) {
    //     Alert.alert('In progress', 'Sign in already in progress');
    //   } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
    //     Alert.alert(
    //       'Play services not available',
    //       'Please check your play services and try again',
    //     );
    //   } else {
    //     Alert.alert('Error', 'Something went wrong while signing in');
    //   }
    // }
  };

  return (
    <View style={styles.container}>
      {!googleToken  ? (
        <>
          <View
            style={{
              
              display: 'flex',
              justifyContent: 'center',
              alignContent: 'center',
              alignSelf: 'center',
              margin: 'auto',
            }}>
            <Text style={{marginVertical:10,fontSize:16,color:'black',fontFamily:'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',}}>Please Link your google calendar</Text>
            <Button title="Sign In with Google" onPress={handleSignIn} />
            {/* <GoogleSigninButton
        style={{ width: 192, height: 48 }}
        size={GoogleSigninButton?.Size?.Wide}
      color={GoogleSigninButton?.Color.Light}
        onPress={handleSignIn}
      /> */}
          </View>
        </>
      ) : (
        <>
          <ScrollView>
            <View style={styles.header}>
              {/* header */}
              {/* 1 */}
              <View>
                <View style={{flexDirection: 'row'}}>
                  <View>
                    <TouchableOpacity
                      onPress={() => setShowCalendar(!showCalendar)}>
                      <Text style={styles.selectedDateText}>
                        {moment(selectedDate).format('Do MMMM YYYY')}
                      </Text>
                      <Text style={{}}>
                        {moment(selectedDate).format('dddd')}
                      </Text>
                    </TouchableOpacity>
                  </View>
                  <View>
                    <TouchableOpacity
                      style={{marginLeft: 5}}
                      onPress={() => setShowCalendar(!showCalendar)}>
                      <Icon
                        name={showCalendar ? 'caret-up' : 'caret-down'}
                        size={20}
                        color="grey"
                      />
                    </TouchableOpacity>
                  </View>

                 
                </View>
              </View>

              {/* 2 */}
              <View>
                <Button title="Add Event" onPress={openModal} />
              </View>
            </View>
            {/* calendar */}
            {showCalendar && (
              <View style={{borderBottomWidth: 1, borderBottomColor: '#ccc'}}>

               <Calendar
                  current={selectedDate}
                  onDayPress={handleDateSelectDay}
                  style={styles.calendar}
                  markedDates={{
                    [selectedDate]: {
                      selected: true,
                      selectedColor: 'green',
                      dotColor: 'white',
                    },
                  }}
                /> 


</View>
            )}

            <View style={styles.body}>
             
              <View style={styles.leftColumn}>{renderTimeSlots()}</View>
              <View style={styles.rightColumn}>{renderEvents()}</View>
            </View>
            <View>
              <TaskModal isVisible={isModalVisible} closeModal={closeModal} />
            </View>
          </ScrollView>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 10,
    borderWidth: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#BFEFFF',
    // alignItems: 'center',
  },
  body: {
    flex: 1,
    flexDirection: 'row',
  },
  leftColumn: {
    flex: 1,
    backgroundColor: '#f0f0f0',
    //marginRight: 5,
  },
  rightColumn: {
    flex: 4,
    backgroundColor: '#fff',
    //marginLeft: 5,
  },
  calendar: {
    marginBottom: 10,
  },
  timeSlot1: {
    paddingHorizontal: 10,
    height: 100,
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  timeSlot2: {
    paddingHorizontal: 10,

    alignItems: 'center',
    justifyContent: 'center',

    borderBottomColor: '#ccc',
  },
  eventContainer: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  selectedDateText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 1,
    fontFamily:'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },

  timeSlotRow: {
    height: 100,

    flexDirection: 'row',
    borderBottomWidth: 1, // Border between rows
    borderBottomColor: '#ccc', // Border color
    // alignItems:'center',
    // justifyContent:"center",
  },
  eventsContainer: {
    //backgroundColor:'blue',
    borderRadius: 10,
  },
  eventRow: {
    borderWidth: 0,
    alignItems: 'center',
    padding: 3,
    borderRadius: 10,
  },
});

export default CalendarModule;
