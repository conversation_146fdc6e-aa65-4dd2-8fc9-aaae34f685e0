import axios from 'axios';
import React from 'react';
import {API} from '@env';
import { useAuth } from '../../components/Auth/AuthContext';
import { View, Text, ScrollView, StyleSheet,TouchableOpacity, Alert, } from 'react-native';
import { useNavigation } from '@react-navigation/native';



const timeToMinutes = (time) => {
  const [hours, minutes] = time.split(':').map(Number);
  return hours * 60 + minutes;
};

const formatTimeWithAmPm = (time) => {
  const [hours, minutes] = time.split(':');
  const hoursInt = parseInt(hours, 10);
  const suffix = hoursInt >= 12 ? 'PM' : 'AM';
  const formattedHours = ((hoursInt + 11) % 12 + 1);
  return `${formattedHours}:${minutes} ${suffix}`;
};

const CalendarSlot = ({ slot, minuteHeight,setTaskDeleted }) => {
  const {user, setPageName} = useAuth();
  const navigation = useNavigation()
  //console.log("slot in calendar",slot)

  const startMinutes = timeToMinutes(slot.startTime);
  const endMinutes = timeToMinutes(slot.endTime);
  const duration = endMinutes - startMinutes;

  const slotHeight = Math.max(duration * minuteHeight, 30);
  const slotTopPosition = startMinutes * minuteHeight;

  // Function to show delete confirmation alert
  const showDeleteConfirmation = () => {
    console.log("Showing delete confirmation for event:", slot?.id);

    // Use a direct Alert call without setTimeout
    Alert.alert(
      'Delete Event',
      'Do you want to delete this event? It will also get deleted from your Google account.',
      [
        {
          text: 'Cancel',
          onPress: () => console.log('Cancel Pressed'),
          style: 'cancel',
        },
        {
          text: 'Delete',
          onPress: () => handleDeleteEvent(slot?.id, user?.data?.id, user?.data?.token),
          style: 'destructive',
        },
      ],
      {
        cancelable: true,
        // Ensure the alert is shown with highest priority
        userInterfaceStyle: 'light'
      }
    );
  };

  const handlePageRedirect = async(slot) => {
    try {
      const descriptionObj = JSON.parse(slot?.description);

      const response = descriptionObj?.type === 'bookings' ?
      await axios.get(`${API}/api/booking/${descriptionObj?.id}`) :
      await axios.get(`${API}/api/course/${descriptionObj?.id}`);

      let data = await response?.data;

      if (data && data.message === "No bookings found") {
        data = []; // Setting data to an empty array if the message indicates no bookings found
        Alert.alert("No bookings found in this ")
        return
      }

      descriptionObj.type === 'bookings' ?
      (navigation.navigate('BookingDetails',{source: 'custom-calendar', booking: data}))
      : (navigation.push('CourseCreate', { source: 'custom-calendar', listing: data }))
    } catch (error) {
      console.log("Error in page redirect:", error);
      Alert.alert("Error", "Failed to load details. Please try again.");
    }
  }

  const handleDeleteEvent = async (eventId, coachId, token) => {
    try {
      console.log("Deleting event with ID:", eventId);
      console.log("Coach ID:", coachId);

      if (!eventId || !coachId || !token) {
        console.log("Missing required parameters for event deletion");
        Alert.alert("Error", "Missing information required to delete the event. Please try again.");
        return;
      }

      // Extract the base event ID if it contains an underscore
      const baseEventId = eventId.includes("_") ? eventId.split("_")[0] : eventId;
      console.log("Base event ID for deletion:", baseEventId);

      // Show a loading indicator or message
      Alert.alert("Processing", "Deleting event...");

      const response = await axios.delete(`${API}/api/events/${coachId}`, {
        data: {
          eventId: baseEventId,
        },
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        timeout: 15000, // 15 second timeout to handle slow connections
      });

      console.log("Delete response:", response?.data);

      if(response?.data){
        // Dismiss the loading alert if it's still showing
        Alert.alert("Success", "Event Deleted Successfully");
        setTaskDeleted(true);
      } else {
        Alert.alert("Warning", "Event deletion completed but no confirmation received. Please check if the event was deleted.");
        // Still update the UI to reflect the change
        setTaskDeleted(true);
      }
    }
    catch(error){
      console.log("Error deleting event:", error);
      if (error.response) {
        console.log("Error response data:", error.response.data);
        console.log("Error response status:", error.response.status);

        // More user-friendly error message
        let errorMessage = "Failed to delete event. ";
        if (error.response.status === 401 || error.response.status === 403) {
          errorMessage += "You may not have permission to delete this event.";
        } else if (error.response.status === 404) {
          errorMessage += "The event could not be found.";
        } else if (error.response.status >= 500) {
          errorMessage += "Server error occurred. Please try again later.";
        } else {
          errorMessage += `Error code: ${error.response.status}`;
        }

        Alert.alert("Error", errorMessage);
      } else if (error.request) {
        console.log("No response received:", error.request);
        Alert.alert("Connection Error", "No response received from server. Please check your internet connection and try again.");
      } else {
        console.log("Error message:", error.message);
        Alert.alert("Error", `An unexpected error occurred: ${error.message}`);
      }
    }
  }

  // Handle the press event based on the box color
  const handlePress = () => {
    console.log("Event pressed:", slot?.title, "Box color:", slot?.boxColor);

    if(slot?.boxColor === '1'){
      showDeleteConfirmation();
    }
    else if(slot?.boxColor === "2" || slot?.boxColor === "3" || slot?.boxColor === "4"){
      handlePageRedirect(slot);
    }
  };

  return (
    <View style={{
      position: 'absolute',
      top: slotTopPosition,
      left: 55,
      right: 10,
      minHeight: slotHeight,
      zIndex: 1
    }}>
      <TouchableOpacity
        key={slot?.id}
        activeOpacity={0.7}
        style={{
          flex: 1,
          backgroundColor: (slot?.boxColor === "1" && '#B91C1C') ||
                          (slot?.boxColor === "2" && 'rgb(107 114 128)') ||
                          (slot?.boxColor === "3" && '#15803D') ||
                          (slot?.boxColor === "4" && "rgb(251 191 36)") ||
                          (slot?.boxColor === "5" && "lightblue"),
          borderRadius: 5,
          padding: 5,
        }}
        onPress={handlePress}
        delayPressIn={0}
      >
        <View>
          <Text style={styles.slotText}>{`${formatTimeWithAmPm(slot.startTime)} - ${formatTimeWithAmPm(slot.endTime)}`}</Text>
          <Text style={styles.slotTitle}>{slot.title}</Text>
        </View>
      </TouchableOpacity>
    </View>
  );
};

const TimeAxis = ({ minuteHeight }) => {
  const times = [];
  for (let i = 0; i <= 23; i++) {
    times.push(
      <View key={i} style={{ height: 60 * minuteHeight }}>
        <Text style={styles.timeText}>{formatTimeWithAmPm(`${i}:00`)}</Text>
      </View>
    );
  }
  return <View style={styles.timeAxis}>{times}</View>;
};

const CustomCalendar = ({bookedSlots,setTaskDeleted}) => {
  const minuteHeight = 2;
  const calendarHeight = 24 * 60 * minuteHeight;

  // Group events into columns based on overlapping times
  const columns = [];
  bookedSlots.forEach(event => {
    let placed = false;
    for (let i = 0; i < columns.length; i++) {
      let column = columns[i];
      const lastEvent = column[column.length - 1];
      if (!lastEvent || event.startTime >= lastEvent.endTime) {
        column.push(event);
        placed = true;
        break;
      }
    }
    if (!placed) {
      columns.push([event]);
    }
  });
  return (
    <ScrollView horizontal
    style={[styles.container]} contentContainerStyle={{ height: calendarHeight }}>
      <TimeAxis minuteHeight={minuteHeight} />
      <ScrollView horizontal>
        <View style={{ flexDirection: 'row' }}>
          {columns.map((column, columnIndex) => (
            <View key={columnIndex} style={{ width: 200, marginRight: 10 }}>
              {column.map((event, index) => (
                <CalendarSlot key={index} slot={event} minuteHeight={minuteHeight} setTaskDeleted={setTaskDeleted} />
              ))}
            </View>
          ))}
        </View>
      </ScrollView>

    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    width: '100%',
    borderRadius: 10,
    marginBottom: 10,
    elevation: 2,
    color:"#000"
  },
  slot: {
    backgroundColor: '#0EA5E9',
    borderRadius: 5,
    padding: 5,
    color:"#000",
    flex: 1
  },
  slotText: {
    color: '#fff',
    fontWeight: 'bold',
    fontFamily:'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e'
  },
  slotTitle: {
    color: '#fff',
    fontFamily:'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e'
  },
  timeAxis: {
    position: 'absolute',
    left: 5,
    top: 0,
    bottom: 0,
    width: 50,
    paddingRight: 5,
    borderRightWidth: 1,
    borderColor: '#ddd',
    color:"#000"
  },
  timeText: {
    color: '#000',
    fontSize: 14,
  },
});

export default CustomCalendar;