import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Button,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
} from 'react-native'
import { Calendar, } from 'react-native-calendars';
import { Agenda } from 'react-native-calendars';
import Icon from 'react-native-vector-icons/FontAwesome';
import moment from 'moment';
import TaskModal from './TaskModal';
import axios from 'axios';
import { API, ANDROID_CLIENT_ID, WEB_CLIENT_ID, IOS_CLIENT_ID } from '@env';
import { getLoginToken } from '../../helpers';
import { authorize } from 'react-native-app-auth';
import { useAuth } from '../../components/Auth/AuthContext';
import { useNavigation } from '@react-navigation/native';
import CustomCalendar from './CustomCalendar';
import Svg, { Circle } from 'react-native-svg';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  GoogleSignin,
  GoogleSigninButton,
  statusCodes,
} from '@react-native-google-signin/google-signin';

GoogleSignin.configure({
  webClientId: WEB_CLIENT_ID,
  androidClientId: ANDROID_CLIENT_ID,
  iosClientId: IOS_CLIENT_ID,
  offlineAccess: true,
  scopes: ['profile', 'email', 'https://www.googleapis.com/auth/calendar'],
  forceCodeForRefreshToken: true, // This forces a code to be obtained on Android
})

const CalendarModule1 = () => {
  const [selectedDate, setSelectedDate] = useState(
    moment().format('YYYY-MM-DD'),
  );
  const [allEvents, setAllEvents] = useState([]);
  const [showCalendar, setShowCalendar] = useState(true);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [googleToken, setGoogleToken] = useState(false);
  const [loading, setLoading] = useState(false);
  const [taskCreated, setTaskCreated] = useState(false);
  const [taskDeleted, setTaskDeleted] = useState(false);
  const [calendarKey, setCalendarKey] = useState(0); // key to force re-render
  const [googleSignInCompleted, setGoogleSignInCompleted] = useState(false); // track when Google Sign-In is completed

  const { user, getUserDetails, updateUserDetails } = useAuth();

  // We'll keep the navigation import for potential future use
  const navigation = useNavigation();
  useEffect(() => {
    setLoading(true);
    if (user?.data?.refreshToken) {
      setGoogleToken(true);
      setLoading(false);
    } else {
      setLoading(false);
    }

    googleEvents();
  }, [selectedDate, setSelectedDate, closeModal, taskCreated]);
  useEffect(() => {
    if (taskCreated) {
      // Fetch updated calendar data here
      googleEvents();
      setTaskCreated(false); // Reset taskCreated state
      setLoading(true);
    } else if (taskDeleted) {
      googleEvents();
      setTaskDeleted(false);
      setLoading(true);
    }
  }, [taskCreated, taskDeleted]);

  useEffect(() => {
    GoogleSignin.configure({
      webClientId: WEB_CLIENT_ID,
      androidClientId: ANDROID_CLIENT_ID,
      iosClientId: IOS_CLIENT_ID,
      offlineAccess: true,
      scopes: ['profile', 'email', 'https://www.googleapis.com/auth/calendar'],
    })
  }, []);

  // const googleEvents = async () => {
  //   try {
  //     //console.log("user refersh token",user?.data?.refreshToken)
  //     if (user?.data?.refreshToken) {
  //       setGoogleToken(true);
  //     }
  //     //console.log("selected date inside google evenrts fn", selectedDate)

  //     const nextDate = moment(selectedDate).add(1, 'day').format('YYYY-MM-DD');
  //     //console.log("Next Date:", nextDate);
  //     let data = {
  //       startDate: selectedDate,
  //       endDate: nextDate,
  //       //email: '<EMAIL>',
  //       email: user?.data?.email,
  //     };

  //     //console.log('login tokennn', user?.data?.token);

  //     //console.log("dtaaa before api test", data)
  //     const result = await axios.post(`${API}/api/calendar/eventList`, data, {
  //       headers: {
  //         'Content-Type': 'application/json',
  //         Authorization: `Bearer ${user?.data?.token}`,
  //       },
  //     });
  //     let tempEvents = [];

  //     if (!result.error) {
  //       result?.data?.data &&
  //         result?.data?.data?.length > 0 &&
  //         result?.data?.data?.map(x => {
  //           if (
  //             selectedDate ==
  //             formatDateToYYYYMMDD(new Date(x?.start?.dateTime?.split('+')[0]))
  //           ) {
  //             //console.log(x?.description);
  //             let startTime = formatTime(x.start?.dateTime);
  //             let endTime = formatTime(x.end?.dateTime);
  //             let title = x?.summary;
  //             let boxColor = x?.colorId ? x.colorId : '5';
  //             let description = x?.description;
  //             let id = x?.id;

  //             let obj = {
  //               startTime: startTime,
  //               endTime: endTime,
  //               title: title,
  //               boxColor: boxColor,
  //               description: description,
  //               id: id,
  //             };
  //             tempEvents?.push(obj);
  //           }
  //         });
  //       //console.log(tempEvents, "000");
  //       setAllEvents(tempEvents);
  //       setLoading(false);
  //     }
  //     if (!result) {
  //       window.location.reload();
  //     }
  //   } catch (error) {
  //     console.log(error, 'error');
  //   }
  // };
  useEffect(() => {
    console.log("Calendar component - Main useEffect triggered");
    console.log("Calendar component - User data:", user?.data);
    console.log("Calendar component - Refresh token:", user?.data?.refreshToken);

    // Only set loading and fetch events when the date changes or when explicitly triggered
    // This prevents infinite loops
    const shouldFetchEvents = user?.data?.refreshToken &&
      (googleSignInCompleted || // Only fetch after sign-in is completed
       calendarKey > 0); // Or when calendar key changes (manual refresh)

    if (user?.data?.refreshToken) {
      console.log("Setting googleToken to true - refresh token found");
      setGoogleToken(true);
    } else {
      console.log("Setting googleToken to false - no refresh token found");
      setGoogleToken(false);
      setLoading(false);
    }

    // Only call googleEvents if we have a refresh token AND we should fetch events
    if (shouldFetchEvents) {
      console.log("Calling googleEvents to fetch calendar data");
      setLoading(true);
      googleEvents();

      // Reset the googleSignInCompleted flag to prevent repeated fetching
      if (googleSignInCompleted) {
        setGoogleSignInCompleted(false);
      }
    }
  }, [selectedDate, user?.data?.refreshToken, googleSignInCompleted, calendarKey]);
  useEffect(() => {
    console.log("Task creation/deletion useEffect triggered");

    if (taskCreated) {
      console.log("Task created, fetching updated calendar data");
      setLoading(true);
      // Use setTimeout to ensure state updates have completed
      setTimeout(() => {
        googleEvents();
        setTaskCreated(false); // Reset taskCreated state
      }, 100);
    } else if (taskDeleted) {
      console.log("Task deleted, fetching updated calendar data");
      setLoading(true);
      // Use setTimeout to ensure state updates have completed
      setTimeout(() => {
        googleEvents();
        setTaskDeleted(false);
      }, 100);
    }
  }, [taskCreated, taskDeleted]);

  useEffect(() => {
    GoogleSignin.configure({
      webClientId: WEB_CLIENT_ID,
      androidClientId: ANDROID_CLIENT_ID,
      iosClientId: IOS_CLIENT_ID,
      offlineAccess: true,
      scopes: ['profile', 'email', 'https://www.googleapis.com/auth/calendar'],
      forceCodeForRefreshToken: true, // This forces a code to be obtained on Android
    })

    // Check if user is already signed in and validate token
    const validateGoogleAccess = async () => {
      try {
        // First check if signed in with Google
        const isSignedIn = await GoogleSignin.isSignedIn();
        console.log("Is user signed in with Google:", isSignedIn);

        if (isSignedIn) {
          try {
            // Try to get tokens to verify they're still valid
            const tokens = await GoogleSignin.getTokens();
            console.log("Google tokens are valid");

            // Verify calendar access by making a test API call
            if (user?.data?.token && user?.data?.email) {
              try {
                // Simple test call to check if we can access calendar
                const testData = {
                  startDate: moment().format('YYYY-MM-DD'),
                  endDate: moment().add(1, 'day').format('YYYY-MM-DD'),
                  email: user?.data?.email,
                };

                await axios.post(`${API}/api/calendar/eventList`, testData, {
                  headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${user?.data?.token}`,
                  },
                  timeout: 5000,
                });
                console.log("Calendar API access is valid");
              } catch (apiError) {
                console.log("Calendar API access failed:", apiError);
                // Handle revoked access
                await handleRevokedAccess("Calendar API access failed");
              }
            }
          } catch (tokenError) {
            console.log("Google tokens are invalid or expired:", tokenError);
            // Handle revoked access
            await handleRevokedAccess("Google tokens are invalid or expired");
          }
        } else if (user?.data?.refreshToken) {
          // User has a refresh token in our system but is not signed in with Google
          // This indicates access was revoked externally
          console.log("User has refresh token but is not signed in with Google");
          await handleRevokedAccess("Google access appears to have been revoked");
        }
      } catch (error) {
        console.error("Error validating Google access:", error);
        // On any error, reset Google token state to be safe
        setGoogleToken(false);
      }
    };

    validateGoogleAccess();
  }, [user?.data?.token, user?.data?.email, user?.data?.refreshToken]);

  const googleEvents = async () => {
    try {
      console.log("googleEvents function - Checking refresh token:", user?.data?.refreshToken);

      // First verify that we're still signed in with Google
      try {
        if (user?.data?.refreshToken) {
          setGoogleToken(true);
        }

        console.log("Fetching events for date:", selectedDate);

        const nextDate = moment(selectedDate).add(1, 'day').format('YYYY-MM-DD');
        console.log("Next Date for event range:", nextDate);

        let data = {
          startDate: selectedDate,
          endDate: nextDate,
          email: user?.data?.email,
        };

        console.log("Request data for events:", data);
        console.log("Using auth token:", user?.data?.token ? "Token available" : "No token");

        const result = await axios.post(`${API}/api/calendar/eventList`, data, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${user?.data?.token}`,
          },
        });

        console.log("Event list API response status:", result.status);
        let tempEvents = [];

        if (!result.error) {
          console.log("Events received:", result?.data?.data?.length || 0);

          result?.data?.data &&
            result?.data?.data?.length > 0 &&
            result?.data?.data?.map(x => {
              if (
                selectedDate ==
                formatDateToYYYYMMDD(new Date(x?.start?.dateTime?.split('+')[0]))
              ) {
                console.log("Processing event:", x?.summary, "ID:", x?.id);

                let startTime = formatTime(x.start?.dateTime);
                let endTime = formatTime(x.end?.dateTime);
                let title = x?.summary;
                let boxColor = x?.colorId ? x.colorId : '5';
                let description = x?.description;
                let id = x?.id;

                let obj = {
                  startTime: startTime,
                  endTime: endTime,
                  title: title,
                  boxColor: boxColor,
                  description: description,
                  id: id,
                };
                tempEvents?.push(obj);
              }
            });

          console.log("Events for display:", tempEvents.length);
          setAllEvents(tempEvents);
          setLoading(false);
        }
        if (!result) {
          console.log("No result from API, reloading page");
          window.location.reload();
        }
      } catch (signInError) {
        console.log("Error checking Google sign-in status:", signInError);
        // On error, assume we're not signed in
        setGoogleToken(false);
        setLoading(false);
        return;
      }

      if (user?.data?.refreshToken) {
        console.log("Setting googleToken to true in googleEvents");
        setGoogleToken(true);
      } else {
        console.log("No refresh token found in googleEvents");
        setGoogleToken(false);
        setLoading(false);
        return; // Exit early if no refresh token
      }

      console.log("Selected date for events:", selectedDate);

      const nextDate = moment(selectedDate).add(1, 'day').format('YYYY-MM-DD');
      console.log("Next date for events:", nextDate);

      if (!user?.data?.email) {
        console.log("No user email found, cannot fetch events");
        setLoading(false);
        return;
      }

      let data = {
        startDate: selectedDate,
        endDate: nextDate,
        email: user?.data?.email,
      };

      console.log("Fetching events with data:", data);
      console.log("Using token:", user?.data?.token);

      try {
        console.log(`Making API request to ${API}/api/calendar/eventList`);
        const result = await axios.post(`${API}/api/calendar/eventList`, data, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${user?.data?.token}`,
          },
          timeout: 10000, // 10 second timeout
        });

        console.log("API response status:", result.status);
        console.log("API response for events:", result?.data);

        let tempEvents = [];

        if (!result.error) {
          if (result?.data?.data && result?.data?.data?.length > 0) {
            console.log("Found events:", result?.data?.data?.length);

            result?.data?.data.forEach(x => {
              try {
                if (
                  selectedDate ==
                  formatDateToYYYYMMDD(new Date(x?.start?.dateTime?.split('+')[0]))
                ) {
                  let startTime = formatTime(x.start?.dateTime);
                  let endTime = formatTime(x.end?.dateTime);
                  let title = x?.summary;
                  let boxColor = x?.colorId ? x.colorId : '5';
                  let description = x?.description;
                  let id = x?.id;

                  let obj = {
                    startTime: startTime,
                    endTime: endTime,
                    title: title,
                    boxColor: boxColor,
                    description: description,
                    id: id,
                  };
                  tempEvents?.push(obj);
                }
              } catch (eventError) {
                console.log("Error processing event:", eventError);
                // Continue processing other events
              }
            });

            console.log("Processed events for display:", tempEvents.length);
          } else {
            console.log("No events found for the selected date");
          }

          // Update events and loading state in a single batch to prevent multiple renders
          console.log("Setting events and completing loading");
          setAllEvents(tempEvents);
          setLoading(false);

          // Only update calendar key if explicitly requested (not during normal data fetching)
          // This prevents unnecessary re-renders that could cause loops
          if (googleSignInCompleted) {
            console.log("Forcing calendar re-render after sign-in");
            // We don't need to update the key here as the main useEffect will handle the refresh
          }
        } else {
          console.log("Error in API response:", result.error);
          setLoading(false);
        }
      } catch (apiError) {
        console.log("API error when fetching events:", apiError);

        if (apiError.response) {
          console.log("Error response data:", apiError.response.data);
          console.log("Error response status:", apiError.response.status);

          // Check for specific error codes that might indicate revoked access
          if (apiError.response.status === 401 ||
              apiError.response.status === 403 ||
              (apiError.response.data &&
               (apiError.response.data.includes("access_denied") ||
                apiError.response.data.includes("invalid_grant")))) {

            console.log("API error indicates access might be revoked");

            // Handle revoked access
            await handleRevokedAccess("API error indicates access might be revoked");

            Alert.alert(
              "Access Revoked",
              "Your Google Calendar access appears to have been revoked. Please sign in again."
            );
          } else {
            // For other errors, just show a generic message
            setLoading(false);
            Alert.alert("Error", "Failed to fetch calendar events. Please try again.");
          }
        } else if (apiError.request) {
          console.log("No response received:", apiError.request);
          setLoading(false);
          Alert.alert("Connection Error", "Could not connect to the server. Please check your internet connection and try again.");
        } else {
          console.log("Error setting up request:", apiError.message);
          setLoading(false);
          Alert.alert("Error", "Failed to fetch calendar events. Please try again.");
        }
      }
    } catch (error) {
      console.log("Error in googleEvents function:", error);
      setLoading(false);
      Alert.alert("Error", "An unexpected error occurred. Please try again.");
    }
  };

  // Function to handle when Google access has been revoked
  const handleRevokedAccess = async (reason) => {
    console.log(`Handling revoked access: ${reason}`);

    // Reset Google token state
    setGoogleToken(false);

    // Clear loading state
    setLoading(false);

    // Try to sign out from Google to force re-authentication
    try {
      await GoogleSignin.signOut();
      console.log("Signed out from Google due to revoked access");
    } catch (signOutError) {
      console.log("Error signing out from Google:", signOutError);
    }

    // Clear the refresh token from the backend if needed
    try {
      if (user?.data?.id && user?.data?.token) {
        console.log("Attempting to clear refresh token from backend");
        await axios.post(
          `${API}/api/calendar/revoke-access`,
          { coachId: user.data.id },
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${user.data.token}`,
            },
          }
        );
        console.log("Successfully cleared refresh token from backend");
      }
    } catch (clearTokenError) {
      console.log("Error clearing refresh token from backend:", clearTokenError);
    }

    // Update local user data
    updateUserDetails();
  };

  const formatTime = dateString => {
    let date = dateString?.split('+')[0];
    let hours = String(new Date(date).getHours()).padStart(2, '0');
    let minutes = String(new Date(date).getMinutes()).padStart(2, '0');
    let formattedTime = `${hours}:${minutes}`;
    //const number = moment(formattedTime, ['HH.mm']).format('hh:mm a');
    //console.log(number);
    // console.log("formatted timeee", formattedTime);
    // return number;
    return formattedTime;
  };

  function formatDateToYYYYMMDD(dateString) {
    //console.log("datee in formatt",dateString)
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = `0${date.getMonth() + 1}`.slice(-2); // Add leading zero if needed
    const day = `0${date.getDate()}`.slice(-2); // Add leading zero if needed
    //console.log("datee in formatt",`${year}-${month}-${day}`)
    return `${year}-${month}-${day}`;
  }

  const openModal = () => {
    setIsModalVisible(true);
  };

  const closeModal = () => {
    setIsModalVisible(false);
  };

  const handleDateSelectDay = date => {
    setSelectedDate(date.dateString);
    //setShowCalendar(false)
  };

  const handleSignIn = async () => {
    try {
      console.log("Starting Google Sign-In process...");

      // Check if Google Play Services are available
      const playServicesAvailable = await GoogleSignin.hasPlayServices({
        showPlayServicesUpdateDialog: true // Show dialog if Play Services need to be updated
      });
      console.log("Play Services available:", playServicesAvailable);

      // Trigger Google Sign-In flow
      console.log("Triggering Google Sign-In...");
      const userInfo = await GoogleSignin.signIn();
      console.log("Google Sign-In successful, user info:", userInfo);

      const { user, serverAuthCode } = userInfo;
      console.log("Server Auth Code received:", serverAuthCode ? "Yes" : "No");

      if (user && serverAuthCode) {
        try {
          // Get the coach email from AsyncStorage
          const coachEmail = await AsyncStorage.getItem('coachEmail');
          console.log("Coach email from storage:", coachEmail);

          const url = `${API}/api/calendar/google/redirect?code=${serverAuthCode}&state=${coachEmail}&mobile=true`;
          console.log("Calling API with URL:", url);

          const response = await axios.get(url, {
            headers: {
              'Content-Type': 'application/json',
            },
          });

          console.log('API response status:', response.status);
          console.log('API response data:', response.data);

          // Update user details to get the refreshToken
          console.log("Getting updated user details...");
          const updatedUser = await getUserDetails();
          console.log("Updated user refresh token:", updatedUser?.data?.refreshToken ? "Present" : "Missing");

          if (updatedUser?.data?.refreshToken) {
            console.log("Setting Google token to true after successful sign-in");

            // Update all states in a controlled manner to prevent loops
            // First set the token state
            setGoogleToken(true);

            // Then trigger a single calendar refresh by setting the completed flag
            // This will be picked up by the main useEffect
            console.log("Marking Google Sign-In as completed to trigger calendar refresh");
            setGoogleSignInCompleted(true);

            // Increment the calendar key to force a re-render
            // This is separate from the data fetching to prevent loops
            setCalendarKey(prevKey => prevKey + 1);

            Alert.alert('Success', 'Google Calendar linked successfully!');
          } else {
            console.log("No refresh token found in updated user data");
            Alert.alert('Warning', 'Calendar linked but refresh token not found. Please try again.');
          }
        } catch (error) {
          console.log('Error retrieving data from API:', error);
          if (error.response) {
            console.log('API error status:', error.response.status);
            console.log('API error data:', error.response.data);
          }
          Alert.alert('Error', 'Failed to link Google Calendar. Please try again.');
        }
      } else {
        console.log('Missing user or serverAuthCode from Google Sign-In response');
        Alert.alert('Error', 'Failed to get authorization from Google. Please try again.');
      }
    } catch (error) {
      console.log('Google Sign-In error code:', error.code);

      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        // User cancelled the sign-in flow
        console.log('Sign-in cancelled by user');
      } else if (error.code === statusCodes.IN_PROGRESS) {
        // Another sign-in operation is in progress
        console.log('Sign-in already in progress');
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        // Google Play Services is not available on this device
        console.log('Google Play Services not available on this device');
        Alert.alert('Error', 'Google Play Services not available or outdated on this device. Please update Google Play Services and try again.');
      } else {
        // Other error
        console.error('Detailed error:', error);
        Alert.alert(
          'Error',
          'Failed to sign in with Google. Please try again. Error: ' +
          (error.message || 'Unknown error')
        );
      }
    }
  };

  const colorScheme = [
    { name: 'Break', color: '#B91C1C', backgroundColor: '#FEE2E2' },
    { name: 'Courses', color: '#4B5563', backgroundColor: '#F3F4F6' },
    { name: 'Bookings', color: '#15803D', backgroundColor: '#DCFCE7' },
    { name: 'Sessions', color: '#854D0E', backgroundColor: '#FEF3C7' },
    { name: 'Other Events', color: '#1D4ED8', backgroundColor: '#DBEAFE' },
  ];

  return (
    <View style={styles.container}>
      {!googleToken ? (
        <>
          <View
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignContent: 'center',
              alignSelf: 'center',
              margin: 'auto',
            }}>
            <Text
              style={{
                marginVertical: 10,
                fontSize: 16,
                color: 'black',
                fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
              }}>
              Please Link your google calendar
            </Text>
            <Button title="Sign In with Google" onPress={handleSignIn} />
          </View>
        </>
      ) : (
        <>
          <ScrollView>
            {loading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" />
              </View>
            ) : (
              <>
                <View style={styles.header}>
                  {/* header */}

                  <View
                    style={{
                      borderWidth: 0,
                      marginVertical: 5,
                      marginBottom: 15,
                    }}>
                    <View>
                      <TouchableOpacity
                        onPress={() => setShowCalendar(!showCalendar)}>
                        <Text style={styles.selectedDateText}>
                          {moment(selectedDate).format('Do MMMM YYYY')}
                        </Text>
                        <Text style={{}}>
                          {moment(selectedDate).format('dddd')}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>

                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}>
                    <View
                      style={{
                        flex: 1,
                        marginRight: 15,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        borderWidth: 1,
                        paddingHorizontal: 10,
                        paddingVertical: 4,
                        borderRadius: 5,
                        borderColor: '#979797',
                      }}>
                      <TouchableOpacity
                        style={{ borderRadius: 50 }}
                        onPress={() => {
                          const previousDate = moment(selectedDate)
                            .subtract(1, 'day')
                            .format('YYYY-MM-DD');
                          setSelectedDate(previousDate);
                        }}>
                        <Icon name="angle-left" size={25} color="#979797" />
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={{
                          paddingVertical: 6,
                          paddingHorizontal: 5,
                          borderRadius: 5,
                        }}
                        onPress={() => {
                          setSelectedDate(moment().format('YYYY-MM-DD'));
                          setCalendarKey(prevKey => prevKey + 1); // force re-render to shift month
                        }}>
                        <Text
                          style={{
                            fontSize: 16,
                            color: 'black',
                            fontFamily:
                              'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                          }}>
                          Today
                        </Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={{ borderRadius: 50 }}
                        onPress={() => {
                          const nextDate = moment(selectedDate)
                            .add(1, 'day')
                            .format('YYYY-MM-DD');
                          setSelectedDate(nextDate);
                        }}>
                        <Icon name="angle-right" size={25} color="#979797" />
                      </TouchableOpacity>
                    </View>

                    <View>
                      <TouchableOpacity
                        style={{
                          backgroundColor: '#4F46E5',
                          alignSelf: 'center',
                          paddingVertical: 10,
                          paddingHorizontal: 10,
                          borderRadius: 5,
                        }}
                        onPress={openModal}>
                        <Text
                          style={{
                            fontSize: 16,
                            color: 'white',
                            fontFamily:
                              'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                          }}>
                          Add Event
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
                {/* calendar */}
                {showCalendar && (
                  <View
                    style={{
                      borderWidth: 1,
                      margin: 10,
                      borderColor: 'black',
                      borderRadius: 5,
                    }}>
                    <Calendar
                      key={calendarKey}
                      current={selectedDate}
                      onDayPress={handleDateSelectDay}
                      style={styles.calendar}
                      markedDates={{
                        [selectedDate]: {
                          selected: true,
                          selectedColor: 'blue',
                          dotColor: 'white',
                        },
                      }}
                    />
                  </View>
                )}

                <View style={styles.body}>
                  <View
                    style={{
                      borderWidth: 0,
                      width: '100%',
                      flexDirection: 'row',
                      flexWrap: 'wrap',
                      justifyContent: 'flex-start',
                      paddingHorizontal: 6,
                      marginVertical: 10,
                    }}>
                    {colorScheme?.map((item, index) => (
                      <View
                        key={index}
                        style={{
                          paddingHorizontal: 6,
                          paddingVertical: 3,
                          flexDirection: 'row',
                          backgroundColor: `${item.backgroundColor}`,
                          alignItems: 'center',
                          borderRadius: 15,
                          margin: 2,
                          marginRight: 10,
                        }}>
                        <Svg
                          height={10}
                          width={10}
                          viewBox="0 0 6 6"
                          aria-hidden="true">
                          <Circle cx="3" cy="3" r="3" fill={item.color} />
                        </Svg>
                        <Text
                          style={{
                            color: `${item.color}`,
                            marginLeft: 2,
                            fontFamily:
                              'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                          }}>
                          {item.name}
                        </Text>
                      </View>
                    ))}
                  </View>
                  <CustomCalendar
                    bookedSlots={allEvents}
                    setTaskDeleted={setTaskDeleted}
                  />
                </View>
                <View>
                  <TaskModal
                    setTaskCreated={setTaskCreated}
                    isVisible={isModalVisible}
                    closeModal={closeModal}
                    user={user}
                    setLoading={setLoading}
                  />
                </View>
              </>
            )}
          </ScrollView>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {

    height: '100%',

    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    flex: 1,
    backgroundColor: '#F5F8FC',
  },
  header: {
    padding: 10,
    borderWidth: 0,

  },
  body: {

  },
  leftColumn: {
    flex: 1,
    backgroundColor: '#f0f0f0',
    //marginRight: 5,
  },
  rightColumn: {
    flex: 4,
    backgroundColor: '#fff',
    //marginLeft: 5,
  },
  calendar: {
    //marginBottom: 10,
    borderRadius: 5,
  },
  timeSlot1: {
    paddingHorizontal: 10,
    height: 100,
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  timeSlot2: {
    paddingHorizontal: 10,

    alignItems: 'center',
    justifyContent: 'center',

    borderBottomColor: '#ccc',
  },
  eventContainer: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  selectedDateText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 1,
    color: 'black',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e'
  },

  timeSlotRow: {
    height: 100,

    flexDirection: 'row',
    borderBottomWidth: 1, // Border between rows
    borderBottomColor: '#ccc', // Border color

  },
  eventsContainer: {
    //backgroundColor:'blue',
    borderRadius: 10,
  },
  eventRow: {
    borderWidth: 0,
    alignItems: 'center',
    padding: 3,
    borderRadius: 10,
  },
});

export default CalendarModule1;