import {
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ActivityIndicator,
  ScrollView,
  Alert
} from 'react-native';
import React, { useEffect, useState } from 'react';
import { Badge, DataTable } from 'react-native-paper';
import moment from 'moment';
import { Image } from 'react-native';
import axios from 'axios';
import { API } from '@env';
import { useAuth } from '../../components/Auth/AuthContext';
import { useNavigation } from '@react-navigation/native';
import { TextInput } from 'react-native';
import { Picker } from '@react-native-picker/picker';
import Icon from 'react-native-vector-icons/FontAwesome';
import DatePicker from 'react-native-modern-datepicker';
import Invoice from '../Invoice';
import RNFS from 'react-native-fs';
import Share from 'react-native-share';
import * as ScopedStorage from 'react-native-scoped-storage';
import RNHTMLtoPDF from 'react-native-html-to-pdf';
import { fromByteArray } from 'base64-js';
import { Buffer } from 'buffer';
import AsyncStorage from '@react-native-async-storage/async-storage';
const Reports = () => {
  const today = moment().tz('Asia/Kolkata').format('YYYY-MM-DD');
  const [bookingDetails, setBookingDetails] = useState([]);
  const [loading, setLoading] = useState(true);
  const navigation = useNavigation();
  const { user, setPageName } = useAuth();
  const [classType, setClassType] = useState('');
  const [statusType, setStatusType] = useState('');
  const [searchQuery, setSearchQuery] = useState();
  const [selectedStartDate, setSelectedStartDate] = useState(today);
  const [selectedEndDate, setSelectedEndDate] = useState(today);
  const [minEndDate, setMinEndDate] = useState(today);
  const [maxEndDate, setMaxEndDate] = useState(today);
  const [maxStartDate, setMaxStartDate] = useState(today);
  const [open, setOpen] = useState(false);
  const [openEndDate, setOpenEndDate] = useState(false);
  const [arrayLength, setArrayLength] = useState('');
  const [invoiceModal, setInvoiceModal] = useState(false);
  const [singleReport, setSingleReport] = useState({})
  const [singleReportIndex, setSingleReportIndex] = useState({})
  const [page, setPage] = useState(1);
  useEffect(() => {
    getCoachBooking();
    setClassType('');
    setStatusType('');
    setSearchQuery('');
    setSelectedStartDate(today);
    setSelectedEndDate(today);
    setTimeout(() => {
      setLoading(false);
    }, 3000);
  }, []);
  useEffect(() => {
    getCoachBooking();
  }, [page, setPage, selectedStartDate, selectedEndDate]);
  useEffect(() => {
    const start = moment(selectedStartDate).tz('Asia/Kolkata').startOf('day');
    const minEnd = start.clone();
    const maxEnd = moment.min(start.clone().add(7, 'days'), moment(today));
    setMinEndDate(minEnd.format('YYYY-MM-DD'));
    setMaxEndDate(maxEnd.format('YYYY-MM-DD'));
    if (
      moment(selectedEndDate).isBefore(minEnd) ||
      moment(selectedEndDate).isAfter(maxEnd)
    ) {
      setSelectedEndDate(minEnd.format('YYYY-MM-DD'));
    }
  }, [selectedStartDate]);

  const getCoachBooking = async () => {
    try {
      setLoading(true);
      const response = await axios.post(
        `${API}/api/booking/reports?coachId=${user?.data?._id}&page=${page}&startDate=${selectedStartDate}&endDate=${selectedEndDate}`,
        {},
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${user?.data?.token}`,
          },
        },
      );
      //   console.log("----->> reponse", response?.data?.report)
      setBookingDetails(response?.data?.report);
      const finalResult = response?.data?.report;
      setArrayLength(finalResult.length);
    } catch (error) {
      console.log('---> error', error);
    } finally {
      setLoading(false);
    }
  };
  const showDatePicker = () => {
    setOpen(!open);
  };

  const showEndDatePicker = () => {
    setOpenEndDate(!openEndDate);
  };

  function handleStartDateChange(propDate) {
    const formattedDate = moment(propDate, 'YYYY/MM/DD').format('YYYY-MM-DD');
    setSelectedStartDate(formattedDate);
    setOpen(false);
  }

  function handleEndDateChange(propDate) {
    const formattedDate = moment(propDate, 'YYYY/MM/DD').format('YYYY-MM-DD');
    try {
      if (moment(formattedDate).isBetween(minEndDate, maxEndDate, null, '[]')) {
        setSelectedEndDate(formattedDate);
        setOpenEndDate(false);
        handleSearchFilter({
          search: searchQuery,
          clas: classType,
          status: statusType,
          startdate: selectedStartDate,
          enddate: formattedDate,
        });
      } else {
        alert(
          'End date cannot be before start date or more than 7 days from start date',
        );
        setOpenEndDate(false);
      }
    } catch (error) {
      setOpenEndDate(false);
    }
  }
  const handleLoadMore = () => {
    if (!loading) {
      getCoachBooking();
    }
  };
  const ListEndLoader = () => {
    if (loading) {
      return <ActivityIndicator size={'large'} />;
    }
  };

  const renderItem = ({ item, index }) => {
    var bookingDateMoment = moment(item?.createdAt);
    var formattedBookingDate = bookingDateMoment.format('Do MMMM YYYY');

    let str = item?.courseName;
    let coursename =
      str &&
      str
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');

    return (
      <>
        {invoiceModal && (
          <Invoice
            open={invoiceModal}
            setOpen={setInvoiceModal}
            singleReport={singleReport}
            singleReportIndex={singleReportIndex}
          />
        )}
        <ScrollView>
          <View style={{ flex: 1, borderBottomWidth: 1, borderColor: '#DFDFDF' }}>
            <TouchableOpacity
              key={item.id}
              onPress={() => {
                setInvoiceModal(!invoiceModal);
                setSingleReport(item);
                setSingleReportIndex(index)
              }}>
              <View
                style={{
                  borderColor: '#DFDFDF',
                  paddingVertical: '2%',
                  paddingHorizontal: '2%',
                }}
                key={item.id}>
                <DataTable>
                  <DataTable.Row style={styles.row}>
                    <DataTable.Cell>
                      <Text style={styles.cell}>Booking Id :</Text>
                    </DataTable.Cell>
                    <DataTable.Cell>
                      <Text style={styles.value}>{item?.bookingId}</Text>
                    </DataTable.Cell>
                  </DataTable.Row>
                  <DataTable.Row style={styles.row}>
                    <DataTable.Cell>
                      <Text style={styles.cell}>Date:</Text>
                    </DataTable.Cell>
                    <DataTable.Cell>
                      <Text style={styles.value}>
                        {item?.date?.split('T')[0]}
                      </Text>
                    </DataTable.Cell>
                  </DataTable.Row>
                  <DataTable.Row style={styles.row}>
                    <DataTable.Cell>
                      <Text style={styles.cell}>Course Name :</Text>
                    </DataTable.Cell>
                    <DataTable.Cell>
                      <Text style={styles.value}>
                        {item?.courseName.split(' ').slice(0, 4).join(' ')}
                      </Text>
                    </DataTable.Cell>
                  </DataTable.Row>
                  <DataTable.Row style={styles.row}>
                    <DataTable.Cell>
                      <Text style={styles.cell}>Coach Name :</Text>
                    </DataTable.Cell>
                    <DataTable.Cell>
                      <Text style={styles.value}>{item?.coachName}</Text>
                    </DataTable.Cell>
                  </DataTable.Row>

                  <DataTable.Row style={styles.row}>
                    <DataTable.Cell>
                      <Text style={styles.cell}>Course Fees :</Text>
                    </DataTable.Cell>
                    <DataTable.Cell>
                      <Text style={styles.value}>{item?.classFees}</Text>
                    </DataTable.Cell>
                  </DataTable.Row>

                  <DataTable.Row style={styles.row}>
                    <DataTable.Cell>
                      <Text style={styles.cell}>Coach Name :</Text>
                    </DataTable.Cell>
                    <DataTable.Cell>
                      <Text style={styles.value}>{item?.coachName}</Text>
                    </DataTable.Cell>
                  </DataTable.Row>

                  <DataTable.Row style={styles.row}>
                    <DataTable.Cell>
                      <Text style={styles.cell}>TDS :</Text>
                    </DataTable.Cell>
                    <DataTable.Cell>
                      <Text style={styles.value}>{item?.tds}</Text>
                    </DataTable.Cell>
                  </DataTable.Row>

                  <DataTable.Row style={styles.row}>
                    <DataTable.Cell>
                      <Text style={styles.cell}>Amount Received :</Text>
                    </DataTable.Cell>
                    <DataTable.Cell>
                      <Text style={styles.value}>{item?.amountReceived}</Text>
                    </DataTable.Cell>
                  </DataTable.Row>

                  <DataTable.Row style={styles.row}>
                    <DataTable.Cell>
                      <Text style={styles.cell}>Status:</Text>
                    </DataTable.Cell>
                    <DataTable.Cell>
                      <Text style={styles.value}>{item?.paymentStatus}</Text>
                    </DataTable.Cell>
                  </DataTable.Row>

                  <DataTable.Row style={styles.row}>
                    <DataTable.Cell>
                      <Text style={styles.cell}>Coach Attendance :</Text>
                    </DataTable.Cell>
                    <DataTable.Cell>
                      <Text style={styles.value}>{item?.coachAttendance}</Text>
                    </DataTable.Cell>
                  </DataTable.Row>

                  <DataTable.Row style={styles.row}>
                    <DataTable.Cell>
                      <Text style={styles.cell}>Player Attendance :</Text>
                    </DataTable.Cell>
                    <DataTable.Cell>
                      <Text style={styles.value}>{item?.playerAttendance}</Text>
                    </DataTable.Cell>
                  </DataTable.Row>
                  <DataTable.Row style={styles.row}>
                    <DataTable.Cell>
                      <Text style={styles.cell}>Invoice :</Text>
                    </DataTable.Cell>
                    <DataTable.Cell>
                      <TouchableOpacity
                        onPress={() => {
                          setSingleReport(item);
                          setInvoiceModal(!invoiceModal);
                        }}>
                        <Text style={{color:"#000"}}>Download</Text>
                      </TouchableOpacity>
                    </DataTable.Cell>
                  </DataTable.Row>
                </DataTable>
              </View>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </>
    );
  };

  const handleSearchFilter = async ({
    search,
    clas,
    status,
    startdate,
    enddate,
  }) => {
    try {
      let queryString = '';
      if (search) {
        queryString += `courseName=${search}`;
      }
      if (startdate) {
        queryString += `${queryString ? '&' : ''}startDate=${startdate}`;
      }
      if (enddate) {
        queryString += `${queryString ? '&' : ''}endDate=${enddate}`;
      }
      if (status) {
        queryString += `${queryString ? '&' : ''}paymentStatus=${status}`;
      }
      console.log('---->> query', queryString);
      const response = await axios.post(
        `${API}/api/booking/reports?coachId=${user?.data?._id}&page=${page}&${queryString}`,
        {},
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${user?.data?.token}`,
          },
        },
      );
      const finalResult = await response?.data?.report;

      setArrayLength(finalResult?.length);

      setBookingDetails([]);
      setBookingDetails(finalResult);
    } catch (error) {
      console.error('error', error);
      setClassType('');
      setStatusType('');
      setSearchQuery('');
    }
  };

  const seachFilterComp = () => {
    return (
      <View
        style={{
          borderColor: '#DFDFDF',
          paddingVertical: '2%',
          borderBottomWidth: 1,
        }}>
          <Text style={{color:"#000", alignSelf:"center", fontSize:16}}>Choose the status</Text>
        <View style={styles.dropdownContainer}>
          <View
            style={{
              flex: 1,
              borderColor: '#e5e7eb',
              borderWidth: 1,
              borderRadius: 6,
            }}>
            <Picker
              selectedValue={statusType}
              onValueChange={(itemValue, itemIndex) => {
                setStatusType(itemValue),
                  handleSearchFilter({
                    status: itemValue,
                    startdate: selectedStartDate,
                    enddate: selectedEndDate,
                  });
              }}
              style={styles.dropdown}>
              <Picker.Item label="Paid" value="paid" />
              <Picker.Item label="Unpaid" value="unpaid" />
              <Picker.Item label="All" value="" />
            </Picker>
          </View>
        </View>

        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginHorizontal: '3%',
            borderWidth: 0,
            marginBottom: 5,
          }}>
          {/* start dateeee */}

          <View style={[styles.inputContainer, { marginRight: 5 }]}>
            <TextInput
              style={{ color: 'black' }}
              placeholder="Start Date"
              name="startDate"
              value={selectedStartDate ? selectedStartDate : ''}
              editable={false}
            />

            <TouchableOpacity
              onPress={showDatePicker}
              style={styles.calendarIcon}>
              <Icon name="calendar" size={20} color="black" />
            </TouchableOpacity>
          </View>

          {/* start date  */}

          {/* enddatee */}
          <View style={styles.inputContainer}>
            <TextInput
              style={{ color: 'black' }}
              placeholder="End Date"
              name="endDate"
              value={selectedEndDate ? selectedEndDate : ''}
              //value={formik.values.endDate}
              editable={false}
            />

            <TouchableOpacity
              onPress={showEndDatePicker}
              style={styles.calendarIcon}>
              <Icon name="calendar" size={20} color="black" />
            </TouchableOpacity>
          </View>
          {/* end date */}
        </View>

        {/* start date picker */}
        {open && (
          <DatePicker
            mode="calendar"
            name="startDate"
            maximumDate={maxStartDate}
            selected={selectedStartDate}
            // onMonthChange={(month, year) => console.log(month, year)}
            onDateChange={handleStartDateChange}
          />
        )}

        {/* end date picker  */}
        {openEndDate && (
          <DatePicker
            mode="calendar"
            name="endDate"
            id="end_date"
            selected={selectedEndDate}
            minimumDate={minEndDate}
            maximumDate={maxEndDate}
            onDateChange={handleEndDateChange}
          />
        )}
      </View>
    );
  };
  const generateCSV = () => {
    let csvContent = "Booking Id,Date,Course Name,Coach Name,Course Fees,TDS,Amount Received,Status, Coach Attendence, Player Attendence\n";
    bookingDetails.forEach(item => {
      csvContent += `${item.bookingId},${item.date.split('T')[0]},${item.courseName.split(' ').slice(0, 4).join(' ')},${item.coachName},${item.classFees},${item.tds},${item.amountReceived},${item.paymentStatus}, ${item?.coachAttendance}, ${item?.playerAttendance}\n`;
    });
    return csvContent;
  };

  const downloadCSV = async () => {
    const csvContent = generateCSV();
    const base64CSV = Buffer.from(csvContent).toString('base64');

    if (Platform.OS === 'android') {
      try {
        let dir = await ScopedStorage.openDocumentTree(true);
        const timestamp = new Date().getTime();
        const fileName = `reports_${timestamp}.csv`;
        const mimeType = "text/csv";
        const encoding = "base64";
        await ScopedStorage.writeFile(dir.uri, base64CSV, fileName, mimeType, encoding);
        await AsyncStorage.setItem('userMediaDirectory', JSON.stringify(dir));
        Alert.alert('CSV Created', `CSV file has been created and saved in the selected directory.`);
      } catch (error) {
        console.error(error);
        Alert.alert('Error', 'An error occurred while creating the CSV.');
      }
    } else if (Platform.OS === 'ios') {
      try {
          const timestamp = new Date().getTime();
          const fileName = `reports_${timestamp}.csv`;
          const filePath = `${RNFS.DocumentDirectoryPath}/${fileName}`;
          await RNFS.writeFile(filePath, csvContent, 'utf8');
          
          const shareOptions = {
              title: 'Share CSV',
              url: `file://${filePath}`,
              type: 'text/csv',
          };

          await Share.open(shareOptions);
          Alert.alert('CSV Created', `CSV file has been created and ready for sharing.`);
      } catch (error) {
          console.error(error);
          // Alert.alert('Error', 'An error occurred while creating the CSV.');
      }
  } else {
      console.log("Unsupported platform");
  }

  };
  return (
    <ScrollView>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" />
        </View>
      ) : (
        <View
          style={{
            backgroundColor: '#F5F8FC',
          }}>
          <View style={{ backgroundColor: '#F5F8FC' }}>{seachFilterComp()}</View>

          {arrayLength === 0 || arrayLength === undefined ? (
            <View
              style={{
                flex: 1,
                padding: 5,
                alignItems: 'center',
                backgroundColor: 'white',
              }}>
              <Text
                style={{
                  color: 'black',
                  fontSize: 19,
                  padding: 4,
                  fontFamily:
                    'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                }}>
                No Reports Found
              </Text>
            </View>
          ) : (
            <View>
              <TouchableOpacity onPress={downloadCSV} style={{ marginBottom: "4%", display: "flex", flexDirection: "row", justifyContent: "space-evenly", alignSelf:"flex-end" }}>
                <Text style={{ color: "#000" }}>Download CSV File</Text>
                <Icon name="download" size={25} color="#000" />
              </TouchableOpacity>
              <View style={{ borderWidth: 0, }}>

                <FlatList
                  data={bookingDetails}
                  keyExtractor={(item, index) => index.toString()}
                  renderItem={renderItem}
                //  onEndReached={handleLoadMore}
                //  onEndReachedThreshold={0.8}
                //  ListFooterComponent={ListEndLoader}
                />
              </View>
            </View>
          )}
        </View>
      )}
    </ScrollView>
  );
};

export default Reports;

const styles = StyleSheet.create({
  loadingContainer: {
    height: '100%',

    justifyContent: 'center',
    alignItems: 'center',
  },

  // search comp

  search: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: '3%',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 6,
    paddingVertical: '1%',
    paddingHorizontal: '7%',
    marginHorizontal: 10,
  },
  input: {
    height: 40,
    width: '95%',
    //borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
    paddingHorizontal: 10,
    alignSelf: 'center',
    //  marginRight: 10,
  },
  dropdownContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: '2%',
    marginHorizontal: '3%',
    borderWidth: 0,
  },

  boxContainer: {
    marginTop: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  inputContainer: {
    // width:'50%',
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 6,
    paddingVertical: '1%',
    paddingHorizontal: '2%',
    color: 'black',
    //backgroundColor:'white',
    //marginHorizontal:'2.5%',
  },

  //   table - card

  cell: {
    fontSize: 14,
    fontWeight: '600',
    borderBottomWidth: 0, // This will remove the bottom border from all cells
    color: 'black',
    borderWidth: 0,
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
    //alignItems: 'flex-start',
  },
  row: {
    borderBottomWidth: 0,
    marginVertical: '-1.5%',
  },
  value: {
    color: '#626262',
    fontSize: 14,
    fontWeight: '400',
    borderWidth: 0,
    //alignItems: 'center',
    //justifyContent:'center',
    flexWrap: 'wrap',
    marginLeft: '-25%',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
    //marginTop: '1%',
    marginLeft:"1%"
  },

  buttonV: {
    borderWidth: 1,
    borderColor: '#0068FF',
    borderRadius: 6,
  },
  buttonT: {
    paddingHorizontal: 10,
    paddingVertical: 6,
    color: '#0068FF',
    fontWeight: '500',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
});
