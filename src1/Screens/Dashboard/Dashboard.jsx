import React, {useEffect, useState, useRef, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
  PermissionsAndroid,
  Platform,
} from 'react-native';

import Geolocation from '@react-native-community/geolocation';
import {Calendar} from 'react-native-calendars';
import Icon from 'react-native-vector-icons/FontAwesome';
import axios from 'axios';

import { API } from '@env';

import {useAuth} from '../../components/Auth/AuthContext';
import {useFocusEffect, useNavigation} from '@react-navigation/native';

import QRCode from 'react-native-qrcode-svg';
import QRCodeModal from './QRCodeModal';
import moment from 'moment-timezone';
import CancelBookingModal from './CancelBookingModal';
import ScreenContainer from '../../components/ScreenContainer';

const Dashboard = () => {
  const [selectedDate, setSelectedDate] = useState(
    moment().format('YYYY-MM-DD'),
  );
  const [showEvent, setShowEvent] = useState(false);
  const [eventType, setEventType] = useState('course');
  const [classEvents, setClassEvents] = useState([]);
  const [bookingList, setBookingList] = useState([]);
  const [showCalendar, setShowCalendar] = useState(true);
  const {
    user,
    getUserDetails,
    isHeader,
    calendarLinked,
    setPageName,
  } = useAuth();
  const [expandedIndex, setExpandedIndex] = useState(null);
  const [loading, setLoading] = useState(false);
  const [showQRCode, setShowQRCode] = useState(false);
  const [qrCodeItem, setQRCodeItem] = useState(null);

  const [showBookingModal, setShowBookingModal] = useState(false);
  const [bookingCancelled, setBookingCancelled] = useState(false);
  const [bookingData, setBookingData] = useState();
  const [calendarKey, setCalendarKey] = useState(0); // key to force re-render

  let base64Logo =
    'data:image/jpeg;base64,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';

  useEffect(() => {
    if (bookingCancelled) {
      setLoading(true);
      setExpandedIndex(null);
      googleEvents();
      setBookingCancelled(false); // Reset state
      setLoading(false);
    }
  }, [bookingCancelled]);

  const checkAttendanceShowTime = data => {
    console.log('data inside qr ', data);
    let startDate = moment
      .tz(moment(selectedDate), 'Asia/Kolkata')
      .format()
      .split('T')[0];
    startDate = moment
      .tz(moment(`${startDate}T${data.startTime}`), 'Asia/Kolkata')
      .format();

    let endDate = moment
      .tz(moment(selectedDate), 'Asia/Kolkata')
      .format()
      .split('T')[0];
    endDate = moment
      .tz(moment(`${endDate}T${data.endTime}`), 'Asia/Kolkata')
      .format();

    const currentDate = moment.tz(moment(), 'Asia/Kolkata').format();

    const showButton = moment(currentDate).isBetween(
      moment(startDate).subtract(15, 'minutes'),
      moment(endDate).add(15, 'minutes'),
    );

    // Enhanced attendance details structure from production
    const attendanceDetails = {
      bookingId: data.bookingId,
      classId: data.classId,
      playerId: data.playerId,
      maxGroupSize: data.maxGroupSize,
      courseId: data.courseId,
      isAttendanceVisible: showButton,
    };

    if (showButton) {
      setQRCodeItem(attendanceDetails);
      setShowQRCode(true);
    } else {
      Alert.alert(
        'QR Code Not Available',
        'The QR code will be available for scanning from 15 minutes prior to the course start time until 15 minutes after the course end time.'
      );
    }
  };





  useFocusEffect(
    useCallback(() => {
      if (!user || !isHeader) {
        navigation.navigate('Login');
      } else {
        // Automatically refresh events when the screen comes into focus
        console.log('Dashboard screen focused - refreshing events');
        googleEvents();
      }
    }, [user]),
  );

  // useEffect(() => {
  //   if (pageName == 'Login' || pageName == 'Dashboard') {
  //     const backAction = () => {
  //       Alert.alert('Hold on!', 'Are you sure you want to exit the app?', [
  //         {
  //           text: 'Cancel',
  //           onPress: () => null,
  //           style: 'cancel',
  //         },
  //         {text: 'YES', onPress: () => BackHandler.exitApp()},
  //       ]);
  //       return true;
  //     };

  //     const backHandler = BackHandler.addEventListener(
  //       'hardwareBackPress',
  //       backAction,
  //     );

  //     return () => backHandler.remove();
  //   }
  // }, []);

  const requestLocationPermission = async () => {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        {
          title: 'Geolocation Permission',
          message: 'Can we access your location?',
          buttonPositive: 'OK',
        },
      );
      console.log('granted', granted);
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    } catch (err) {
      console.error('Error requesting location permission:', err);
      return false;
    }
  };

  const getUserLocation = async () => {
    return new Promise((resolve, reject) => {
      const handleSuccess = position => {
        const {latitude, longitude} = position.coords;
        console.log('Latitude:', latitude, 'Longitude:', longitude);
        const obj = {Latitude: latitude, Longitude: longitude};
        console.log('objjjjj', obj);
        resolve(obj);
      };

      const handleError = error => {
        console.log('Error getting location:', error.message);
        if (error.code === 3) {
          // Timeout error, retry or inform the user
          console.log('Timeout error: Please try again.');
        }
        reject(error);
      };

      const permissionGranted = requestLocationPermission();
      if (!permissionGranted) {
        console.log('Location permission denied');
        Alert.alert("Location Permission Denied", "You won't be able to mark the attendance if location is denied");
        reject(new Error('Location permission denied'));
        return;
      }

      Geolocation.getCurrentPosition(handleSuccess, handleError, {
        timeout: 60000,
        maximumAge: 1000,
      });
    });
  };

  const markCoachAttendance = async data => {
    try {
      setLoading(true);
      let startDate = moment
        .tz(moment(selectedDate), 'Asia/Kolkata')
        .format()
        .split('T')[0];
      startDate = moment
        .tz(moment(`${startDate}T${data.startTime}`), 'Asia/Kolkata')
        .format();

      let endDate = moment
        .tz(moment(selectedDate), 'Asia/Kolkata')
        .format()
        .split('T')[0];
      endDate = moment
        .tz(moment(`${endDate}T${data.endTime}`), 'Asia/Kolkata')
        .format();

      const currentDate = moment.tz(moment(), 'Asia/Kolkata').format();

      const showButton = moment(currentDate).isBetween(
        moment(startDate).subtract(15, 'minutes'),
        moment(endDate).add(15, 'minutes'),
      );

      if (showButton) {
        const gotCoachLocation = await getUserLocation();

        // Enhanced location validation from production
        if (!gotCoachLocation || !gotCoachLocation.Latitude || !gotCoachLocation.Longitude) {
          setLoading(false);
          Alert.alert('Location Error', 'Unable to get your location. Please ensure location services are enabled.');
          return;
        }

        const result = await axios.post(
          `${API}/api/attendance`,
          {
            ...data,
            lat: gotCoachLocation.Latitude,
            long: gotCoachLocation.Longitude,
          },
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${user?.data?.token}`,
            },
          },
        );

        setLoading(false);
        console.log('result for coach attendance', result);

        if (!result.data.error) {
          Alert.alert('Success', 'Attendance marked successfully');
          // Refresh the events to show updated attendance status
          googleEvents();
        } else {
          // Enhanced error handling from production
          if (result.data?.error?.status === 400) {
            Alert.alert('Location Error', 'Your location does not match the required venue location.');
          } else {
            Alert.alert('Error', 'Please provide a valid location.');
          }
        }
      } else {
        setLoading(false);
        Alert.alert(
          'Attendance Window',
          'You can mark your attendance from 15 minutes prior to the course start time until 15 minutes after the course end time.'
        );
      }
    } catch (error) {
      console.log('error', error);
      setLoading(false);
      // Enhanced error handling
      if (error.response?.status === 400) {
        Alert.alert('Location Error', 'Your location does not match the required venue location.');
      } else {
        Alert.alert('Error', 'Something went wrong while marking attendance. Please try again.');
      }
    }
  };



  const handleHideQRCode = () => {
    setShowQRCode(false);
  };

  const hideBookingModal = () => {
    setShowBookingModal(false);
  };

  const scrollViewRef = useRef();
  const navigation = useNavigation();

  //console.log("userrr in dashbord",user)

  useEffect(() => {
    getUserDetails();
    googleEvents();
  }, [selectedDate, bookingData, calendarLinked]);

  const scrollToCard = () => {
    // You can adjust yOffset according to your UI
    const yOffset = 500; // Adjust this value based on your layout
    scrollViewRef.current.scrollTo({y: yOffset, animated: true});
  };

  const googleEvents = async () => {
    try {
      setLoading(true);

      console.log('selected date in dashboard', selectedDate);

      const payload = {
        date: selectedDate,
        id: user?.data?._id,
      };

      const result = await axios.post(`${API}/api/coach/dashboard`, payload, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${user?.data?.token}`,
        },
      });

      if (!result.error) {
        setBookingList(result?.data);
        setLoading(false);

        return true;
      }
      if (!result) {
        window.location.reload();
      }
    } catch (error) {
      console.log(error, 'error');
    }
  };

  const handleDateSelectDay = date => {
    setLoading(true);
    setSelectedDate(date.dateString);
    console.log('inside date select', showEvent);
    setExpandedIndex(null);
    //setShowCalendar(false)
  };

  const formatTimeDetails = time => {
    //console.log("format time", time)
    if (time) {
      const [hours, minutes] = time.split(':');

      // Convert hours to 12-hour format
      let hours12 = parseInt(hours, 10) % 12;
      hours12 = hours12 === 0 ? 12 : hours12;

      // Determine whether it's AM or PM
      const period = parseInt(hours, 10) < 12 ? 'AM' : 'PM';

      // Format the time in 12-hour format
      const time12 = `${hours12}:${minutes} ${period}`;

      return time12;
    }
  };

  const calculateDuration = (start, end) => {
    const startTime = start;
    const endTime = end;
    //console.log("sdhfd",start,end)

    const format = 'hh:mm A'; // 12-hour format with AM/PM
    const startMoment = moment(startTime, format);
    const endMoment = moment(endTime, format);

    // Calculate the difference in minutes
    const durationMinutes = endMoment.diff(startMoment, 'minutes');

    // Convert minutes to hours with decimal points
    const durationHours = (durationMinutes / 60).toFixed(1);

    // // Calculate the duration
    // const duration = moment.duration(endMoment.diff(startMoment));

    // // Get the duration in hours and minutes
    // const durationHours = duration.hours();
    //console.log(`Duration: ${durationHours} hours`)
    return durationHours;
    //return parseFloat(durationHours)
  };



  const getDetails = async item => {
    console.log('item', item);
    try {
      const response = await axios.get(`${API}/api/booking/${item?.id}`, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${user?.data?.token}`,
        },
      });
      //const response = await axios.get(`${API}/api/booking/65df1b1ff8b4d1ad33ec4be7`)
      console.log('responsee', response?.data?.data);
      let data1 = response?.data;
      if (data1 == []) {
        return;
      }
      //navigation.navigate('CourseCreate', { source: 'dashboard', listing: data1 })
      setPageName('Bookings');
      navigation.navigate('BookingDetails', {booking: response?.data});
      //setBookingData(bookingData)
    } catch (error) {
      console.log('errorrr', error);
    }
  };

  const renderClassEvents = () => {
    //console.log("booking descript", classEvents)

    const formattedDate = moment(selectedDate).format('DD MMMM, YYYY');
    //console.log(formattedDate);

    let capitalizedStr;
    const str = classEvents != [] ? classEvents?.name : '';
    if (str !== '') {
      capitalizedStr = str
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
      //console.log(capitalizedStr);
    }

    return (
      <View
        style={{
          padding: 15,
          backgroundColor: 'white',
          borderRadius: 10,
          marginTop: 10,
          marginBottom: 10,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.1,
          shadowRadius: 3,
          elevation: 2,
        }}>
        <View style={{flexDirection: 'row'}}>
          <View
            style={{
              width: 90,
              height: 90,
              marginRight: 15,
              borderRadius: 8,
              overflow: 'hidden',
              backgroundColor: '#F3F4F6',
            }}>
            <Image
              source={{uri: classEvents?.image && classEvents?.image}}
              style={{
                width: '100%',
                height: '100%',
                resizeMode: 'cover',
              }}
            />
          </View>

          <View
            style={{
              flex: 1,
              flexDirection: 'column',
              justifyContent: 'space-between',
            }}>
            <Text style={styles.eventTitle}>
              {capitalizedStr}
            </Text>

            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginTop: 5,
              }}>
              <Icon name="clock-o" size={16} color="#6B7280" />
              <Text
                style={{
                  color: '#3B82F6',
                  fontSize: 14,
                  fontWeight: '500',
                  marginLeft: 8,
                  fontFamily:
                    'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                }}>{`${formatTimeDetails(
                classEvents?.startTime,
              )} - ${formatTimeDetails(classEvents?.endTime)}`}</Text>
            </View>

            <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 5}}>
              <Icon name="map-marker" size={16} color="#6B7280" />
              <Text
                style={{
                  color: '#6B7280',
                  fontSize: 14,
                  marginLeft: 8,
                  fontFamily:
                    'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                }}>
                {classEvents && classEvents?.facility?.toUpperCase()}
              </Text>
            </View>
          </View>

          <View>
            <View
              style={{
                flexDirection: 'column',
                alignItems: 'flex-end',
              }}>
              <Text
                style={{
                  color: '#6B7280',
                  fontSize: 13,
                  fontFamily:
                    'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                }}>
                {formattedDate}
              </Text>
              <View style={{
                backgroundColor: '#F0FDF4',
                paddingHorizontal: 8,
                paddingVertical: 3,
                borderRadius: 12,
                marginTop: 5,
              }}>
                <Text
                  style={{
                    color: '#22C55E',
                    fontSize: 13,
                    fontWeight: '500',
                    fontFamily:
                      'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                  }}>{`${classEvents?.bookings?.length} Bookings`}</Text>
              </View>
            </View>
          </View>
        </View>

        {eventType === 'course' && (
          <View
            style={{
              flexDirection: 'column',
              marginTop: 15,
              borderTopWidth: 1,
              borderTopColor: '#F3F4F6',
              paddingTop: 15,
            }}>
            {/* Debug console logs for course type */}
            {console.log('=== COURSE DEBUG INFO ===')}
            {console.log('eventType:', eventType)}
            {console.log('classEvents:', classEvents)}
            {console.log('classEvents.bookings:', classEvents?.bookings)}
            {console.log('classEvents.bookings[0]:', classEvents?.bookings?.[0])}
            {console.log('classEvents.bookings[0].status:', classEvents?.bookings?.[0]?.status)}
            {console.log('Button disabled condition (status !== upcoming):', classEvents?.bookings?.[0]?.status !== 'upcoming')}
            {console.log('=========================')}
            <TouchableOpacity
              style={[
                styles.buttonOutlineSuccess,
                {marginBottom: 10, marginRight: 0},
                (classEvents?.bookings[0]?.status !== 'upcoming' ||
                 classEvents?.bookings[0]?.coachAttendance === 'present') && styles.buttonDisabled
              ]}
              onPress={() =>
                markCoachAttendance({
                  startTime: classEvents.startTime,
                  endTime: classEvents.endTime,
                  courseId: classEvents.id,
                  maxGroupSize: classEvents.maxGroupSize,
                  bookingId:
                    classEvents.maxGroupSize === 1
                      ? classEvents?.bookings[0]?.id
                      : '',
                  classId:
                    classEvents.maxGroupSize === 1
                      ? classEvents?.bookings[0]?.classId
                      : '',
                  playerId:
                    classEvents.maxGroupSize === 1
                      ? classEvents?.bookings[0]?.playerId
                      : '',
                })
              }
              disabled={classEvents?.bookings[0]?.status !== 'upcoming' ||
                       classEvents?.bookings[0]?.coachAttendance === 'present'}>
              <Text style={[
                styles.buttonOutlineText,
                styles.buttonOutlineTextSuccess,
                (classEvents?.bookings[0]?.status !== 'upcoming' ||
                 classEvents?.bookings[0]?.coachAttendance === 'present') && styles.buttonTextDisabled
              ]}>
                {classEvents?.bookings[0]?.coachAttendance === 'present' ? 'Attendance Marked' : 'Mark your Attendance'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.buttonOutlinePrimary,
                {marginRight: 0},
                classEvents?.bookings[0]?.status !== 'upcoming' && styles.buttonDisabled
              ]}
              onPress={() =>
                checkAttendanceShowTime({
                  startTime: classEvents.startTime,
                  endTime: classEvents.endTime,
                  courseId: classEvents.id,
                  maxGroupSize: classEvents.maxGroupSize,
                  bookingId:
                    classEvents.maxGroupSize === 1
                      ? classEvents?.bookings[0]?.id
                      : '',
                  classId:
                    classEvents.maxGroupSize === 1
                      ? classEvents?.bookings[0]?.classId
                      : '',
                  playerId:
                    classEvents.maxGroupSize === 1
                      ? classEvents?.bookings[0]?.playerId
                      : '',
                })
              }
              disabled={classEvents?.bookings[0]?.status !== 'upcoming'}>
              <Text style={[
                styles.buttonOutlineText,
                styles.buttonOutlineTextPrimary,
                classEvents?.bookings[0]?.status !== 'upcoming' && styles.buttonTextDisabled
              ]}>
                Mark Player Attendance
              </Text>
            </TouchableOpacity>
          </View>
        )}

        <View style={{marginTop: 20}}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#1F2937',
            marginBottom: 10,
            fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
          }}>
            Bookings
          </Text>

          {classEvents?.bookings?.length > 0 ? (
            <>
              {classEvents?.bookings?.map((item, index) => {
                return (
                  <TouchableOpacity
                    key={index}
                    onPress={() => getDetails(item)}
                    style={{
                      backgroundColor: '#F9FAFB',
                      borderRadius: 8,
                      padding: 12,
                      marginBottom: 10,
                      borderLeftWidth: 3,
                      borderLeftColor: item.status === 'cancelled' ? '#EF4444' : '#22C55E',
                    }}>
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                      }}>
                      <View>
                        <View style={{flexDirection: 'row', alignItems: 'center'}}>
                          <Text
                            style={{
                              color: '#1F2937',
                              fontSize: 16,
                              fontWeight: '600',
                              marginRight: 8,
                              fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                            }}>
                            {item?.name}
                          </Text>
                          <Text
                            style={[
                              styles.badge,
                              item.status === 'cancelled' ? styles.badgeDanger : styles.badgeSuccess
                            ]}>
                            {item.status}
                          </Text>
                        </View>

                        {eventType === 'class' && (
                          <Text
                            style={{
                              color: '#3B82F6',
                              fontSize: 14,
                              marginTop: 4,
                              fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                            }}>
                            {`${formatTimeDetails(item?.startTime)} - ${formatTimeDetails(item?.endTime)}`}
                          </Text>
                        )}
                      </View>

                      {eventType !== 'class' && (
                        <TouchableOpacity
                          style={{
                            backgroundColor: '#EFF6FF',
                            paddingVertical: 6,
                            paddingHorizontal: 12,
                            borderRadius: 6,
                          }}
                          onPress={() => getDetails(item)}>
                          <Text
                            style={{
                              color: '#3B82F6',
                              fontSize: 14,
                              fontWeight: '500',
                              fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                            }}>
                            View Details
                          </Text>
                        </TouchableOpacity>
                      )}
                    </View>

                    {eventType === 'course' && (
                      <View
                        style={{
                          flexDirection: 'row',
                          marginTop: 8,
                          paddingTop: 8,
                          borderTopWidth: 1,
                          borderTopColor: '#E5E7EB',
                        }}>
                        <View style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          marginRight: 15,
                        }}>
                          <Text style={{
                            color: '#6B7280',
                            fontSize: 14,
                            fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                          }}>
                            Coach:
                          </Text>
                          <Text
                            style={{
                              color: item.coachAttendance === 'present' ? '#22C55E' :
                                    item.coachAttendance === 'absent' ? '#EF4444' : '#6B7280',
                              marginLeft: 5,
                              fontSize: 14,
                              fontWeight: '500',
                              fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                            }}>
                            {item.coachAttendance === 'present' ? 'Present' :
                             item.coachAttendance === 'absent' ? 'Absent' : 'Not marked'}
                          </Text>
                        </View>

                        <View style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                        }}>
                          <Text style={{
                            color: '#6B7280',
                            fontSize: 14,
                            fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                          }}>
                            Player:
                          </Text>
                          <Text
                            style={{
                              color: item.attendance === 'present' ? '#22C55E' :
                                    item.attendance === 'absent' ? '#EF4444' : '#6B7280',
                              marginLeft: 5,
                              fontSize: 14,
                              fontWeight: '500',
                              fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                            }}>
                            {item.attendance === 'present' ? 'Present' :
                             item.attendance === 'absent' ? 'Absent' : 'Not marked'}
                          </Text>
                        </View>
                      </View>
                    )}
                  </TouchableOpacity>
                );
              })}
            </>
          ) : (
            <>
              <Text style={styles.noBookingsText}>
                No Bookings Found
              </Text>
            </>
          )}
        </View>

        {/* Cancel booking button */}
        {classEvents?.maxGroupSize == 1 && (
          <TouchableOpacity
            onPress={() => {
              setBookingData({...classEvents, selectedDate});
              setShowBookingModal(true);
            }}
            style={[styles.buttonDanger, {marginTop: 15}]}>
            <Text style={styles.buttonText}>Cancel Booking</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderEventList = () => {
    const handleListClick = (data, index) => {
      //console.log('inside list click', data);
      // Don't do anything for events of type 'event' (breaks)
      if (data?.type === 'event') {
        return;
      }

      if (data?.type === 'course') {
        setEventType('course');
        setClassEvents(data);
        setShowEvent(!showEvent);
      }
      if (data?.type === 'class') {
        setEventType('class');
        setClassEvents(data);
        setShowEvent(!showEvent);
      }
      setExpandedIndex(index === expandedIndex ? null : index);
      scrollToCard();
    };

    return (
      <View style={{paddingHorizontal: 5}}>
        {bookingList?.length == 0 || bookingList?.length == undefined ? (
          <View style={{
            backgroundColor: 'white',
            borderRadius: 10,
            padding: 20,
            marginHorizontal: 15,
            alignItems: 'center',
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 3,
          }}>
            <Icon name="calendar-times-o" size={40} color="#9CA3AF" />
            <Text style={styles.noBookingsText}>
              No bookings found for this date
            </Text>
            <TouchableOpacity
              style={styles.buttonPrimary}
              onPress={() => {
                googleEvents();
              }}>
              <Text style={styles.buttonText}>Refresh</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <>
            {bookingList?.map((item, index) => {
              const formattedDate = moment(selectedDate).format('DD MMMM, YYYY');
              const duration = calculateDuration(item?.startTime, item?.endTime);

              let capitalizedStr;
              const str = item && item?.name;
              capitalizedStr = str
                .split(' ')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                .join(' ');

              // Determine badge style based on event type
              let badgeStyle = styles.badgePrimary;
              if (item?.type === 'class') {
                badgeStyle = styles.badgeSuccess;
              } else if (item?.type === 'event' || item?.type === 'break') {
                badgeStyle = styles.badgePrimary;
              } else if (item?.type === 'course') {
                badgeStyle = styles.badgeWarning;
              }

              return (
                <View
                  key={index}
                  style={expandedIndex === index ? styles.eventCardExpanded : styles.eventCard}>
                  <TouchableOpacity
                    onPress={() => handleListClick(item, index)}
                    disabled={item?.type === 'event'}>
                    <View>
                      {/* Event Header */}
                      <View style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        marginBottom: 10,
                      }}>
                        <Text style={styles.eventTitle}>
                          {capitalizedStr}
                        </Text>
                        <Text style={[styles.badge, badgeStyle]}>
                          {item?.type === 'event' ? 'Break' : item?.type}
                        </Text>
                      </View>

                      {/* Event Details */}
                      <View style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                      }}>
                        <View style={{flex: 1}}>
                          {/* Time */}
                          <View style={styles.eventDetailRow}>
                            <Icon
                              name="clock-o"
                              size={16}
                              color="#3B82F6"
                              style={styles.eventDetailIcon}
                            />
                            <Text style={styles.eventTime}>
                              {`${formatTimeDetails(item?.startTime)} - ${formatTimeDetails(item?.endTime)}`}
                            </Text>
                          </View>

                          {/* Duration */}
                          <View style={styles.eventDetailRow}>
                            <Icon
                              name="hourglass-half"
                              size={14}
                              color="#3B82F6"
                              style={styles.eventDetailIcon}
                            />
                            <Text style={styles.eventTime}>
                              {duration && `${duration} ${duration == 1 ? 'hour' : 'hours'}`}
                            </Text>
                          </View>

                          {/* Date */}
                          <View style={styles.eventDetailRow}>
                            <Icon
                              name="calendar"
                              size={14}
                              color="#3B82F6"
                              style={styles.eventDetailIcon}
                            />
                            <Text style={styles.eventTime}>
                              {formattedDate}
                            </Text>
                          </View>
                        </View>

                        {/* Actions */}
                        <View style={{alignItems: 'flex-end'}}>
                          <TouchableOpacity
                            style={styles.refreshButton}
                            onPress={() => {
                              googleEvents();
                            }}>
                            <Icon
                              name="refresh"
                              size={14}
                              color="#22C55E"
                            />
                            <Text style={styles.refreshButtonText}>
                              Refresh
                            </Text>
                          </TouchableOpacity>

                          {/* Only show dropdown arrow for events that have details (not for 'event' type) */}
                          {item?.type !== 'event' && (
                            <Icon
                              name={index === expandedIndex ? 'chevron-up' : 'chevron-down'}
                              size={20}
                              color="#6B7280"
                              style={{marginTop: 8}}
                            />
                          )}
                        </View>
                      </View>
                    </View>
                  </TouchableOpacity>

                  {index === expandedIndex && renderClassEvents()}
                </View>
              );
            })}
          </>
        )}
      </View>
    );
  };

  return (
    <ScreenContainer>
      <ScrollView ref={scrollViewRef}>
        <View style={styles.header}>
          {/* Date picker section */}
          <View style={{paddingTop: Platform.OS === 'ios' ? 0 : 12, paddingBottom: 4}}>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                width: '100%',
                alignItems: 'center',
              }}>
              <TouchableOpacity
                style={[styles.datePickerButton, {flex: 1, marginRight: 12}]}
                onPress={() => setShowCalendar(!showCalendar)}>
                <Icon name="calendar" size={20} color="#3B82F6" />
                <Text style={styles.datePickerText}>
                  {moment(selectedDate).format('Do MMMM YYYY')}
                </Text>
                <Icon
                  name={showCalendar ? "chevron-up" : "chevron-down"}
                  size={16}
                  color="#3B82F6"
                  style={{marginLeft: 'auto'}}
                />
              </TouchableOpacity>

              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <TouchableOpacity
                  style={styles.navButton}
                  onPress={() => {
                    const previousDate = moment(selectedDate)
                      .subtract(1, 'day')
                      .format('YYYY-MM-DD');
                    setSelectedDate(previousDate);
                  }}>
                  <Icon name="angle-left" size={20} color="#4B5563" />
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.navButton}
                  onPress={() => {
                    const nextDate = moment(selectedDate)
                      .add(1, 'day')
                      .format('YYYY-MM-DD');
                    setSelectedDate(nextDate);
                  }}>
                  <Icon name="angle-right" size={20} color="#4B5563" />
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.todayButton}
                  onPress={() => {
                    setSelectedDate(moment().format('YYYY-MM-DD'));
                    setCalendarKey(prevKey => prevKey + 1);
                  }}>
                  <Text style={styles.todayButtonText}>
                    Today
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>

        {/* Calendar */}
        {showCalendar && (
          <View style={styles.calendarContainer}>
            <Calendar
              key={calendarKey}
              current={selectedDate}
              onDayPress={handleDateSelectDay}
              style={styles.calendar}
              enableSwipeMonths={true}
              theme={{
                todayTextColor: '#3B82F6',
                arrowColor: '#3B82F6',
                dotColor: '#3B82F6',
                selectedDayBackgroundColor: '#3B82F6',
              }}
              markedDates={{
                [selectedDate]: {
                  selected: true,
                  selectedColor: '#3B82F6',
                  dotColor: 'white',
                },
              }}
            />
          </View>
        )}

        <View style={styles.body}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionIconContainer}>
              <Icon name="calendar-check-o" size={18} color="white" />
            </View>
            <Text style={styles.sectionTitle}>
              Upcoming Events
            </Text>
          </View>

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#3B82F6" />
              <Text style={{
                marginTop: 10,
                color: '#6B7280',
                fontSize: 16,
                fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e'
              }}>
                Loading your schedule...
              </Text>
            </View>
          ) : (
            <>
              {renderEventList()}

              <QRCodeModal
                qrCodeItem={qrCodeItem}
                base64Logo={base64Logo}
                visible={showQRCode}
                onClose={handleHideQRCode}
              />

              <CancelBookingModal
                setBookingCancelled={setBookingCancelled}
                bookingData={bookingData}
                selectedDate={selectedDate}
                visible={showBookingModal}
                onClose={hideBookingModal}
              />
            </>
          )}
        </View>
      </ScrollView>
    </ScreenContainer>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    paddingVertical: Platform.OS === 'ios' ? 8 : 12,
    paddingHorizontal: 15,
    borderWidth: 0,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    marginBottom: 10,
  },
  body: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  calendar: {
    borderRadius: 10,
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    padding: 5,
  },
  selectedDateText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 5,
  },

  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 12,
    marginBottom: 15,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  sectionIconContainer: {
    backgroundColor: '#3B82F6',
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
    textAlign: 'left',
  },
  eventCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 15,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#F3F4F6',
  },
  eventCardExpanded: {
    backgroundColor: '#EFF6FF',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 15,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#DBEAFE',
    borderLeftWidth: 4,
    borderLeftColor: '#3B82F6',
  },
  eventTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
    textAlign: 'left',
    flex: 1,
    marginRight: 10,
  },
  eventTime: {
    fontSize: 14,
    color: '#4B5563',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
    textAlign: 'left',
  },
  eventLocation: {
    fontSize: 14,
    color: '#4B5563',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
    textAlign: 'left',
  },
  eventDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  eventDetailIcon: {
    marginRight: 8,
    width: 16,
    textAlign: 'center',
  },
  refreshButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0FDF4',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#22C55E',
  },
  refreshButtonText: {
    color: '#22C55E',
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
    textAlign: 'center',
  },
  buttonPrimary: {
    backgroundColor: '#3B82F6',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  buttonSuccess: {
    backgroundColor: '#22C55E',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  buttonDanger: {
    backgroundColor: '#EF4444',
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  buttonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
    textAlign: 'center',
  },
  buttonOutlinePrimary: {
    borderColor: '#3B82F6',
    borderWidth: 1,
    backgroundColor: '#EFF6FF',
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  buttonOutlineSuccess: {
    borderColor: '#22C55E',
    borderWidth: 1,
    backgroundColor: '#F0FDF4',
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  buttonOutlineText: {
    fontSize: 14,
    fontWeight: '500',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
    textAlign: 'center',
    flexShrink: 1,
  },
  buttonOutlineTextPrimary: {
    color: '#3B82F6',
  },
  buttonOutlineTextSuccess: {
    color: '#22C55E',
  },
  buttonDisabled: {
    opacity: 0.5,
    backgroundColor: '#F3F4F6',
  },
  buttonTextDisabled: {
    color: '#9CA3AF',
  },
  calendarContainer: {
    marginHorizontal: 15,
    marginBottom: 15,
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  datePickerText: {
    fontSize: 16,
    color: '#1F2937',
    marginLeft: 10,
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
    textAlign: 'left',
    flex: 1,
  },
  navButton: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
    backgroundColor: 'white',
    marginRight: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  todayButton: {
    backgroundColor: '#3B82F6',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  todayButtonText: {
    color: 'white',
    fontSize: 15,
    fontWeight: '500',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
    textAlign: 'center',
  },
  noBookingsText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginVertical: 20,
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
    alignSelf: 'center',
  },
  // qr code
  alertContainer: {
    top: 0,
    bottom: 500,
    left: 0,
    right: 200,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  alert: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
  },
  badge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 16,
    fontSize: 12,
    fontWeight: '600',
    overflow: 'hidden',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
    textAlign: 'center',
    minWidth: 70,
    alignItems: 'center',
    justifyContent: 'center',
  },
  badgeSuccess: {
    backgroundColor: '#F0FDF4',
    color: '#16A34A',
    borderWidth: 1,
    borderColor: '#22C55E',
  },
  badgeDanger: {
    backgroundColor: '#FEF2F2',
    color: '#DC2626',
    borderWidth: 1,
    borderColor: '#EF4444',
  },
  badgePrimary: {
    backgroundColor: '#EFF6FF',
    color: '#2563EB',
    borderWidth: 1,
    borderColor: '#3B82F6',
  },
  badgeWarning: {
    backgroundColor: '#FEF3C7',
    color: '#D97706',
    borderWidth: 1,
    borderColor: '#FBBB04',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  button: {
    padding: 10,
    borderRadius: 5,
  },
});

export default Dashboard;