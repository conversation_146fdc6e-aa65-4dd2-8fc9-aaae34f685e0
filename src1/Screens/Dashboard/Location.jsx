import { StyleSheet, Text, View,Button,PermissionsAndroid  } from 'react-native'
import React, { useState } from 'react'
import Geolocation from '@react-native-community/geolocation';
import { watchPosition } from 'react-native-geolocation-service';

const Location = () => {
//console.log("geolocation", Geolocation)
const[coachLocation,setCoachLocation] = useState({})


const requestLocationPermission = async () => {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        {
          title: 'Geolocation Permission',
          message: 'Can we access your location?',
          buttonPositive: 'OK',
        },
      );
      console.log('granted', granted);
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    } catch (err) {
      console.error('Error requesting location permission:', err);
      return false;
    }
  };
  
  const getUserLocation = async () => {
    const permissionGranted = await requestLocationPermission();
    if (!permissionGranted) {
      console.log('Location permission denied');
      alert("You wont be able to mark the attendance, if location is denied")
      return;
    }

    Geolocation.getCurrentPosition(
      position => {
        const { latitude, longitude } = position.coords;
        console.log('Latitude:', latitude, 'Longitude:', longitude);
        const obj = {Latitude :latitude, Longitude: longitude}
             console.log("objjjjj", obj)
             setCoachLocation(obj)
        // Do something with the location data, like storing it in state
      },
      error => {
        console.log('Error getting location:', error.message);
        if (error.code === 3) {
          // Timeout error, retry or inform the user
          console.log('Timeout error: Please try again.');
        }
      },
      { timeout: 60000, maximumAge: 1000 } // Increased timeout to 60 seconds
    );
  };

  
  return (
    <View>
      <Text>Location</Text>
      <Button title="Get Location" onPress={getUserLocation} />
    </View>
  )
}

export default Location

const styles = StyleSheet.create({})




// if does not work for ios, follow this 
// If you don't have an Info.plist file in your iOS project, it's possible that you're using a newer version of React Native where the default project setup differs slightly, or your project might not have been initialized properly.

// Here's how you can proceed to ensure location permissions for iOS:

// Create Info.plist file (if missing):If your project doesn't have an Info.plist file, you'll need to create one. You can generate it manually in the ios directory of your project. To do this, follow these steps:a. Navigate to your project's ios directory.b. Inside the ios directory, create a new file named Info.plist.c. Add the necessary keys for location permissions in this Info.plist file as mentioned earlier.
// Link Info.plist with Xcode (if necessary):If the Info.plist file isn't automatically linked with Xcode, you'll need to add it manually to your Xcode project:a. Open your project in Xcode by double-clicking the .xcworkspace file located in the ios directory.b. In Xcode, right-click on the ios folder in the project navigator and select "Add Files to 'YourProjectName'".c. Navigate to and select the Info.plist file you created, and click "Add".d. Verify that the Info.plist file now appears in the project navigator. If not, you might need to manually drag it into the project navigator.
// Add location permission keys:Open the Info.plist file in Xcode and add the necessary keys for location permissions as mentioned earlier.
// Provide appropriate descriptions:Make sure to provide clear and accurate descriptions for location permission keys to ensure compliance with App Store guidelines and a good user experience.
// Once you've added the Info.plist file with the required keys for location permissions and linked it with your Xcode project, you should be able to request and access location permissions on iOS.


{/* <key>NSLocationWhenInUseUsageDescription</key>
<string>Your message requesting access to location while the app is in use.</string>
<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
<string>Your message requesting access to location when the app is in use and in the background.</string>
<key>NSLocationAlwaysUsageDescription</key>
<string>Your message requesting access to location at all times.</string> */}


{/* <key>NSBluetoothAlwaysUsageDescription</key>
<string>Your message requesting access to Bluetooth at all times.</string>
<key>NSBluetoothPeripheralUsageDescription</key>
<string>Your message requesting access to use Bluetooth peripherals.</string> */}





