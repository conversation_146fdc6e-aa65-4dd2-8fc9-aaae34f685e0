import React, { useState,useEffect } from 'react';
import { View, Modal, Text, TouchableOpacity, StyleSheet, Button, ActivityIndicator,Alert } from 'react-native';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import moment from 'moment-timezone';
import axios from 'axios';
import {API} from '@env';
import {useAuth} from '../../components/Auth/AuthContext';


const CancelBookingModal = ({bookingData,visible,onClose,setBookingCancelled}) => {
const [selectedBookings, setSelectedBookings] = useState([]);
const [finalBookingData,setFinalBookingData] = useState([]);
const [processing, setProcessing] = useState(false);

const {user} = useAuth();

useEffect(() => {
    filterDates(); 
  
  }, [bookingData])

  useEffect(() => {
    if (!visible) {
        // Reset selected bookings when modal is closed
        setSelectedBookings([]);
    }
}, [visible]);

// console.log("booking dtaa", bookingData)

    const filterDates = () => {
        if (
            bookingData &&
            bookingData.bookings &&
            bookingData.bookings.length > 0
        ) {
          let upcomingBookings = bookingData?.bookings.filter(
            (x) => (x.status === "upcoming") && x?.attendance === "NA" && x?.coachAttendance === "NA"
          );
          upcomingBookings = upcomingBookings?.filter((x) => {
            let endDate = moment
              .tz(moment(bookingData.selectedDate), "Asia/Kolkata")
              .format()
              .split("T")[0];
            endDate = moment
              .tz(moment(`${endDate}T${x.endTime}`), "Asia/Kolkata")
              .format();
            const currentDate = moment.tz(moment(), "Asia/Kolkata").format();
            console.log(currentDate, endDate);
            return moment(currentDate).isBefore(moment(endDate));
          });
          console.log(upcomingBookings);
          setFinalBookingData([...upcomingBookings]);
        }
      };


    const handleCheckboxToggle = (booking) => {
      const index = selectedBookings.findIndex(
          (b) => b.bookingId === booking.bookingId && b.classId === booking.classId
      );
  
      if (index === -1) {
          // If the booking is not already selected, add it to the selected bookings array
          setSelectedBookings([...selectedBookings, booking]);
      } else {
          // If the booking is already selected, remove it from the selected bookings array
          const updatedBookings = [...selectedBookings];
          updatedBookings.splice(index, 1);
          setSelectedBookings(updatedBookings);
      }
  };
  

      const renderBookings = () => {
        if (!finalBookingData) return null;
    
        return (
            <View>
                <Text style={{fontSize:20,color:'black',marginBottom:10}}>{"Select the classes to cancel"}</Text>
            
    <View>
        {finalBookingData.map((item, index) => {
            const date = new Date(
                bookingData.selectedDate
            ).toLocaleDateString("en-IN", {
                day: "numeric",
                month: "short",
                year: "numeric",
            });
            return (
                <TouchableOpacity
                    key={index}
                    style={styles.bookingItem}
                    onPress={() => handleCheckboxToggle({ bookingId: item.id, classId: item.classId})}
                >
                    <FontAwesome
                        name={selectedBookings.some(booking => booking.bookingId === item.id && booking.classId === item.classId) ? 'check-square-o' : 'square-o'}
                        size={20}
                        color={selectedBookings.some(booking => booking.bookingId === item.id && booking.classId === item.classId) ? 'green' : 'black'}
                    />
                    <Text style={styles.bookingText}>
                        {`${date} (${item.startTime} - ${item.endTime})`}
                    </Text>
                </TouchableOpacity>
            );
        })}
    </View>
 

            </View>
        );
    };

  

const cancelEvents = async (events) => {
  try {
    setProcessing(true);
      
      const promises = events.map(async (event, index) => {
          try {
              const result = await axios.get(`${API}/api/booking/${event?.bookingId}`, {
                  headers: {
                      'Content-Type': 'application/json',
                      Authorization: `Bearer ${user?.data?.token}`,
                  }
              });

              if (result && result.data && result.data.classes && result.data.classes.length > 0) {
                  const filteredItem = result.data.classes.filter((item) => item._id === event.classId);
                  console.log("filtered item", filteredItem);

                  let obj = {
                      status: "cancelled",
                      sender: "coach",
                      classes: filteredItem,
                  };

                  console.log("obj", obj);

                  const result2 = await axios.post(
                      `${API}/api/booking/cancel/${event.bookingId}`,
                      { ...obj },
                      {
                          headers: {
                              "Content-Type": "application/json",
                              Authorization: `Bearer ${user?.data?.token}`,
                          },
                      }
                  );

                  console.log(result2?.data, "result2222");

                  // Return the result2 to be captured by Promise.all()
                  return result2?.data;
              } else {
                  alert("Booking not found");
                  onClose();
                  return null;
              }
          } catch (error) {
              console.log("Error in processing event", error);
              
              return null;
          }
      });

      const responses = await Promise.all(promises);

      console.log("result of reponses",responses); // Contains all responses from axios.post

      responses.forEach((response) => {
          if (response) {
              // Event cancelled successfully
              console.log("inside responses")
              setBookingCancelled(true)
              setTimeout(()=>{
                setProcessing(false);
                onClose(); 
               alert( 'Event Cancelled sucessfully'); 
             
              },1000)
             
              
          }
      });

      
      setProcessing(false);
      onClose();
  } catch (error) {
      console.log("error", error);
     
      setProcessing(false);
      alert("An error occurred");
      onClose();
  }
};


  return (

<Modal
    animationType="fade"
    transparent={true}
    visible={visible}
    onRequestClose={onClose}
>
    <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
            {finalBookingData && finalBookingData.length > 0 ? (
                <>
                    {renderBookings()}
                    <View style={{ flexDirection: 'row', alignSelf: "flex-end", marginTop: 10 }}>
                        <TouchableOpacity onPress={onClose} style={[styles.button, { backgroundColor: 'maroon' }]}>
                            <Text style={[styles.buttonText]}>Close</Text>
                        </TouchableOpacity>

                        <TouchableOpacity onPress={() => {
                            cancelEvents(selectedBookings);
                        }} style={[styles.button, { backgroundColor: 'green' }]}>

{processing ? (
            <ActivityIndicator size="large" color="white" />
          ) : (
                            <Text style={styles.buttonText}>Confirm</Text>

                          )}
                        </TouchableOpacity>


                    </View>
                </>
            ) : (
              <View>
                <Text style={{ fontSize: 16, color: 'black',marginVertical:10 }}>No Bookings Found</Text>
                <Button title="Close" onPress={()=>onClose()} color="red" style={{alignSelf:'flex-end',width:20}} />
                </View>
            )}
        </View>
    </View>
</Modal>

  )
}



const styles = StyleSheet.create({
    modalContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalContent: {
      backgroundColor: 'white',
      padding: 20,
      borderRadius: 10,
      width: '80%',
    },
    bookingItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: 5,
      
    },
    bookingText: {
      marginLeft: 10,
      fontSize:15,
      fontWeight:"500",
      color:"#6B7280",
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      marginTop: 20,
      
    },
    button: {
      marginLeft: 10,
      borderWidth:0,
      paddingHorizontal:10,
      paddingVertical:5,
      borderRadius:5
    },
    buttonText: {
      color: 'white',
      fontSize:15,
      fontWeight:"500",
    },
  });
  

export default CancelBookingModal