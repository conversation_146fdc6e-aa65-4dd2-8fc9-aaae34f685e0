import React, { useState } from 'react';
import { View, Modal, Text, TouchableOpacity, StyleSheet } from 'react-native';
import QRCode from 'react-native-qrcode-svg'; // Assuming you are using this library for QRCode generation

const QRCodeModal = ({ qrCodeItem, base64Logo, visible, onClose }) => {
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
     <Text style={{fontSize:16,color:'black',marginBottom:10,fontWeight:'500'}}>Scan the QRCode to mark as present.</Text>       
 {/* did not apply yet,If you are using React Native 0.60.+ go to the folder your-project/ios and run pod install, and you're done. */}
          <QRCode
            value={JSON.stringify(qrCodeItem)} 
            size={150}
            color="black"
            backgroundColor="white"
            // logo={{uri: base64Logo}}
            logoSize={20}
            logoBackgroundColor='transparent'
          />
          <TouchableOpacity onPress={onClose} style={styles.button}>
            <Text style={styles.buttonText}>OK</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};



const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
  },
  button: {
    marginTop: 20,
    padding: 7,
    backgroundColor: 'blue',
    borderRadius: 5,
  },
  buttonText: {
    fontSize: 15,
    fontWeight: 'bold',
    color:'white',
    fontFamily:'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
});

export default QRCodeModal;
