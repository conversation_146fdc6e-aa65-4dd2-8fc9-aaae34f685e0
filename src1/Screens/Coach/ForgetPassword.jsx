import React, {useState} from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  Alert,
  Image,
} from 'react-native';
import {useAuth} from '../../components/Auth/AuthContext';
import {API} from '@env';
import {useNavigation} from '@react-navigation/native';

const ForgetPassword = () => {
  const [email, setEmail] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const emailRegExp = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;

  const {setPageName, setIsHeader,setIsAuthenticated} = useAuth();
  const navigation = useNavigation();

  const handleSubmit = async () => {
    if (!emailRegExp.test(email)) {
      setErrorMessage('Please enter a valid email address.');
      return;
    }
    setErrorMessage('');
    try {
      const response = await fetch(
        `${API}/api/coach/requestResetPassword/${email}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );
      const data = await response.json();
      if (data.message) {
        Alert.alert(
          'Success',
          'Password reset link has been sent to your email.',
        );
        setPageName('Login');
        navigation.navigate('Login');
      } else {
        Alert.alert(
          'Error',
          data.error || 'Something went wrong. Please try again.',
        );
      }
    } catch (error) {
      Alert.alert(
        'Error',
        'Unable to send password reset link. Please try again later.',
      );
    }
  };
  return (
    <KeyboardAvoidingView
      style={{flex: 1}}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={styles.container}>
          <View style={styles.logoContainer}>
            <Image
              source={require('../../assets/MainKhelCoach.png')}
              alt="HeaderLogo"
              style={styles.headerLogo}
            />
          </View>
          <View style={styles.textContainer}>
            <Text style={[styles.title]}>Recover your account</Text>
          </View>
          <View style={styles.inputContainer}>
            <Text style={styles.labelEmail}>Email Address</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter your email"
              keyboardType="email-address"
              autoCapitalize="none"
              value={email}
              onChangeText={setEmail}
            />
            {errorMessage ? (
              <Text style={styles.errorText}>{errorMessage}</Text>
            ) : null}

            <View>
              <TouchableOpacity
                style={styles.signInButton}
                onPress={handleSubmit}>
                <Text style={styles.signInButtonText}>Submit</Text>
              </TouchableOpacity>
            </View>
          </View>
          <View style={styles.signupCon}>
            <Text style={styles.notamem}>
              <View>
                <Text style={{color: 'black'}}> Remember Password?</Text>
              </View>
              <TouchableOpacity
                onPress={() => {
                  setIsHeader(false);
                  setIsAuthenticated(false)
                  setPageName('Login');
                  navigation.navigate('Login');
                }}>
                <Text style={styles.signUpText}> LogIn</Text>
              </TouchableOpacity>
            </Text>
          </View>
        </View>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};
export default ForgetPassword;

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    flex: 1,
    paddingHorizontal: '5%',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    backgroundColor: '#fff',
    // margin: '3%',
    padding: '10%',
    //paddingHorizontal:'11%',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 1,
  },
  logoContainer: {
    marginBottom: '10%',
    borderWidth: 0,
  },
  headerLogo: {
    width: 200,
    height: 100,
    resizeMode: 'contain',
  },
  textContainer: {
    marginBottom: '5%',
  },
  title: {
    fontSize: 24,
    // fontWeight: 'bold',
    marginBottom: '7%',
    color: 'black',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  inputContainer: {
    width: '100%',
  },
  input: {
    marginBottom: '3%',
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 10,
    borderRadius: 5,
    width: '100%',
  },
  labelEmail: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: '2%',
    color: 'black',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  label: {
    fontSize: 14,
    fontWeight: 'bold',
    color: 'black',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    color: '#0EA5E9',
    marginBottom: '2%',
    fontWeight: '600',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  passwordRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: '3%', // Adjust spacing as needed
  },
  eyeIcon: {
    width: 24,
    height: 24,
  },
  signInButton: {
    backgroundColor: '#EF4444',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 100,
    height: 50,
    marginTop: 15,
  },
  signInButtonText: {
    color: '#ffffff',
    fontSize: 16,
    textAlign: 'center',
    fontWeight: '600',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  signupCon: {
    marginTop: '5%',
  },
  notamem: {
    fontSize: 20,
    alignItems: 'center',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  signUpText: {
    color: '#0EA5E9',
    fontWeight: '600',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  signUpLink: {
    textDecorationLine: 'underline',
  },
  otpButton: {
    backgroundColor: '#EF4444',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 300,
    // width:'100%',
    height: 50,
  },
  googleSignInButton: {
    flexDirection: 'row',
    backgroundColor: '#FAFBFCFF',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 15,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  googleSignInButtonText: {
    marginLeft: 10,
    color: '#000',
    fontSize: 16,
  },
  googleLogo: {
    width: 24,
    height: 24,
  },
});
