import {
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  View,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Linking,
  Alert,
  ActivityIndicator,
  Modal
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome';
import React, { useCallback, useEffect, useState } from 'react';
import { launchImageLibrary } from 'react-native-image-picker';
import { Form, useFormik } from 'formik';
import Svg, { Path } from 'react-native-svg';
import * as Yup from 'yup';
import axios from 'axios';
import { API } from '@env';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getLoginToken } from '../../helpers';
import { useAuth } from '../../components/Auth/AuthContext';
import { useRoute } from '@react-navigation/native';
import { Country, State, City } from 'country-state-city';
import CheckBox from '@react-native-community/checkbox';
import { RadioButton } from "react-native-paper";
import CustomRadioButton from '../../components/CustomRadioButton';
const KYCdetails = () => {
  const route = useRoute();
  const { showMessage } = route.params || { showMessage: false };

  const [loading, setLoading] = useState(false);
  const { user, updateUserDetails, logout, isLoggedin } = useAuth();

  const [documentImageFront, setDocumentImageFront] = useState('');
  const [documentImageBack, setDocumentImageBack] = useState('');
  const [isEditable, setIsEditable] = useState(false);
  const navigation = useNavigation();
  const [loginToken, setLoginToken] = useState('');
  const [coachId, setCoachId] = useState();
  const [coachDetails, setCoachDetails] = useState({});

  const [states, setStates] = useState([]);
  const [isStateDropDown, setIsStateDropDown] = useState(false);
  const [selectedState, setSelectedState] = useState('')

  useEffect(() => {
    const stateData = State.getStatesOfCountry("IN");
    setStates(stateData);
  }, []);
  let token;
  let id;
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      // coachData = await getLoginToken()
      //console.log('user in kyc ', user?.data?.token, user?.data?._id);

      (token = await user?.data?.token), setLoginToken(token);
      id = await user?.data?._id;
      setCoachId(id);

      getCoachDetails(user);
    };

    fetchData();
    setTimeout(() => {
      setLoading(false);
    }, 3000);
  }, [user, coachId, loginToken]);
  const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
  const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;

  const url = 'https://ft44cpdhke.execute-api.ap-south-1.amazonaws.com/';

  const getCoachDetails = async coachData => {
    try {
      setCoachDetails(coachData.data);

      if (
        !coachData?.data?.kycDocuments.documentNumber ||
        coachData?.data?.kycDocuments.documentNumber === '' ||
        coachData?.data?.kycDocuments.documentImg.length == 0
      ) {
        //setIsEditable(true);
        setIsEditable(false);
      } else {
        setIsEditable(true);
        //setIsEditable(false);
      }

      formik.setValues({ ...formik?.values, ...coachData?.data });

      if (coachData?.data?.kycDocuments?.documentImg?.length > 1) {
        setDocumentImageFront(
          coachData?.data?.kycDocuments?.documentImg[0]?.url,
        );
        setDocumentImageBack(
          coachData?.data?.kycDocuments?.documentImg[1]?.url,
        );
      } else if (coachData?.data?.kycDocuments?.documentImg?.length == 1) {
        setDocumentImageFront(
          coachData?.data?.kycDocuments?.documentImg[0]?.url,
        );
      }
    } catch (error) {
      console.log(error);
    }
  };

  const formik = useFormik({
    initialValues: {
      kycDocuments: {
        // documentName: '',
        documentNumber: '',
        documentImg: [{ url: '' }],
      },
      bankDetails: {
        accountNumber: '',
        accountHolderName: '',
        ifsc: '',
      },
      hasGst: false,
      gstNumber: "",
      gstState: "",
    },
    validationSchema: Yup.object().shape({
      kycDocuments: Yup.object().shape({
        documentNumber: Yup.string()
          .required('Document Number is required')
          .matches(
            panRegex,
            'Must be a valid pan number with all upper case character',
          ),
        documentImg: Yup.array()
          .of(
            Yup.object().shape({
              url: Yup.string().required('Document Images are required'),
            }),
          )
          .min(1, 'Atleast one image is required'),
      }),
      bankDetails: Yup.object().shape({
        accountNumber: Yup.number().required('Account Number is required'),
        accountHolderName: Yup.string()
          .max(30, 'Only 30 characters are allowed')
          .required('Account Holder Name is required'),
        ifsc: Yup.string().required('IFSC Code is required'),
      }),
    }),
    hasGst: Yup.boolean(),
    gstState: Yup.string(),
    gstNumber: Yup.string()
      .matches(gstRegex, "Please enter a valid GST number")
      .when("hasGst", {
        is: true,
        then: (schema) => schema.required("GST Number is required"),
        otherwise: (schema) => schema.notRequired(),
      }),

    onSubmit: async values => {
      setLoading(true);
      try {
        console.log(values, 'Kyc details');
        delete values.email;
        delete values.password;

        const response = await axios.patch(
          `${API}/api/coach/${user?.data?.id}`,
          values,
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${user?.data?.token}`,
            },
          },
        );
        if (!response.error) {
          await updateUserDetails();
          setTimeout(() => {
            setLoading(false);
            // navigation.navigate('Login');
          }, 3000);
        }
      } catch (error) {
        console.log('error', error);
        setLoading(false);
      }
    },
  });

  async function handleImagePickerFront() {
    const options = {
      mediaType: 'photo',
    };

    try {
      // Use as a promise without 'callback'
      const result = await launchImageLibrary(options);

      // Check if the user cancelled the picker
      if (result.didCancel) {
        console.log('User cancelled image picker');
      } else if (result.error) {
        console.error('ImagePicker Error:', result.error);
      } else {
        // Handle the selected image
        // Handle the selected image
        const imageSizeInMB = result.fileSize / (1024 * 1024);
        if (imageSizeInMB > 10) {
          Alert.alert(
            'Image size is too large',
            'Please select an image smaller than 10MB.',
          );
          return;
        }
        console.log('Selected Image:', result.assets[0].uri);
        const form = new FormData();

        form.append('image', {
          uri: `${result.assets[0].uri}`,
          type: 'image/jpg',
          name: `${result.assets[0].fileName}`,
        });
        const response = await axios.post(
          `${API}/api/coach/uploadImage`,
          form,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          },
        );

        //console.log(response, "ppp");
        const url = response?.data?.url;
        console.log('urlllll', url);

        setDocumentImageFront(url);
        formik.setFieldValue(`kycDocuments.documentImg.${0}.url`, url);

        updateDb(url, documentImageBack);
      }
    } catch (error) {
      console.error('Error during image selection:', error);
      //formik.setFieldValue(`kycDocuments.documentImg.${0}.url`, "Image is required");
    }
  }

  async function handleImagePickerBack() {
    const options = {
      mediaType: 'photo',
    };

    try {
      // Use as a promise without 'callback'
      const result = await launchImageLibrary(options);

      // Check if the user cancelled the picker
      if (result.didCancel) {
        console.log('User cancelled image picker');
      } else if (result.error) {
        console.error('ImagePicker Error:', result.error);
      } else {
        // Handle the selected image
        // Handle the selected image
        const imageSizeInMB = result.fileSize / (1024 * 1024);
        if (imageSizeInMB > 10) {
          Alert.alert(
            'Image size is too large',
            'Please select an image smaller than 10MB.',
          );
          return;
        }

        const form = new FormData();

        form.append('image', {
          uri: `${result.assets[0].uri}`,
          type: 'image/jpg',
          name: `${result.assets[0].fileName}`,
        });
        const response = await axios.post(
          `${API}/api/coach/uploadImage`,
          form,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          },
        );

        //console.log(response, "ppp");
        const url = response?.data?.url;
        console.log('urlllll', url);

        setDocumentImageBack(url);
        formik.setFieldValue(`kycDocuments.documentImg.${1}.url`, url);
        updateDb(documentImageFront, url);
      }
    } catch (error) {
      console.error('Error during image selection:', error);
    }
  }
  async function updateDb(url1, url2) {
    const data = {
      kycDocuments: {
        documentImg: [{ url: url1 ? url1 : null }, { url: url2 ? url2 : null }],
      },
    };
    try {
      const response = await axios.patch(
        `${API}/api/coach/${user?.data?.id}`,
        data,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${user?.data?.token}`,
          },
        },
      );
      console.log('responseeee', response?.data);
    } catch (error) {
      console.log('error', error);
    }
  }

  const deleteImage = async (url, type) => {
    try {
      const formData = new FormData();
      formData.append('url', url);
      console.log('delete img', user?.data?.id, user?.data?.token);
      const response = await axios.post(
        `${API}/api/coach/uploadImage`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      );
      const resp = response?.data;

      if (type == 'front') {
        setDocumentImageFront('');
        formik.setFieldValue(`kycDocuments.documentImg.${0}.url`, '');
        //console.log("front image", documentImageFront,documentImageBack)
        updateDb(null, documentImageBack);
      }
      if (type == 'back') {
        setDocumentImageBack('');
        formik.setFieldValue(`kycDocuments.documentImg.${1}.url`, '');
        updateDb(documentImageFront, null);
      }
    } catch (error) {
      console.log('erorr', error);
    }
  };

  const handleReset = () => {
    formik.resetForm();
    setDocumentImageFront('');
    setDocumentImageBack('');
    navigation.navigate('Login');
  };

  useFocusEffect(
    useCallback(() => {
      if (
        (showMessage || coachDetails?.status !== 'active') &&
        coachDetails?.kycDocuments?.documentImg?.length > 0
      ) {
        Alert.alert(
          'Message',
          'Details saved Successfully.Your application is under process and we will inform you once approved',
          [
            {
              text: 'Explore Website',
              onPress: () => {
                Linking.openURL(url).catch(err =>
                  console.error("Couldn't load page", err),
                );
              },
            },
            { text: 'Logout', onPress: () => logout() },
          ],
        );
      }

      if (
        (showMessage || coachDetails?.status !== 'active') &&
        coachDetails?.kycDocuments?.documentImg?.length <= 0
      ) {
        console.log('inside if true condition here');

        alert(
          'Your profile has been successfully authorized. Please add your KYC details to continue.',
        );
      }
    }, [showMessage, coachDetails]),
  );

  const toggleStateDropdown = () => {
    setIsStateDropDown(!isStateDropDown); // Toggle the state dropdown visibility
  };
  const handleSelectState = (stateCode) => {
    setSelectedState(stateCode);
    formik.setFieldValue('gstState', stateCode);
    console.log("---->>selected", stateCode); // Log the current selected state
    toggleStateDropdown();
  };
  return (
    <SafeAreaView>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" />
        </View>
      ) : (
        <View
          style={{ backgroundColor: 'lightblue', padding: 10, borderWidth: 0 }}>
          <ScrollView
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}>
            {/* <Text style={{paddingVertical: 10, color: 'black'}}>KYC Details</Text> */}

            <View
              style={{
                backgroundColor: 'white',
                paddingVertical: 10,
                paddingHorizontal: 20,
                borderWidth: 0,
                elevation: 5,
                borderRadius: 5,
              }}>
              <View
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  marginVertical: '2%',
                }}>
                <View style={styles.boxContainer}>
                  <Text style={[styles.label]}>Document Details</Text>
                </View>

                {/* kycDocuments */}
                <View style={styles.boxContainer}>
                  <Text style={styles.label}>Document Number *</Text>
                  <TextInput
                    keyboardType="default"
                    placeholder="Enter document no."
                    style={styles.input}
                    onBlur={formik.handleBlur('kycDocuments.documentNumber')}
                    value={formik?.values?.kycDocuments?.documentNumber}
                    onChangeText={formik.handleChange(
                      'kycDocuments.documentNumber',
                    )}
                    editable={!isEditable}
                  />
                  {formik.touched.kycDocuments?.documentNumber &&
                    formik.errors.kycDocuments?.documentNumber && (
                      <Text style={styles.error}>
                        {formik.errors.kycDocuments?.documentNumber}
                      </Text>
                    )}
                </View>

                {documentImageFront == '' ? (
                  <View style={styles.boxContainer}>
                    <View
                      style={{
                        width: '99%',
                        height: 120,
                        borderWidth: 1,
                        borderStyle: 'dashed',
                        marginVertical: 10,
                        marginHorizontal: '1%',
                        borderRadius: 6,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}>
                      <View
                        style={{
                          flex: 1,
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}>
                        <Svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 -5 24 28"
                          stroke-width="1"
                          stroke="grey"
                          width={100}
                          height={100}
                          class="w-8 h-8">
                          <Path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"></Path>
                        </Svg>
                      </View>
                      <View style={{ borderWidth: 0, paddingVertical: 5 }}>
                        <TouchableOpacity onPress={handleImagePickerFront}>
                          <Text style={{ color: 'blue' }}>
                            {' '}
                            Upload front of image file{' '}
                          </Text>
                        </TouchableOpacity>
                        <Text>PNG, JPG, GIF up to 10MB</Text>
                      </View>
                    </View>
                  </View>
                ) : (
                  <View style={styles.boxContainer}>
                    <View
                      style={{
                        width: '99%',
                        height: 120,
                        borderWidth: 0,
                        marginVertical: 10,
                        marginHorizontal: '1%',
                        borderRadius: 6,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        //position:'relative'
                      }}>
                      <Image
                        source={{ uri: documentImageFront }}
                        style={{
                          width: '96%',
                          height: 120,
                          borderRadius: 6,
                          resizeMode: 'cover',
                        }}
                      />
                      <TouchableOpacity
                        style={{
                          alignSelf: 'flex-end',
                          position: 'absolute',
                          top: 1,
                          right: 10,
                        }}
                        onPress={() =>
                          deleteImage(documentImageFront, 'front')
                        }>
                        {isEditable ? (
                          <></>
                        ) : (
                          <Icon name="close" size={20} color="red" />
                        )}
                      </TouchableOpacity>
                    </View>
                  </View>
                )}

                {documentImageBack == '' ? (
                  <View style={styles.boxContainer}>
                    <View
                      style={{
                        width: '99%',
                        height: 120,
                        borderWidth: 1,
                        borderStyle: 'dashed',
                        marginVertical: 10,
                        marginHorizontal: '1%',
                        borderRadius: 6,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}>
                      <View
                        style={{
                          flex: 1,
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}>
                        <Svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 -5 24 28"
                          stroke-width="1"
                          stroke="grey"
                          width={100}
                          height={100}
                          class="w-8 h-8">
                          <Path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"></Path>
                        </Svg>
                      </View>

                      <View style={{ paddingVertical: 5 }}>
                        <TouchableOpacity onPress={handleImagePickerBack}>
                          <Text style={{ color: 'blue' }}>
                            {' '}
                            Upload back of image file{' '}
                          </Text>
                        </TouchableOpacity>
                        <Text>PNG, JPG, GIF up to 10MB</Text>
                      </View>
                    </View>
                  </View>
                ) : (
                  <View style={styles.boxContainer}>
                    <View
                      style={{
                        width: '99%',
                        height: 120,
                        borderWidth: 0,
                        marginVertical: 10,
                        marginHorizontal: '1%',
                        borderRadius: 6,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}>
                      <Image
                        source={{ uri: documentImageBack }}
                        style={{ width: '96%', height: 120, borderRadius: 6 }}
                      />

                      <TouchableOpacity
                        style={{
                          alignSelf: 'flex-end',
                          position: 'absolute',
                          top: 1,
                          right: 10,
                        }}
                        onPress={() => deleteImage(documentImageBack, 'back')}>
                        {isEditable ? (
                          <></>
                        ) : (
                          <Icon name="close" size={20} color="red" />
                        )}
                      </TouchableOpacity>
                    </View>
                  </View>
                )}

                <View>
                  {formik.touched.kycDocuments?.documentImg &&
                    formik.errors.kycDocuments?.documentImg && (
                      <Text style={styles.error}>
                        {formik.errors.kycDocuments?.documentImg}
                      </Text>
                    )}
                </View>
                {/* Account Details */}

                <View style={styles.boxContainer}>
                  <Text
                    style={[
                      styles.label,
                      {
                        borderTopWidth: 1,
                        borderColor: 'lightblue',
                        paddingTop: 10,
                      },
                    ]}>
                    Account Details
                  </Text>
                </View>

                {/* account holder name */}
                <View style={styles.boxContainer}>
                  <Text style={styles.label}>Account Holder Name</Text>
                  <TextInput
                    keyboardType="default"
                    placeholder="Enter name"
                    style={styles.input}
                    onBlur={formik.handleBlur('bankDetails.accountHolderName')}
                    value={formik?.values?.bankDetails?.accountHolderName}
                    onChangeText={formik.handleChange(
                      'bankDetails.accountHolderName',
                    )}
                    editable={!isEditable}
                  />
                  {formik.touched.bankDetails?.accountHolderName &&
                    formik.errors.bankDetails?.accountHolderName && (
                      <Text style={styles.error}>
                        {formik.errors.bankDetails?.accountHolderName}
                      </Text>
                    )}
                </View>
                {/* account number  */}
                <View style={styles.boxContainer}>
                  <Text style={styles.label}>Account No. *</Text>
                  <TextInput
                    //keyboardType="default"
                    keyboardType="number-pad"
                    placeholder="Enter account no."
                    style={styles.input}
                    onBlur={formik.handleBlur('bankDetails.accountNumber')}
                    value={formik?.values?.bankDetails?.accountNumber}
                    onChangeText={formik.handleChange(
                      'bankDetails.accountNumber',
                    )}
                    editable={!isEditable}
                  />
                  {formik.touched.bankDetails?.accountNumber &&
                    formik.errors.bankDetails?.accountNumber && (
                      <Text style={styles.error}>
                        {formik.errors.bankDetails?.accountNumber}
                      </Text>
                    )}
                </View>

                {/* Ifsc code */}
                <View style={styles.boxContainer}>
                  <Text style={styles.label}>IFSC Code</Text>
                  <TextInput
                    keyboardType="default"
                    placeholder="Enter IFSC code"
                    style={styles.input}
                    onBlur={formik.handleBlur('bankDetails.ifsc')}
                    value={formik?.values?.bankDetails?.ifsc}
                    onChangeText={formik.handleChange('bankDetails.ifsc')}
                    // editable={!formik?.values?.bankDetails?.ifsc}
                    //editable={user?.data?.bankDetails?.ifsc === '' ? null : !formik?.values?.bankDetails?.ifsc}
                    editable={!isEditable}
                  />
                  {formik.touched.bankDetails?.ifsc &&
                    formik.errors.bankDetails?.ifsc && (
                      <Text style={styles.error}>
                        {formik.errors.bankDetails?.ifsc}
                      </Text>
                    )}
                </View>
                <View style={{ marginTop: "5%" }}>
                  <Text style={styles.label}>GST Details</Text>
                 
                  <View style={styles.bookingAlign}>
                    <Text style={styles.label}>Has GST?</Text>
                    <View style={styles.radioGroup}>
                      <View style={styles.radioOption}>
                        <CustomRadioButton
                          value="yes"
                          status={formik.values.hasGst}
                          onPress={() => formik.setFieldValue("hasGst", true)}
                          color="#000" // You can customize the color as needed
                          label="Yes"
                          disabled={isEditable}
                        />
                      </View>

                      <View style={styles.radioOption}>
                        <CustomRadioButton
                          value="no"
                          status={!formik.values.hasGst}
                          onPress={() => formik.setFieldValue("hasGst", false)}
                          color="#000" // You can customize the color as needed
                          label="No"
                          disabled={isEditable}
                        />
                      </View>
                    </View>
                  </View>

                  {formik.values.hasGst && (
                    <View style={styles.inputContainer}>
                      <Text style={styles.label}>GST Account No.</Text>
                      <TextInput
                        placeholder="Enter GST number."
                        value={formik.values.gstNumber}
                        onChangeText={(text) => formik.setFieldValue("gstNumber", text)}
                        style={styles.input}
                        editable={!isEditable}
                      />
                      {formik.touched.gstNumber && formik.errors.gstNumber && (
                        <Text style={styles.error}>{formik.errors.gstNumber}</Text>
                      )}
                    </View>
                  )}

                  {formik.values.hasGst && (
                    <>
                      <TouchableOpacity onPress={toggleStateDropdown} disabled={isEditable}>
                        <TextInput
                          style={styles.input}
                          editable={false}
                          placeholder="Select State"
                          placeholderTextColor="#9CA3AF"
                          value={
                            selectedState
                              ? states.find((state) => state.isoCode === selectedState)?.name
                              : states.find((state) => state.isoCode === formik.values.gstState)?.name || ""
                          }
                          pointerEvents="none"

                        />
                      </TouchableOpacity>

                      <Modal visible={isStateDropDown} animationType="slide" transparent={true}>
                        {/* <View style={styles.modalOverlay}> */}
                        <View style={styles.modalContainer}>
                          <ScrollView contentContainerStyle={styles.scrollView}>
                            {states.map((state) => (
                              <TouchableOpacity
                                key={state.isoCode}
                                style={styles.option}
                                onPress={() => handleSelectState(state.isoCode)}
                              >
                                <Text style={styles.optionText}>
                                  {state.name} {selectedState === state.isoCode ? "✓" : ""}
                                </Text>
                              </TouchableOpacity>
                            ))}
                          </ScrollView>
                          <TouchableOpacity onPress={toggleStateDropdown} style={styles.closeButton}>
                            <Text style={{ color: "#FFF" }}>Close</Text>
                          </TouchableOpacity>
                        </View>
                        {/* </View> */}
                      </Modal>

                      {formik.touched.gstState && formik.errors.gstState && (
                        <Text style={styles.error}>{formik.errors.gstState}</Text>
                      )}
                    </>
                  )}
                </View>

                {isEditable ? (
                  <></>
                ) : (
                  <View style={{ alignSelf: 'flex-end', marginTop: '10%' }}>
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'space-around',
                        marginVertical: 20,
                        marginHorizontal: 20,
                      }}>
                      <TouchableOpacity onPress={handleReset}>
                        <Text
                          style={{
                            borderWidth: 1,
                            borderColor: 'grey',
                            padding: 10,
                            marginRight: 10,
                            fontWeight: '700',
                            color: 'black',
                            borderRadius: 10,
                            fontFamily:
                              'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                          }}>
                          Cancel
                        </Text>
                      </TouchableOpacity>

                      <TouchableOpacity onPress={formik.handleSubmit}>
                        <Text
                          style={{
                            borderWidth: 1,
                            borderColor: 'white',
                            padding: 10,
                            fontWeight: '700',
                            color: 'white',
                            borderRadius: 10,
                            backgroundColor: 'lightskyblue',
                            fontFamily:
                              'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                          }}>
                          Save
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                )}
              </View>
            </View>
          </ScrollView>
        </View>
      )}
    </SafeAreaView>
  );
};

export default KYCdetails;

const styles = StyleSheet.create({
  loadingContainer: {
    height: '100%',

    justifyContent: 'center',
    alignItems: 'center',
  },

  boxContainer: {
    marginTop: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
    //marginLeft:2
  },
  bookingAlign: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: "6%",
    width: "80%"
    // marginRight:"4%"
  },
  radioGroup: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 10, // Adds vertical margin for spacing
  },
  radioOption: {
    flexDirection: 'row', // Aligns the radio button and label horizontally
    alignItems: 'center', // Vertically centers the items
    marginRight: 20, // Adds space between the "Yes" and "No" options
  },
  input: {
    marginTop: 4,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#ccc',
    color: '#333',
    fontSize: 14,
  },
  error: {
    fontSize: 12,
    color: 'red',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: '15%',
    width: '92%',
    maxHeight: '70%',
  },
  option: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  optionText: {
    fontSize: 16,
    color: '#000',
  },
  closeButton: {
    padding: 10,
    backgroundColor: '#000',
    alignItems: 'center',
    borderRadius: 10,
    marginTop: '3%',
  },
  scrollView: {
    flexDirection: 'column',
    marginVertical: '2%',
  },
});