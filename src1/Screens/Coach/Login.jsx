import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  TextInput,
  BackHandler,
  StyleSheet,
  Image,
  Alert,
  TouchableOpacity,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Platform,
  Keyboard,
} from 'react-native';
import axios from 'axios';
import Icon from 'react-native-vector-icons/Ionicons';
import {useNavigation} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useAuth} from '../../components/Auth/AuthContext';
import { API, ANDROID_CLIENT_ID, WEB_CLIENT_ID, IOS_CLIENT_ID } from '@env';

import {
  GoogleSignin,
  GoogleSigninButton,
  statusCodes,
} from '@react-native-google-signin/google-signin';
GoogleSignin.configure({
  webClientId: WEB_CLIENT_ID,
  androidClientId: ANDROID_CLIENT_ID,
  iosClientId: IOS_CLIENT_ID,
  offlineAccess: true,
  scopes: ['profile', 'email', 'https://www.googleapis.com/auth/calendar'],
})

const GoogleLogin = async () => {
  await GoogleSignin.hasPlayServices();
  // await GoogleSignin.getTokens();
  const userInfo = await GoogleSignin.signIn();
  return userInfo;
};

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false); // State for toggling password visibility
  const navigation = useNavigation();
  const {
    login,
    isLoggedin,
    getUserDetails,
    setIsAuthenticated,
    isAuthenticated,
    pageName,
    setPageName,
    setIsHeader,
  } = useAuth();

  useEffect(() => {
    // Ensure header is hidden when on Login screen
    setIsHeader(false);

    const retrieveData = async () => {
      try {
        const coachToken = await AsyncStorage.getItem('coachToken');
        const coachId = await AsyncStorage.getItem('coachId');
        const coachLoggedIn = await AsyncStorage.getItem('coachLoggedIn');
        const coachEmail = await AsyncStorage.getItem('coachEmail');
        if (coachLoggedIn) {
          // getUserDetails();
          const detailsUser = await getUserDetails();

          if (
            coachToken &&
            coachId &&
            detailsUser?.data?.authStatus === 'authorized' &&
            detailsUser?.data?.status === 'active'
          ) {
            //console.log("111")
            setIsAuthenticated(true);
            setIsHeader(true); // Only set header to true when navigating away
            setPageName('Dashboard');
            navigation.navigate('Dashboard');
          } else if (
            coachToken &&
            coachId &&
            detailsUser?.data?.authStatus === 'authorized' &&
            detailsUser?.data?.status === 'inactive'
          ) {
            //console.log("2222")
            setIsHeader(true); // Only set header to true when navigating away
            setPageName('KYC Details');
            navigation.navigate('KYCDetails');
          } else if (
            coachToken &&
            coachId &&
            detailsUser?.data?.authStatus === 'unauthorized' &&
            detailsUser?.data?.status === 'inactive'
          ) {
            //console.log("3333")
            setIsHeader(true); // Only set header to true when navigating away
            setPageName('Professional Details');
            navigation.navigate('ProfessionalDetails');
          }
        }
      } catch (error) {
        console.error('Error retrieving data 73:', error);
      }
    };

    retrieveData();
  }, [isLoggedin]);

  const handleSignIn = async () => {
    // The login function in AuthContext already handles navigation
    // based on user status, so we don't need to navigate here
    await login(email.trim(), password.trim());
  };

  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  useEffect(() => {
    // if (pageName == 'Login' || pageName == 'Dashboard') {
      const backAction = () => {
        Alert.alert('Hold on!', 'Are you sure you want to exit the app?', [
          {
            text: 'Cancel',
            onPress: () => null,
            style: 'cancel',
          },
          {text: 'YES', onPress: () => BackHandler.exitApp()},
        ]);
        return true;
      };

      const backHandler = BackHandler.addEventListener(
        'hardwareBackPress',
        backAction,
      );

      return () => backHandler.remove();
    // }
  }, []);

  // Handle keyboard events to prevent flickering on iOS
  useEffect(() => {
    if (Platform.OS === 'ios') {
      // Create state variables to track keyboard height and visibility if needed
      const keyboardWillShowListener = Keyboard.addListener(
        'keyboardWillShow',
        () => {
          // The keyboard is about to show
          // We don't need to manually adjust anything as KeyboardAvoidingView will handle it
        }
      );

      const keyboardDidShowListener = Keyboard.addListener(
        'keyboardDidShow',
        () => {
          // Keyboard is fully visible
        }
      );

      const keyboardWillHideListener = Keyboard.addListener(
        'keyboardWillHide',
        () => {
          // The keyboard is about to hide
        }
      );

      const keyboardDidHideListener = Keyboard.addListener(
        'keyboardDidHide',
        () => {
          // Keyboard is fully hidden
        }
      );

      // Clean up all listeners
      return () => {
        keyboardWillShowListener.remove();
        keyboardDidShowListener.remove();
        keyboardWillHideListener.remove();
        keyboardDidHideListener.remove();
      };
    }
  }, []);

  // Ensure header is hidden when component mounts
  useEffect(() => {
    // This will run when the component mounts
    setIsHeader(false);

    // This will run when the component unmounts
    return () => {
      // No need to do anything on unmount
    };
  }, []);

  return (
    <KeyboardAvoidingView
      style={styles.keyboardAvoidingContainer}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 10 : 0}
      enabled>
      <View style={styles.container}>
        <View style={styles.logoContainer}>
          <Image
            source={require('../../assets/MainKhelCoach.png')}
            alt="HeaderLogo"
            style={styles.headerLogo}
          />
        </View>
        <View style={styles.textContainer}>
          <Text style={[styles.title]}>Sign in to your account</Text>
        </View>
        <View style={styles.inputContainer}>
          <Text style={styles.labelEmail}>Email Address</Text>
          <TextInput
            style={styles.input}
            placeholder="Enter Email Address"
            placeholderTextColor={'#000'}
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
            autoCorrect={false}
            textContentType="emailAddress"
          />
          <View style={styles.passwordRow}>
            <Text style={styles.label}>Password</Text>
            <TouchableOpacity
              onPress={() => {
                setPageName('Forgot Password');
                navigation.navigate('ForgetPassword');
              }}>
              <Text style={styles.forgotPassword}>Forgot Password</Text>
            </TouchableOpacity>
          </View>
          <View style={{position: 'relative'}}>
            <TextInput
              style={styles.input}
              placeholder="Enter Password"
              placeholderTextColor={'#000'}
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!showPassword}
              autoCapitalize="none"
              autoCorrect={false}
              spellCheck={false}
              keyboardAppearance="light"
              returnKeyType="done"
              enablesReturnKeyAutomatically={true}
              blurOnSubmit={true}
              onSubmitEditing={handleSignIn}
              textContentType="password"
            />
            <TouchableOpacity
              onPress={toggleShowPassword}
              style={{position: 'absolute', top: 10, right: 10}}>
              <Icon
                name={showPassword ? 'eye' : 'eye-off'}
                size={22}
                color="black"
              />
            </TouchableOpacity>
          </View>
          <View>
            <TouchableOpacity
              style={styles.signInButton}
              onPress={handleSignIn}>
              <Text style={styles.signInButtonText}>Login</Text>
            </TouchableOpacity>
          </View>
        </View>
        <View style={styles.signupCon}>
          <Text style={styles.notamem}>
            <View>
              <Text style={{color: 'black'}}> Not a member?</Text>
            </View>
            <TouchableOpacity
              onPress={() => {
                setIsHeader(false);
                setPageName('Coach Registration');
                navigation.navigate('OneStepForm');
              }}>
              <Text style={styles.signUpText}> Sign Up</Text>
            </TouchableOpacity>
          </Text>
        </View>

        {/* Add a touchable area at the bottom to dismiss keyboard when tapping outside inputs */}
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.dismissKeyboardArea} />
        </TouchableWithoutFeedback>
      </View>
    </KeyboardAvoidingView>
  );
};

export default Login;

const styles = StyleSheet.create({
  keyboardAvoidingContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    alignItems: 'center',
    flex: 1,
    paddingHorizontal: '5%',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    backgroundColor: '#fff',
    padding: '10%',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 1,
  },
  dismissKeyboardArea: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
  logoContainer: {
    marginBottom: '10%',
    borderWidth: 0,
  },
  headerLogo: {
    width: 200,
    height: 100,
    resizeMode: 'contain',
  },
  textContainer: {
    marginBottom: '2%',
  },
  title: {
    fontSize: 24,
    // fontWeight: 'bold',
    marginBottom: '7%',
    color: 'black',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  inputContainer: {
    width: '100%',
  },
  input: {
    marginBottom: '3%',
    borderWidth: 1,
    borderColor: '#ddd',
    padding: Platform.OS === 'ios' ? 15 : 10,
    borderRadius: 5,
    width: '100%',
    color: 'black',
    fontSize: 16,
  },
  labelEmail: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: '2%',
    color: 'black',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  label: {
    fontSize: 14,
    fontWeight: 'bold',
    color: 'black',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    color: '#0EA5E9',
    marginBottom: '2%',
    fontWeight: '600',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  passwordRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: '3%', // Adjust spacing as needed
  },
  eyeIcon: {
    width: 24,
    height: 24,
  },
  signInButton: {
    backgroundColor: '#EF4444',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 100,
    height: 50,
    marginTop: 15,
  },
  signInButtonText: {
    color: '#ffffff',
    fontSize: 16,
    textAlign: 'center',
    fontWeight: '600',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  signupCon: {
    marginTop: '5%',
  },
  notamem: {
    fontSize: 20,
    alignItems: 'center',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  signUpText: {
    color: '#0EA5E9',
    fontWeight: '600',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  signUpLink: {
    textDecorationLine: 'underline',
  },
  otpButton: {
    backgroundColor: '#EF4444',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 300,
    // width:'100%',
    height: 50,
  },
  googleSignInButton: {
    flexDirection: 'row',
    backgroundColor: '#FAFBFCFF',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 15,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  googleSignInButtonText: {
    marginLeft: 10,
    color: '#000',
    fontSize: 16,
  },
  googleLogo: {
    width: 24,
    height: 24,
  },
});
