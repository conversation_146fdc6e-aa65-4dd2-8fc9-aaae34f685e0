import {
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  View,
  TouchableOpacity,
  Image,
  SafeAreaView,
  ActivityIndicator,
  Alert
} from 'react-native';
import React, { useEffect, useState } from 'react';

import DatePicker from 'react-native-modern-datepicker';
import Icon from 'react-native-vector-icons/FontAwesome';
import Icon1 from 'react-native-vector-icons/Ionicons';
import { launchImageLibrary } from 'react-native-image-picker';
import axios from 'axios';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { API } from '@env';
import { NEXT_GOOGLE_MAPS_KEY } from '@env';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Picker } from '@react-native-picker/picker';
import { getLoginToken } from '../../helpers';
import { useAuth } from '../../components/Auth/AuthContext';
import moment from 'moment-timezone';
import { State } from 'country-state-city';
import QuillEditor from 'react-native-cn-quill';


const DAYS_OF_WEEK = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
const Registration = ({ route }) => {
  const [open, setOpen] = useState(false);
  const [profileImage, setProfileImage] = useState('');
  const [loginToken, setLoginToken] = useState('');
  const navigation = useNavigation();
  const { user, updateUserDetails, setIsHeader, setPageName } = useAuth();

  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // For address functionality
  const [states, setStates] = useState([]);
  const [coachId, setCoachId] = useState();
  const [isUserAuthorized, setIsUserAuthorized] = useState(false);

  let coachData;
  let token;
  let id;
  useEffect(() => {
    const stateData = State.getStatesOfCountry("IN");
    setStates(stateData);
  }, []);
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      if (user.length === 0) {
        coachData = await getLoginToken();
        setLoginToken(coachData?.data?.token);
        setCoachId(coachData?.data?.id);
        setIsHeader(true);
      } else {
        token = user?.data?.token;
        setLoginToken(token);
        id = user?.data?._id;
        setCoachId(id);
        getCoachDetails(user);
      }
    };
    fetchData();
    getCategories();
    setTimeout(() => {
      setLoading(false);
    }, 3000);
  }, [user, coachId, loginToken]);
  // Function to get categories - not needed for personal info
  const getCategories = async () => {
    try {
      let response = await axios.get(`${API}/api/category`);
      console.log("Categories fetched:", response?.data?.data);
    } catch (error) {
      console.log(error);
    }
  };
  const getCoachDetails = async coachData => {
    try {
      // Set basic personal information
      formik.setValues({ ...formik?.values, ...coachData?.data });

      // Set token and login info
      token = coachData?.data?.token;
      setLoginToken(token);
      id = coachData?.data?._id;

      // Format and set date of birth
      if (coachData?.data?.dob) {
        const finalDate = formatDateToYYYYMMDD(coachData?.data?.dob);
        formik.setFieldValue('dob', finalDate);
      }

      // Set linked facilities data
      if (coachData?.data?.linkedFacilities && coachData?.data?.linkedFacilities.length > 0) {
        coachData?.data?.linkedFacilities.forEach((item, index) => {
          if (index < formik.values.linkedFacilities.length) {
            formik.setFieldValue(
              `linkedFacilities.${index}.description`,
              item?.description,
            );
          }
        });
      }

      // Set profile image
      if (coachData?.data?.profileImg) {
        setProfileImage(coachData?.data?.profileImg);
      }
    } catch (error) {
      console.log("Error getting coach details:", error);
    }
  };
  // Professional details image functions removed
  // Regular expressions for validation
  const phoneRegExp = /^[6-9]\d{9}$/;
  const pinCodeRegExp = /^[1-9][0-9]{5}$/;
  const emailRegExp = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
  const passwordRegExp = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\w\s])[A-Za-z\d!"#$%&'()*+,-./:;<=>?@[\\\]^_`{|}~]{8,}$/;

  // Reference for the rich text editor
  const _editor = React.createRef();

  // let token;

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);

      const coachData = await getLoginToken();
      console.log("coach dataaaaa", coachData);
      const token = coachData?.data?.token;
      setLoginToken(token);
      setCoachId(coachData?.data?._id);
      //console.log("coach dataaaaa", token)
      getCoachDetails(user);
      setTimeout(() => {
        setLoading(false);
      }, 3000);
      //setLoading(false);
    };

    if (user.length === 0) {
      setLoading(false);
    } else {
      fetchData();
    }
  }, []);

  const getLocationInfo = async pincode => {
    try {
      const response = await axios.get(
        `https://api.postalpincode.in/pincode/${pincode}`,
      );
      if (
        response.data.length > 0 &&
        response.data[0]?.PostOffice?.length > 0
      ) {
        const postOffice = response.data[0].PostOffice[0];
        const locationInfo = {
          city: `${postOffice.Name},${postOffice.District}`,
          state: postOffice.State,
          country: postOffice.Country,
        };
        return locationInfo;
      } else {
        return null;
      }
    } catch (error) {
      console.error('Error fetching location:', error);
      return null;
    }
  };

  const handlePincodeChange = async (newPincode, index) => {
    formik.setFieldValue(`linkedFacilities.${index}.pinCode`, newPincode);
    if (newPincode?.length === 6) {
      const locationInfo = await getLocationInfo(newPincode);
      // console.log(locationInfo);
      const { city, country, state } = locationInfo;
      formik.setFieldValue(`linkedFacilities.${index}.city`, city);
      formik.setFieldValue(`linkedFacilities.${index}.country`, country);
      formik.setFieldValue(`linkedFacilities.${index}.state`, state);
    }
  };

  // const getCoachDetails = async coachData => {
  //   try {
  //     (token = coachData?.data?.token), setLoginToken(token);
  //     id = coachData?.data?._id;

  //     await formik.setValues({ ...formik.values, ...coachData?.data });
  //     const finalDate = await formatDateToYYYYMMDD(coachData?.data?.dob);
  //     await formik.setFieldValue('dob', finalDate);

  //     coachData?.data?.linkedFacilities?.map(async (item, index) => {
  //       console.log('insidde setting values', item?.description, item);
  //       await formik.setFieldValue(
  //         `linkedFacilities.${index}.description`,
  //         item?.description,
  //       );
  //     });

  //     setProfileImage(coachData?.data?.profileImg);
  //   } catch (error) {
  //     console.log(error);
  //   }
  // };
  function formatDateToYYYYMMDD(dateString) {
    //console.log("datee in formatt",dateString)
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = `0${date.getMonth() + 1}`.slice(-2); // Add leading zero if needed
    const day = `0${date.getDate()}`.slice(-2); // Add leading zero if needed

    return `${day}-${month}-${year}`;
  }

  const getGeoLocations = async address => {
    const apiKey = NEXT_GOOGLE_MAPS_KEY;
    try {
      const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(
        address,
      )}&key=${apiKey}`;
      const response = await axios.get(url);
      if (response.data.status === 'OK') {
        const result = response.data.results[0];
        const location = result.geometry.location;
        return {
          message: 'Success',
          data: { Latitude: location.lat, Longitude: location.lng },
        };
      } else {
        console.error(
          'Geocode was not successful for the following reason',
          response.data.status,
        );
        return null;
      }
    } catch (error) {
      console.error('There was an error fetching the geocode data:', error);
      return null;
    }
  };

  const validationSchema = Yup.object().shape({
    firstName: Yup.string()
      .min(3, 'At least 3 characters are required')
      .required('Name is required'),
    lastName: Yup.string()
      .min(3, 'At least 3 characters are required')
      .required('Last name is required'),
    gender: Yup.string().required('Gender is required'),
    email: Yup.string()
      .email('Invalid email')
      .matches(emailRegExp, 'Invalid email')
      .required('Email is required'),
    dob: Yup.date()
      .required('Date of birth is required')
      .max(new Date(), 'Date of birth cannot be in the future')
      .test('is-adult', 'Must be at least 18 years old', function (value) {
        const today = new Date();
        const minAgeDate = new Date(
          today.getFullYear() - 18,
          today.getMonth(),
          today.getDate(),
        );
        return value <= minAgeDate;
      }),
    password: !loginToken
      ? Yup.string()
        .min(6, 'Password must be at least 6 characters')
        .matches(
          passwordRegExp,
          'Password must contain minimum eight characters, at least one uppercase letter, one lowercase letter, one number, and one special character.',
        )
        .required('Password is required')
      : Yup.string(),

    confirmPassword: !loginToken
      ? Yup.string()
        .required('Confirm Password is required')
        .oneOf([Yup.ref('password'), null], 'Password must match')
      : Yup.string(),

    mobile: Yup.string()
      .matches(phoneRegExp, 'Phone number is not valid')
      .required('Please enter your phone number'),
    alternateMobile: Yup.string().matches(
      phoneRegExp,
      'Phone number is not valid',
    ),
    linkedFacilities: Yup.array().of(
      Yup.object().shape({
        name: Yup.string()
          .required('Name is required')
          .max(100, 'Only 100 characters are allowed')
          .min(3, 'Minimum 3 characters are required'),
        addressLine1: Yup.string().required('Address Line 1 is required'),
        addressLine2: Yup.string(),
        city: Yup.string()
          .required('City is required')
          .max(50, 'Only 30 characters are allowed'),
        state: Yup.string()
          .required('State is required')
          .max(50, 'Only 30 characters are allowed'),
        pinCode: Yup.string()
          .matches(pinCodeRegExp, 'PIN code is not valid')
          .required('PinCode is required'),
        country: Yup.string()
          .required('Country is required')
          .max(50, 'Only 30 characters are allowed'),
        description: Yup.string(),
      }),
    ),
  });

  const formik = useFormik({
    initialValues: {
      firstName: '',
      lastName: '',
      gender: '',
      email: '',
      mobile: '',
      dob: '',
      password: '',
      confirmPassword: '',
      profileImg: '',
      age: '',
      alternateMobile: '',
      linkedFacilities: [
        {
          name: '',
          addressLine1: '',
          addressLine2: '',
          city: '',
          state: '',
          pinCode: '',
          country: '',
          amenities: '',
          location: { coordinates: [] },
        },
      ],
    },

    validationSchema: validationSchema,
    onSubmit: async values => {
      console.log('values', values);
      try {
        setLoading(true);
        let result;

        // Check and set age if not present
        if (formik?.values?.age == '') {
          const dob = moment.isMoment(values.dob) ? dob : moment(values.dob);
          const difference = moment().diff(dob, 'years');
          await formik.setFieldValue('age', difference);
          // Continue with form submission instead of returning
          values = { ...values, age: difference };
        }

        // Filter and prepare data
        const filteredData = {
          ...values,
          linkedFacilities: values.linkedFacilities.filter(facility =>
            facility.name || facility.addressLine1 || facility.city
          ),
          dob: new Date(values.dob)
        };

        
        // If startDate exists, set it to the required string
        // if (filteredData.academyAvailability && filteredData.academyAvailability.startDate) {
        //   filteredData.academyAvailability.startDate = "2025-07-31T00:00:00.000Z";
        // }
        if(filteredData.affiliationType === 'academy') {
          delete filteredData.affiliationType;
        }
        

        // Remove empty/null/undefined values
        Object.keys(filteredData).forEach(key => {
          if (filteredData[key] === null ||
              filteredData[key] === undefined ||
              (Array.isArray(filteredData[key]) && filteredData[key].length === 0) ||
              filteredData[key] === '') {
            delete filteredData[key];
          }
        });

        // Handle profile image validation
        if (!filteredData.profileImg) {
          Alert.alert('Please select the profile image');
          setLoading(false);
          formik.setFieldError('profileImg', 'Select the profile image');
          return;
        }

        // Get coordinates for addresses
        if (filteredData.linkedFacilities?.length > 0) {
          for (let i = 0; i < filteredData.linkedFacilities.length; i++) {
            const facility = filteredData.linkedFacilities[i];
            const addressString = `${facility.name} ${facility.addressLine1} ${facility.city}`;
            const exact_address = await getGeoLocations(addressString);

            if (exact_address?.data) {
              filteredData.linkedFacilities[i].location = {
                coordinates: [
                  `${exact_address.data.Latitude}`,
                  `${exact_address.data.Longitude}`
                ]
              };
            }
          }
        }

        // Handle API calls based on user status
        console.log("filtered data:", filteredData);
        if (user?.data?.token && user?.data?._id) {
          // Remove email and password for existing users
          delete filteredData.email;
          delete filteredData.password;
          console.log("Filtered Data: ", filteredData.affiliationType);
          result = await axios.patch(
            `${API}/api/coach/${user?.data?._id}`,
            filteredData,
            {
              headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${user?.data?.token}`,
              },
            }
          );
        } else {
          result = await axios.post(
            `${API}/api/coach`,
            filteredData,
            {
              headers: {
                'Content-Type': 'application/json',
              },
            }
          );
        }
        console.log("API response:", result);

        // Handle response
        if (!result.error) {
          if (user?.data?.token) {
            await updateUserDetails();
            Alert.alert('Personal Information Saved Successfully');
            setLoading(false);
            // setTimeout(() => {
            //   setLoading(false);
            //   setPageName('Calendar');
            //   navigation.navigate('Calendar');
            // }, 2000);
          } else {
            // Handle new user registration
            const tokenNew = result?.data?.token;
            const id = result?.data?.data?._id;
            console.log("Handle New Registration", tokenNew, id);
            await AsyncStorage.setItem('coachToken', tokenNew);
            await AsyncStorage.setItem('coachId', id);
            await AsyncStorage.setItem('coachLoggedIn', 'true');
            await updateUserDetails();

            Alert.alert('Registration Successful', 'Your personal information has been saved.');
            setTimeout(() => {
              setLoading(false);
              setPageName('Dashboard');
              navigation.navigate('Dashboard');
              setIsHeader(true);
            }, 2000);
          }
        }
      } catch (error) {
        console.error('Error:', error);
        setLoading(false);
        if (error.response?.status === 400) {
          Alert.alert('Error', error.response.data.error);
        } else {
          Alert.alert('Error', 'An error occurred while saving your information');
        }
      }
    }

  });

  const addAddress = () => {
    formik.setValues({
      ...formik.values,
      linkedFacilities: [
        ...formik.values.linkedFacilities,
        {
          name: '',
          addressLine1: '',
          addressLine2: '',
          city: '',
          state: '',
          pinCode: '',
          country: '',
          // phone: "",
          description: '',
        },
      ],
    });
    // setIsFacilities(true);
  };

  function replaceSlashWithDash(dateString) {
    return dateString.replace(/\//g, '-');
  }

  const showDatePicker = () => {
    setOpen(!open);
  };

  function isOver18YearsOld(selectedDate) {
    // Calculate the minimum date for being 18 years old
    const today = new Date();
    const minAgeDate = new Date(
      today.getFullYear() - 18,
      today.getMonth(),
      today.getDate(),
    );
    const selectedDateObject = new Date(selectedDate);

    return selectedDateObject <= minAgeDate;
  }

  const calculateAge = dob => {
    const currentDate = new Date();
    const dobDate = new Date(dob);

    let age = currentDate.getFullYear() - dobDate.getFullYear();

    // Check if the current date hasn't reached the birth month and day yet
    if (
      currentDate.getMonth() < dobDate.getMonth() ||
      (currentDate.getMonth() === dobDate.getMonth() &&
        currentDate.getDate() < dobDate.getDate())
    ) {
      age--;
    }

    return age;
  };

  function handleDateChange(propDate) {
    let date = replaceSlashWithDash(propDate);
    try {
      console.log(replaceSlashWithDash(propDate), 'date');
      let isValidAge = isOver18YearsOld(date);
      console.log(isValidAge, '000');
      if (isValidAge) {
        const formattedDate = moment.tz(date, 'Asia/Kolkata').format();
        formik.setFieldValue('dob', formattedDate);
        setOpen(false);
      } else {
        formik.setFieldError('dob', 'Must be at least 18 years old');
        Alert.alert('Age Validation', 'Age must be at least 18 years old');
        setOpen(false);
      }
    } catch (error) {
      // Handle validation errors
      formik.setFieldError('dob', error.message);
      setOpen(false);
    }
  }

  // Function removed - not needed

  async function handleProfileImagePicker() {
    try {
      const url = await handleImagePicker();
      if (url) {
        setProfileImage(url);
        await formik.setFieldValue('profileImg', url);
        console.log("Profile image updated:", url);
      }
    } catch (error) {
      console.error('Error during profile image selection:', error);
    }
  }

  const deleteProfileImage = async url => {
    try {
      await new Promise((resolve, reject) => {
        Alert.alert(
          'Profile Picture Delete',
          'Do you really want to delete, clicking OK will delete this profile picture.',
          [
            {
              text: 'Cancel',
              onPress: () => reject(new Error('User Cancelled')),
              style: 'cancel',
            },
            { text: 'Ok', onPress: resolve },
          ],
          { cancelable: true },
        );
      });

      // If the user pressed "OK", continue with image deletion
      const formData = new FormData();
      formData.append('url', url);

      await axios.post(
        `${API}/api/coach/uploadImage`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      );

      // Clear profile image
      setProfileImage('');
      formik.setFieldValue('profileImg', '');
      console.log("Profile image deleted");
    } catch (error) {
      if (error.message === 'User Cancelled') {
        console.log('User cancelled profile image deletion');
      } else {
        console.log('Error deleting profile image:', error);
      }
    }
  };
  // Professional details functions removed

  // This function is simplified to only handle image uploads
  async function handleImagePicker() {
    const options = {
      title: 'Select Image',
      mediaType: 'photo',
    };

    try {
      const result = await launchImageLibrary(options);

      // Check if the user cancelled the picker
      if (result.didCancel) {
        console.log('User cancelled image picker');
        return null;
      } else if (result.error) {
        console.error('ImagePicker Error:', result.error);
        return null;
      } else {
        // Handle the selected image
        const imageSizeInMB = result.fileSize / (1024 * 1024);
        if (imageSizeInMB > 10) {
          Alert.alert(
            'Image size is too large',
            'Please select an image smaller than 10MB.',
          );
          return null;
        }

        const imageName = result.assets[0].fileName;
        const formData = new FormData();
        formData.append('image', {
          uri: `${result.assets[0].uri}`,
          type: 'image/jpg',
          name: imageName,
        });

        const response = await axios.post(
          `${API}/api/coach/uploadImage`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          },
        );

        const url = response?.data?.url;
        return url;
      }
    } catch (error) {
      console.error('Error during image selection:', error);
      return null;
    }
  }

  // async function updateDb(obj) {
  //   console.log('inside update db', obj);
  //   try {
  //     const response = await axios.patch(
  //       `${API}/api/coach/${user?.data?.id}`,
  //       obj,
  //       {
  //         headers: {
  //           'Content-Type': 'application/json',
  //           Authorization: `Bearer ${user?.data?.token}`,
  //         },
  //       },
  //     );
  //     console.log('responseeee', response?.data);
  //   } catch (error) {
  //     console.log('error', error);
  //   }
  // }
  // async function updateDb(obj) {
  //   console.log('inside update db', obj);
  //   try {
  //     const response = await axios.patch(
  //       `${API}/api/coach/${user?.data?.id}`,
  //       obj,
  //       {
  //         headers: {
  //           'Content-Type': 'application/json',
  //           Authorization: `Bearer ${user?.data?.token}`,
  //         },
  //       },
  //     );
  //     // console.log('responseeee', response?.data);
  //     updateUserDetails();
  //   } catch (error) {
  //     console.log('error', error);
  //   }
  // }

  const handleReset = () => {
    formik.resetForm(); // Reset the form
    setProfileImage('');
    if (loginToken && loginToken !== '') {
      setPageName('Dashboard');
      navigation.navigate('Dashboard');
    } else {
      setPageName('Login');
      navigation.navigate('Login');
    }
  };

  const handleFormSubmit = () => {
    console.log("Submit triggered");
    if (formik.values.age == '') {
      const age = calculateAge(new Date(formik.values.dob));
      formik.setFieldValue('age', age);
    }

    // Call formik.handleSubmit to trigger form submission
    formik.handleSubmit();
  };

  const toggleShowPassword = type => {
    if (type == 'password') {
      setShowPassword(!showPassword);
    } else if (type == 'confirm') {
      setShowConfirmPassword(!showConfirmPassword);
    }
  };
  // KYC and GST related functions removed

  useEffect(()=>{
    console.log("Use Effect User Data: ---------------------->", user);
  })
  return (
    <SafeAreaView>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" />
        </View>
      ) : (
        <View
          style={{ backgroundColor: 'lightblue', padding: 10, borderWidth: 0 }}>
          <ScrollView
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}>
            {/* <Text style={{paddingVertical: 10, color: 'black'}}>Basic Details</Text> */}

            {/* form */}
            <View
              style={{
                backgroundColor: 'white',
                paddingVertical: 10,
                paddingHorizontal: 20,
                borderWidth: 0,
                elevation: 5,
                borderRadius: 5,
              }}>
              {/* Display selected profile image */}

              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <View
                  style={{
                    width: 65,
                    height: 65,
                    borderWidth: 0,
                    marginRight: 10,
                    borderRadius: 50,
                  }}>
                  <Image
                    source={{
                      uri: profileImage
                        ? profileImage
                        : 'data:image/png;base64,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',
                    }}
                    style={{
                      width: 64,
                      height: 64,
                      borderRadius: 50,
                      borderWidth: 0,
                    }}
                  />
                </View>

                {/* Button to open image picker */}
                <View style={{ marginRight: 10 }}>
                  <TouchableOpacity
                    onPress={handleProfileImagePicker}
                    // onPress={user?.data?.id ? showAlert : handleImagePicker}
                    >
                    <Icon name="pencil" size={24} color="black" />
                  </TouchableOpacity>
                </View>

                <TouchableOpacity
                  onPress={() => {
                    profileImage ? deleteProfileImage(profileImage) : '';
                  }}>
                  <Icon name="trash-o" size={24} color="black" />
                </TouchableOpacity>
              </View>
              {formik.touched.profileImg && formik.errors.profileImg && (
                <Text style={styles.error}>{formik.errors.profileImg}</Text>
              )}
              <View
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  marginVertical: '2%',
                }}>
                <View>
                  <Text
                    style={{
                      paddingVertical: 10,
                      paddingTop: 20,
                      color: 'black',
                      borderTopWidth: 1,
                      borderColor: 'lightblue',
                      fontWeight: '500',
                      fontSize: 16,
                      fontFamily:
                        'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                    }}>
                    Personal Information
                  </Text>
                </View>

                {/* fname */}
                <View style={styles.boxContainer}>
                  <Text style={styles.label}>First Name</Text>
                  <TextInput placeholderTextColor="#000"

                    keyboardType="default"
                    style={styles.input}
                    onBlur={formik.handleBlur('firstName')}
                    value={formik.values.firstName}
                    onChangeText={formik.handleChange('firstName')}
                  />
                  {formik.touched.firstName && formik.errors.firstName && (
                    <Text style={styles.error}>{formik.errors.firstName}</Text>
                  )}
                </View>
                {/* lname */}
                <View style={styles.boxContainer}>
                  <Text style={styles.label}>Last Name</Text>
                  <TextInput placeholderTextColor="#000"

                    keyboardType="default"
                    style={styles.input}
                    onChangeText={formik.handleChange('lastName')}
                    onBlur={formik.handleBlur('lastName')}
                    value={formik.values.lastName}
                  />
                  {formik.touched.lastName && formik.errors.lastName && (
                    <Text style={styles.error}>{formik.errors.lastName}</Text>
                  )}
                </View>
                {/* mobile */}
                <View style={styles.boxContainer}>
                  <Text style={styles.label}>Mobile</Text>
                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    borderWidth: 1,
                    borderColor: '#9CA3AF',
                    borderRadius: 5,
                  }}>
                    <View style={{
                      paddingHorizontal: 10,
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderRightWidth: 1,
                      borderRightColor: '#9CA3AF',
                    }}>
                      <Text style={{ fontSize: 16, color:"#000" }}>+91</Text>
                    </View>

                    <TextInput
                      placeholderTextColor="#000"
                      keyboardType="phone-pad"
                      style={{
                        flex: 1,
                        marginTop: 4,
                        paddingHorizontal: 12,
                        paddingVertical: 8,
                        borderRadius: 6,
                        color: '#333',
                        fontSize: 14,
                      }}
                      onChangeText={formik.handleChange('mobile')}
                      onBlur={formik.handleBlur('mobile')}
                      value={formik.values.mobile}
                      editable={user.length === 0 ? null : !formik.values.mobile}
                    />
                  </View>

                  {formik.touched.mobile && formik.errors.mobile && (
                    <Text style={styles.error}>{formik.errors.mobile}</Text>
                  )}

                </View>

                {/* alternate mobile */}
                <View style={styles.boxContainer}>
                  <Text style={styles.label}>Alternate Mobile Number</Text>

                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    borderWidth: 1,
                    borderColor: '#9CA3AF',
                    borderRadius: 5,
                  }}>
                    <View style={{
                      paddingHorizontal: 10,
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderRightWidth: 1,
                      borderRightColor: '#9CA3AF',
                    }}>
                      <Text style={{ fontSize: 16, color:"#000"}}>+91</Text>
                    </View>

                    <TextInput placeholderTextColor="#000"

                      keyboardType="phone-pad"
                      style={{
                        flex: 1,
                        marginTop: 4,
                        paddingHorizontal: 12,
                        paddingVertical: 8,
                        borderRadius: 6,
                        color: '#333',
                        fontSize: 14,
                      }}
                      alternateMobile
                      onChangeText={formik.handleChange('alternateMobile')}
                      onBlur={formik.handleBlur('alternateMobile')}
                      value={formik.values.alternateMobile}
                    />
                  </View>

                  {formik.touched.alternateMobile &&
                    formik.errors.alternateMobile && (
                      <Text style={styles.error}>
                        {formik.errors.alternateMobile}
                      </Text>
                    )}
                </View>

                {/* email */}
                <View style={styles.boxContainer}>
                  <Text style={styles.label}>Email-Address</Text>
                  <TextInput placeholderTextColor="#000"

                    style={styles.input}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    onChangeText={formik.handleChange('email')}
                    onBlur={formik.handleBlur('email')}
                    value={formik.values.email}
                    // editable={!formik.values.email}
                    editable={user.length === 0 ? null : !formik.values.email}
                  />
                  {formik.touched.email && formik.errors.email && (
                    <Text style={styles.error}>{formik.errors.email}</Text>
                  )}
                </View>
                {/* password */}

                {loginToken == '' || loginToken == undefined ? (
                  <>
                    <View style={styles.boxContainer}>
                      <Text style={styles.label}>Password</Text>

                      <View style={{ position: 'relative' }}>
                        <TextInput placeholderTextColor="#000"

                          style={styles.input}
                          // secureTextEntry
                          secureTextEntry={!showPassword}
                          onChangeText={formik.handleChange('password')}
                          onBlur={formik.handleBlur('password')}
                          value={formik.values.password}
                        />
                        <TouchableOpacity
                          onPress={() => toggleShowPassword('password')}
                          style={{ position: 'absolute', top: 13, right: 10 }}>
                          <Icon1
                            name={showPassword ? 'eye' : 'eye-off'}
                            size={22}
                            color="black"
                          />
                        </TouchableOpacity>
                      </View>
                      {formik.touched.password && formik.errors.password && (
                        <Text style={styles.error}>
                          {formik.errors.password}
                        </Text>
                      )}
                    </View>
                  </>
                ) : (
                  <></>
                )}

                {/* confirm password */}

                {loginToken == '' || loginToken == undefined ? (
                  <>
                    <View style={styles.boxContainer}>
                      <Text style={styles.label}>Confirm Password</Text>

                      <View style={{ position: 'relative' }}>
                        <TextInput placeholderTextColor="#000"

                          style={styles.input}
                          // secureTextEntry
                          secureTextEntry={!showConfirmPassword}
                          onChangeText={formik.handleChange('confirmPassword')}
                          onBlur={formik.handleBlur('confirmPassword')}
                          value={formik.values.confirmPassword}
                        />
                        <TouchableOpacity
                          onPress={() => toggleShowPassword('confirm')}
                          style={{ position: 'absolute', top: 13, right: 10 }}>
                          <Icon1
                            name={showConfirmPassword ? 'eye' : 'eye-off'}
                            size={22}
                            color="black"
                          />
                        </TouchableOpacity>
                      </View>
                      {formik.touched.confirmPassword &&
                        formik.errors.confirmPassword && (
                          <Text style={styles.error}>
                            {formik.errors.confirmPassword}
                          </Text>
                        )}
                    </View>
                  </>
                ) : (
                  <></>
                )}

                {/* dob */}

                <View style={styles.boxContainer}>
                  <TouchableOpacity
                    onPress={showDatePicker}
                    style={styles.calendarIcon}>
                    <Text style={styles.label}> Date of Birth </Text>
                    <View style={styles.inputContainer}>
                      {formik.values.dob ? (
                        <TextInput placeholderTextColor="#000"

                          style={styles.textInput}
                          value={formik.values?.dob?.split('T')[0]}
                          editable={false}
                        />
                      ) : (
                        <TextInput placeholderTextColor="#000"

                          style={styles.textInput}
                          placeholder="Select Date of Birth"
                          editable={false}
                        />
                      )}

                      <Icon name="calendar" size={20} color="black" />
                    </View>
                  </TouchableOpacity>

                  {open && (
                    <DatePicker
                      mode="calendar"
                      name="dob"
                      selected={
                        formik.values.dob
                          ? formik.values.dob.toString()
                          : new Date().toString()
                      }
                      //selected={formik.values.dob || new Date()} // Provide a default date if `dob` is undefined
                      onMonthChange={(month, year) => console.log(month, year)}
                      onDateChange={handleDateChange}
                    />
                  )}

                  {formik.touched.dob && formik.errors.dob && (
                    <Text style={styles.error}>{formik.errors.dob}</Text>
                  )}
                </View>

                {/* gender */}
                <View style={styles.boxContainer}>
                  <Text style={styles.label}>Gender</Text>

                  <View
                    style={{
                      borderColor: '#e5e7eb',
                      borderWidth: 1,
                      borderRadius: 6,
                      marginVertical: '2%',
                    }}>
                    <Picker
                      style={{
                        width: '100%',
                        color: '#333',
                        fontSize: 14,
                       }}
                      selectedValue={formik.values.gender}
                      onValueChange={(itemValue) =>
                        formik.setFieldValue('gender', itemValue)
                      }>
                      <Picker.Item label="Select gender" value="" />
                      <Picker.Item label="Male" value="male" />
                      <Picker.Item label="Female" value="female" />
                      <Picker.Item label="Others" value="others" />
                    </Picker>
                  </View>
                  {formik.touched.gender && formik.errors.gender && (
                    <Text style={styles.error}>{formik.errors.gender}</Text>
                  )}
                </View>

                {/* address */}
                <Text
                  style={{
                    paddingVertical: 10,
                    paddingTop: 20,
                    color: 'black',
                    marginTop: 20,
                    borderTopWidth: 1,
                    borderColor: 'lightblue',
                    fontWeight: '500',
                    fontSize: 16,
                  }}>
                  Address
                </Text>
                <View
                  style={{
                    borderWidth: 0,
                    backgroundColor: 'white',
                    elevation: 2,
                    paddingVertical: 10,
                    paddingHorizontal: 20,
                  }}>
                  {formik.values.linkedFacilities?.map((_, index) => (
                    <View key={index}>
                      <View
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          fontFamily: 'Lato-Bold',
                          marginTop: 30,
                        }}>
                        <Text
                          style={{
                            fontSize: 16,
                            fontWeight: 'bold',
                            color: '#333',
                          }}>
                          Address {`${index + 1}`}
                        </Text>
                        {index + 1 > 1 ? (
                          <TouchableOpacity
                            onPress={() =>
                              formik.setValues(prevState => ({
                                ...prevState,
                                linkedFacilities:
                                  prevState.linkedFacilities.filter(
                                    (_, i) => i !== index,
                                  ),
                              }))
                            }>
                            <Icon name="close" size={18} color="red" />
                          </TouchableOpacity>
                        ) : (
                          <></>
                        )}
                      </View>

                      <View style={styles.boxContainer}>
                        <Text style={styles.label}>Facility Name</Text>
                        <TextInput placeholderTextColor="#000"

                          style={styles.input}
                          onChangeText={formik.handleChange(
                            `linkedFacilities.${index}.name`,
                          )}
                          onBlur={formik.handleBlur(
                            `linkedFacilities.${index}.name`,
                          )}
                          value={formik.values.linkedFacilities[index].name}
                        />
                        {formik.touched.linkedFacilities?.[index]?.name &&
                          formik.errors.linkedFacilities?.[index]?.name && (
                            <Text style={styles.error}>
                              {formik.errors.linkedFacilities?.[index]?.name}
                            </Text>
                          )}
                      </View>

                      <View style={styles.boxContainer}>
                        <Text style={styles.label}>Pincode</Text>
                        <TextInput placeholderTextColor="#000"

                          keyboardType="phone-pad"
                          style={styles.input}
                          // onChangeText={formik.handleChange(
                          //   `linkedFacilities.${index}.pinCode`,
                          // )}
                          onChangeText={pin => handlePincodeChange(pin, index)}
                          onBlur={formik.handleBlur(
                            `linkedFacilities.${index}.pinCode`,
                          )}
                          //value={pincode}
                          value={formik.values.linkedFacilities[index].pinCode}
                        />

                        {formik.touched.linkedFacilities?.[index]?.pinCode &&
                          formik.errors.linkedFacilities?.[index]?.pinCode && (
                            <Text style={styles.error}>
                              {formik.errors.linkedFacilities?.[index]?.pinCode}
                            </Text>
                          )}
                      </View>

                      <View style={styles.boxContainer}>
                        <Text style={styles.label}>Address Line 1</Text>
                        <TextInput placeholderTextColor="#000"

                          style={styles.input}
                          multiline
                          numberOfLines={5}
                          onChangeText={formik.handleChange(
                            `linkedFacilities.${index}.addressLine1`,
                          )}
                          onBlur={formik.handleBlur(
                            `linkedFacilities.${index}.addressLine1`,
                          )}
                          value={
                            formik.values.linkedFacilities[index].addressLine1
                          }
                        />

                        {formik.touched.linkedFacilities?.[index]
                          ?.addressLine1 &&
                          formik.errors.linkedFacilities?.[index]
                            ?.addressLine1 && (
                            <Text style={styles.error}>
                              {
                                formik.errors.linkedFacilities?.[index]
                                  ?.addressLine1
                              }
                            </Text>
                          )}
                      </View>

                      <View style={styles.boxContainer}>
                        <Text style={styles.label}>Address Line 2</Text>
                        <TextInput placeholderTextColor="#000"

                          style={styles.input}
                          multiline
                          numberOfLines={5}
                          onChangeText={formik.handleChange(
                            `linkedFacilities.${index}.addressLine2`,
                          )}
                          onBlur={formik.handleBlur(
                            `linkedFacilities.${index}.addressLine2`,
                          )}
                          value={
                            formik.values.linkedFacilities[index].addressLine2
                          }
                        />

                        {formik.touched.linkedFacilities?.[index]
                          ?.addressLine2 &&
                          formik.errors.linkedFacilities?.[index]
                            ?.addressLine2 && (
                            <Text style={styles.error}>
                              {
                                formik.errors.linkedFacilities?.[index]
                                  ?.addressLine2
                              }
                            </Text>
                          )}
                      </View>

                      <View style={styles.boxContainer}>
                        <Text style={styles.label}>City</Text>
                        <TextInput placeholderTextColor="#000"

                          style={styles.input}
                          onChangeText={formik.handleChange(
                            `linkedFacilities.${index}.city`,
                          )}
                          onBlur={formik.handleBlur(
                            `linkedFacilities.${index}.city`,
                          )}
                          value={formik.values.linkedFacilities[index].city}
                        />

                        {formik.touched.linkedFacilities?.[index]?.city &&
                          formik.errors.linkedFacilities?.[index]?.city && (
                            <Text style={styles.error}>
                              {formik.errors.linkedFacilities?.[index]?.city}
                            </Text>
                          )}
                      </View>

                      <View style={styles.boxContainer}>
                        <Text style={styles.label}>State</Text>
                        <TextInput placeholderTextColor="#000"

                          style={styles.input}
                          onChangeText={formik.handleChange(
                            `linkedFacilities.${index}.state`,
                          )}
                          onBlur={formik.handleBlur(
                            `linkedFacilities.${index}.state`,
                          )}
                          value={formik.values.linkedFacilities[index].state}
                        />

                        {formik.touched.linkedFacilities?.[index]?.state &&
                          formik.errors.linkedFacilities?.[index]?.state && (
                            <Text style={styles.error}>
                              {formik.errors.linkedFacilities?.[index]?.state}
                            </Text>
                          )}
                      </View>

                      <View style={styles.boxContainer}>
                        <Text style={styles.label}>Country</Text>
                        <TextInput placeholderTextColor="#000"

                          style={styles.input}
                          onChangeText={formik.handleChange(
                            `linkedFacilities.${index}.country`,
                          )}
                          onBlur={formik.handleBlur(
                            `linkedFacilities.${index}.country`,
                          )}
                          value={formik.values.linkedFacilities[index].country}
                        />
                        {formik.touched.linkedFacilities?.[index]?.country &&
                          formik.errors.linkedFacilities?.[index]?.country && (
                            <Text style={styles.error}>
                              {formik.errors.linkedFacilities?.[index]?.country}
                            </Text>
                          )}
                      </View>

                      <View style={styles.boxContainer}>
                        <Text style={styles.label}>Amenities</Text>

                        <View
                          style={{
                            borderWidth: 1,
                            borderRadius: 0,
                            borderColor: '#ccc',
                            height: 175,
                          }}>
                          <QuillEditor
                            value={
                              formik?.values?.linkedFacilities?.[index]
                                ?.amenities
                            }
                            autoSize
                            ref={_editor}
                            quill={{ theme: 'snow', placeholder: 'write here ' }}
                            webview={{
                              scrollEnabled: true,
                              style: {
                                borderWidth: 1,
                                borderRadius: 6,
                                borderColor: '#ccc',
                              },
                              nestedScrollEnabled: true,
                              showsHorizontalScrollIndicator: true,
                              showsVerticalScrollIndicator: true,
                            }}
                            onHtmlChange={e => {
                              formik.setFieldValue(
                                `linkedFacilities.${index}.amenities`,
                                e.html,
                              );
                            }}
                            initialHtml={
                              formik?.values?.linkedFacilities?.[index]
                                ?.amenities
                            }
                          />
                        </View>
                      </View>
                    </View>
                  ))}
                </View>

                <View
                  style={{
                    alignSelf: 'flex-start',
                    marginVertical: 10,
                    marginHorizontal: 20,
                  }}>
                  <TouchableOpacity onPress={addAddress}>
                    <Text
                      style={{
                        borderWidth: 1,
                        borderColor: 'white',
                        paddingVertical: 10,
                        paddingHorizontal: 15,
                        marginRight: 10,
                        fontWeight: '700',
                        color: 'white',
                        borderRadius: 10,
                        backgroundColor: 'lightskyblue',
                      }}>
                      Add
                    </Text>
                  </TouchableOpacity>
                </View>
                {/* <View
          style={{ backgroundColor: 'lightblue', padding: 10, borderWidth: 0 }}>

        </View> */}

                {/* Show Revenue Sharing and Academy Availability only if affiliationType is 'academy' */}
{user?.data?.affiliationType === 'academy'  && !loading && (
  <>
    {/* Revenue Sharing Section */}
    <View style={{marginTop: 20, padding: 10, borderRadius: 8}}>
      <Text
        style={{
          paddingVertical: 10,
          paddingTop: 20,
          color: 'black',
          borderTopWidth: 1,
          borderColor: 'lightblue',
          fontWeight: '500',
          fontSize: 16,
          fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
        }}>
        Revenue Sharing
      </Text>
      <View style={styles.boxContainer}>
        <Text style={styles.label}>Coach Share (%)</Text>
        <TextInput
          style={{
            paddingHorizontal: 12,
            width: '100%',
            borderRadius: 6,
            borderWidth: 1,
            borderColor: '#d1d5db',
            paddingVertical: 6,
            color: '#111827',
            backgroundColor: '#f3f4f6',
            shadowColor: '#000',
            shadowOpacity: 0.05,
            shadowRadius: 2,
            shadowOffset: {width: 0, height: 1},
            fontSize: 14,
            lineHeight: 20,
            placeholderTextColor: '#9ca3af',
            cursor: 'not-allowed',
          }}
          value={"60"}
          editable={false}
        />
      </View>
      <View style={styles.boxContainer}>
        <Text style={styles.label}>Academy Share (%)</Text>
        <TextInput
          style={{
            paddingHorizontal: 12,
            width: '100%',
            borderRadius: 6,
            borderWidth: 1,
            borderColor: '#d1d5db',
            paddingVertical: 6,
            color: '#111827',
            backgroundColor: '#f3f4f6',
            shadowColor: '#000',
            shadowOpacity: 0.05,
            shadowRadius: 2,
            shadowOffset: {width: 0, height: 1},
            fontSize: 14,
            lineHeight: 20,
            placeholderTextColor: '#9ca3af',
            cursor: 'not-allowed',
          }}
          value={"40"}
          editable={false}
        />
      </View>
    </View>

    {/* Academy Availability Section */}
    <View style={{marginTop: 20, padding: 10, borderRadius: 8}}>
      <Text
        style={{
          paddingVertical: 10,
          paddingTop: 20,
          color: 'black',
          fontWeight: '500',
          fontSize: 16,
          fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
        }}>
        Academy Availability
      </Text>

      <View style={styles.boxContainer}>
        <Text style={styles.label}>Start Date</Text>
        <TextInput
          style={{
            paddingHorizontal: 12,
            width: '100%',
            borderRadius: 6,
            borderWidth: 1,
            borderColor: '#d1d5db',
            paddingVertical: 6,
            color: '#111827',
            backgroundColor: '#f3f4f6',
            shadowColor: '#000',
            shadowOpacity: 0.05,
            shadowRadius: 2,
            shadowOffset: {width: 0, height: 1},
            fontSize: 14,
            lineHeight: 20,
            placeholderTextColor: '#9ca3af',
            cursor: 'not-allowed',
          }}
          value={
            user?.data?.academyAvailability?.startDate
              ? new Date(user.data.academyAvailability.startDate).toLocaleDateString('en-GB')
              : ""
          }
          editable={false}
        />
      </View>

      <View style={styles.boxContainer}>
        <Text style={styles.label}>End Date</Text>
        <TextInput
          style={{
            paddingHorizontal: 12,
            width: '100%',
            borderRadius: 6,
            borderWidth: 1,
            borderColor: '#d1d5db',
            paddingVertical: 6,
            color: '#111827',
            backgroundColor: '#f3f4f6',
            shadowColor: '#000',
            shadowOpacity: 0.05,
            shadowRadius: 2,
            shadowOffset: {width: 0, height: 1},
            fontSize: 14,
            lineHeight: 20,
            placeholderTextColor: '#9ca3af',
            cursor: 'not-allowed',
          }}
          value={
            user?.data?.academyAvailability?.endDate
              ? new Date(user.data.academyAvailability.endDate).toLocaleDateString('en-GB')
              : ""
          }
          editable={false}
        />
      </View>

      <View style={styles.boxContainer}>
        <Text style={styles.label}>Start Time</Text>
        <TextInput
          style={{
            paddingHorizontal: 12,
            width: '100%',
            borderRadius: 6,
            borderWidth: 1,
            borderColor: '#d1d5db',
            paddingVertical: 6,
            color: '#111827',
            backgroundColor: '#f3f4f6',
            shadowColor: '#000',
            shadowOpacity: 0.05,
            shadowRadius: 2,
            shadowOffset: {width: 0, height: 1},
            fontSize: 14,
            lineHeight: 20,
            placeholderTextColor: '#9ca3af',
            cursor: 'not-allowed',
          }}
          value={user?.data?.academyAvailability?.startTime || ""}
          editable={false}
        />
      </View>

      <View style={styles.boxContainer}>
        <Text style={styles.label}>End Time</Text>
        <TextInput
          style={{
            paddingHorizontal: 12,
            width: '100%',
            borderRadius: 6,
            borderWidth: 1,
            borderColor: '#d1d5db',
            paddingVertical: 6,
            color: '#111827',
            backgroundColor: '#f3f4f6',
            shadowColor: '#000',
            shadowOpacity: 0.05,
            shadowRadius: 2,
            shadowOffset: {width: 0, height: 1},
            fontSize: 14,
            lineHeight: 20,
            placeholderTextColor: '#9ca3af',
            cursor: 'not-allowed',
          }}
          value={user?.data?.academyAvailability?.endTime || ""}
          editable={false}
        />
      </View>

      {/* Selected Days Field */}
      <View style={styles.boxContainer}>
        <Text style={styles.label}>Selected Days</Text>
        <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 8, marginTop: 8 }}>
          {DAYS_OF_WEEK.map((day, idx) => {
            const selectedDays = user?.data?.academyAvailability?.days || [];
            const isActive = selectedDays.includes(day);
            return (
              <View
                key={day + idx}
                style={{
                  backgroundColor: isActive ? '#dbeafe' : '#f3f4f6',
                  borderRadius: 6,
                  borderWidth: 1,
                  borderColor: isActive ? '#93c5fd' : '#d1d5db',
                  paddingHorizontal: 16,
                  paddingVertical: 8,
                  minHeight: 38,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 8,
                  marginBottom: 8,
                  cursor: 'not-allowed',
                }}
              >
                <Text style={{
                  color: isActive ? '#1e40af' : '#6b7280',
                  fontSize: 14,
                  fontWeight: '500',
                }}>{day}</Text>
              </View>
            );
          })}
        </View>
      </View>
    </View>
  </>
)}



                <View style={{ alignSelf: 'flex-end', marginTop: '10%' }}>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-around',
                      marginVertical: 20,
                      marginHorizontal: 20,
                    }}>
                    <TouchableOpacity onPress={handleReset}>
                      <Text
                        style={{
                          borderWidth: 1,
                          borderColor: 'grey',
                          padding: 10,
                          marginRight: 10,
                          fontWeight: '700',
                          color: 'black',
                          borderRadius: 10,
                          fontFamily:
                            'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                        }}>
                        Cancel
                      </Text>
                    </TouchableOpacity>

                    <TouchableOpacity onPress={handleFormSubmit}>
                      <Text
                        style={{
                          borderWidth: 1,
                          borderColor: 'white',
                          padding: 10,
                          fontWeight: '700',
                          color: 'white',
                          borderRadius: 10,
                          backgroundColor: 'lightskyblue',
                          fontFamily:
                            'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                        }}>
                        Save
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </View>
          </ScrollView>
        </View>
      )}
    </SafeAreaView>
  );
};

export default Registration;

const styles = StyleSheet.create({
  loadingContainer: {
    height: '100%',

    justifyContent: 'center',
    alignItems: 'center',
  },
  boxContainer: {
    marginTop: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
    //marginLeft:2
  },
  bookingAlign: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: "6%",
    width: "80%"
    // marginRight:"4%"
  },
  radioGroup: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 10, // Adds vertical margin for spacing
  },
  radioOption: {
    flexDirection: 'row', // Aligns the radio button and label horizontally
    alignItems: 'center', // Vertically centers the items
    marginRight: 20, // Adds space between the "Yes" and "No" options
  },
  input: {
    flex: 1,
    marginTop: 4,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#ccc',
    color: '#333',
    fontSize: 14,
  },
  error: {
    fontSize: 12,
    color: 'red',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },

  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: '2%',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 6,
    //paddingVertical: '1%',
    paddingHorizontal: '1%',
  },
  textInput: {
    flex: 1,
    height: 50,
    color: '#000',
  },
  calendarIcon: {
    padding: '1%',
  },
  multiInput: {
    marginTop: 4,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#ccc',
    color: '#333',
    fontSize: 14,
  },

  editor: {
    // flex: 1,
    padding: 0,
    borderColor: 'gray',
    borderWidth: 0,
    // marginHorizontal: 30,
    marginVertical: 5,
    backgroundColor: 'white',
    minHeight: 150,
    maxHeight: 500,
    borderRadius: 6,
    borderColor: '#ccc',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: '15%',
    width: '92%',
    maxHeight: '70%',
  },
  option: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  optionText: {
    fontSize: 16,
    color: '#000',
  },
  closeButton: {
    padding: 10,
    backgroundColor: '#000',
    alignItems: 'center',
    borderRadius: 10,
    marginTop: '3%',
  },
  scrollView: {
    flexDirection: 'column',
    marginVertical: '2%',
  },
});