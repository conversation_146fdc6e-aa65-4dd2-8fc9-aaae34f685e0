import { FlatList, StyleSheet, Text, TouchableOpacity, View, ActivityIndicator, ScrollView, } from 'react-native'
import React, { useEffect, useState, useCallback } from 'react'
import { Badge, DataTable } from 'react-native-paper';
import moment from 'moment';
import { Image } from 'react-native';
import axios from 'axios';
import { API } from '@env';
import { useAuth } from '../../components/Auth/AuthContext';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { TextInput } from 'react-native';
import { Picker } from '@react-native-picker/picker';
import Icon from 'react-native-vector-icons/FontAwesome';
import DatePicker from 'react-native-modern-datepicker';


const CoachBooking = () => {

  const [bookingDetails, setBookingDetails] = useState([])
  const [loading, setLoading] = useState(true);
  const navigation = useNavigation()
  const {user, setPageName} = useAuth();
  const [classType, setClassType] = useState('');
  const [statusType, setStatusType] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStartDate, setSelectedStartDate] = useState('')
  const [selectedEndDate, setSelectedEndDate] = useState('')
  const [open, setOpen] = useState(false);
  const [openEndDate, setOpenEndDate] = useState(false);
  const [arrayLength, setArrayLength] = useState('')

  // Initial load
  useEffect(() => {
    getCoachBooking()
    setClassType('')
    setStatusType('')
    setSearchQuery('')
    setSelectedStartDate('')
    setSelectedEndDate('')
    setTimeout(() => {
      setLoading(false)
    }, 3000)
  }, [])

  // Refresh when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      console.log('Booking screen focused - refreshing bookings');
      getCoachBooking();
      return () => {
        // Cleanup function if needed
      };
    }, [])
  );

  const getCoachBooking = async () => {
    try {
      setLoading(true)
      const response = await axios.get(`${API}/api/booking?coachId=${user?.data?._id}`, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${user?.data?.token}`,
        },
      })
      setBookingDetails(response?.data?.data)
      const finalResult = response?.data?.data
      setArrayLength(finalResult.length)

    }
    catch (error) {
      console.log("error", error)
    }
    finally {
      setLoading(false)
    }

  }
  const showDatePicker = () => {
    setOpen(!open);
  };

  const showEndDatePicker = () => {
    setOpenEndDate(!openEndDate)
  }

  function handleStartDateChange(propDate) {
    // console.log("first startttt",propDate)

    const formattedDate = moment(propDate, 'YYYY/MM/DD').format('DD-MMM-YYYY');
    setSelectedStartDate(formattedDate)

    try {
      setSelectedStartDate(formattedDate)
      setOpen(false)


    } catch (error) {
      setOpen(false);
    }
  }

  function handleEndDateChange(propDate) {
    propDate.replace(/\//g, '-')
    const formattedDate = moment(propDate, 'YYYY/MM/DD').format('DD-MMM-YYYY');
    try {
      if ((selectedStartDate !== "")) {
        setSelectedEndDate(formattedDate)
        setOpenEndDate(false)

        // setTimeout(() => {
        handleSearchFilter({
          search: searchQuery,
          clas: classType,
          status: statusType,
          startdate: selectedStartDate,
          enddate: formattedDate,

        })
        // },2000)
      }
      else {
        alert("End date cannot be before start Date")
        setOpenEndDate(false)
      }
    } catch (error) {
      setOpenEndDate(false);
    }
  }
  const handleLoadMore = () => {
    if (!loading) {
      getCoachBooking()
    }
  }
  const ListEndLoader = () => {
    if (loading) {
      return <ActivityIndicator size={"large"} />
    }

  }

  const renderItem = ({ item, index }) => {

    var bookingDateMoment = moment(item?.createdAt);
    var formattedBookingDate = bookingDateMoment.format('Do MMMM YYYY');


    let str = item?.courseName;
    let coursename = str && str.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');



    return (
      <ScrollView>
        <View style={{flex: 1, borderBottomWidth: 1, borderColor: '#DFDFDF'}}>
          <TouchableOpacity
            onPress={() => {
              setPageName('Booking Details');
              navigation.navigate('BookingDetails', {booking: item});
            }}
            key={item.id}>
            <View
              style={{
                borderColor: '#DFDFDF',
                paddingVertical: '2%',
                // borderTopWidth: 1,
                paddingHorizontal: '2%',
              }}
              key={item.id}>
              <DataTable>
                <DataTable.Row style={styles.row}>
                  <DataTable.Cell>
                    <Text style={styles.cell}>Booking Id :</Text>
                  </DataTable.Cell>
                  <DataTable.Cell>
                    <Text style={styles.value}>{item?.bookingId}</Text>
                  </DataTable.Cell>
                </DataTable.Row>
                <DataTable.Row style={styles.row}>
                  <DataTable.Cell>
                    <Text style={styles.cell}>Course Name :</Text>
                  </DataTable.Cell>
                  <DataTable.Cell>
                    <Text style={styles.value}>{coursename}</Text>
                  </DataTable.Cell>
                </DataTable.Row>

                <DataTable.Row style={styles.row}>
                  <DataTable.Cell>
                    <Text style={styles.cell}>Course Type :</Text>
                  </DataTable.Cell>
                  <DataTable.Cell>
                    <Text style={styles.value}>{item?.courseType}</Text>
                  </DataTable.Cell>
                </DataTable.Row>

                <DataTable.Row style={styles.row}>
                  <DataTable.Cell>
                    <Text style={styles.cell}>Customer :</Text>
                  </DataTable.Cell>
                  <DataTable.Cell>
                    <Text style={styles.value}>{item?.playerName}</Text>
                  </DataTable.Cell>
                </DataTable.Row>

                <DataTable.Row style={styles.row}>
                  <DataTable.Cell>
                    <Text style={styles.cell}>Booking Date :</Text>
                  </DataTable.Cell>
                  <DataTable.Cell>
                    <Text style={styles.value}>{formattedBookingDate}</Text>
                  </DataTable.Cell>
                </DataTable.Row>
              </DataTable>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginVertical: '5%',
                }}>
                <View style={{flexDirection: 'row', marginLeft: 12}}>
                  <View style={styles.buttonV}>
                    <Text style={styles.buttonT}>
                      {(item?.courseType).charAt(0).toUpperCase() +
                        (item?.courseType).slice(1)}
                    </Text>
                  </View>
                  <View
                    style={[
                      {
                        borderWidth: 1,
                        //borderColor: '#4CAF50',
                        borderRadius: 6,
                        marginLeft: 5,
                      },
                      {
                        borderColor:
                          item?.status?.toLowerCase() === 'active'
                            ? 'green'
                            : 'red',
                      },
                    ]}>
                    <Text
                      style={[
                        {
                          paddingHorizontal: 10,
                          paddingVertical: 5,
                          //color: '#4CAF50',
                          fontWeight: '500',
                          fontFamily:
                            'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                        },
                        {
                          color:
                            item?.status?.toLowerCase() === 'active'
                              ? 'green'
                              : 'red',
                        },
                      ]}>
                      {(item?.status).charAt(0).toUpperCase() +
                        (item?.status).slice(1)}
                    </Text>
                  </View>
                </View>

                <View
                  style={{
                    flexDirection: 'row',
                    marginRight: '3%',
                    alignItems: 'center',
                  }}>
                  <View>
                    <Text
                      style={{
                        fontSize: 16,
                        fontWeight: '500',
                        color: '#0095FF',
                        fontFamily:
                          'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                      }}>
                      {`₹${Math.ceil(
                        item.classes
                          .reduce((acc, curr) => acc + curr.fees, 0)
                          .toFixed(2),
                      )}`}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </ScrollView>
    );
  };

  const handleSearchFilter = async ({ search, clas, status, startdate, enddate }) => {
    try {
      //console.log("shdakjshkjsf",searchQuery,classType,status)
      let queryString = "";
      if (search) {
        queryString += `courseName=${search}`;
      }
      if (startdate) {
        queryString += `${queryString ? "&" : ""}startDate=${startdate}`;
      }
      if (enddate) {
        queryString += `${queryString ? "&" : ""}endDate=${enddate}`;
      }
      if (clas) {
        queryString += `${queryString ? "&" : ""}courseType=${clas}`;
      }
      if (status) {
        queryString += `${queryString ? "&" : ""}status=${status}`;
      }
      console.log( `${API}/api/course/filter?coachEmail=${user?.data?._id}&${
        queryString ? `${queryString}` : ""}`)
      const response = await axios.get(
        `${API}/api/booking/?coachId=${user?.data?._id}&${queryString ? `${queryString}` : ""}`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${user?.data?.token}`,
          },
        }
      );

      const finalResult = await response?.data?.data

      setArrayLength(finalResult?.length)

      setBookingDetails([])
      setBookingDetails(finalResult)

    }
    catch (error) {
      console.error("error", error);
      setClassType('')
      setStatusType('')
      setSearchQuery('')
    }

  }

  const seachFilterComp = () => {
    return (

      <View style={{
        borderColor: '#DFDFDF',
        paddingVertical: '2%',
        borderBottomWidth: 1,
      }}>

        <View style={styles.boxContainer}>
          <Text style={[styles.label, { paddingHorizontal: '4%' }]}>
            Bookings
          </Text>
        </View>

        <View style={styles.search}>

          <TouchableOpacity
          //onPress={handleSearchFilter}
          >
            <Icon name="search" size={20} color="grey" />
          </TouchableOpacity>

          <TextInput
            keyboardType="default"
            style={styles.input}
            placeholder="Search"
            placeholderTextColor={'#000'}
            onChangeText={text => {
              setSearchQuery(text);
              if (
                text.length >= 3 ||
                text.length == 0
              ) {
                handleSearchFilter({
                  search: text,
                  clas: classType,
                  status: statusType,
                  startdate: selectedStartDate,
                  enddate: selectedEndDate,
                })
              }
            }}
            value={searchQuery}
          />

        </View>

        <View style={styles.dropdownContainer}>
          <View
            style={{
              flex: 1,
              borderColor: '#e5e7eb',
              borderWidth: 1,
              borderRadius: 6,
              marginRight: 6,
            }}>
            <Picker
              selectedValue={classType}

              onValueChange={(itemValue, itemIndex) => {
                setClassType(itemValue),
                  handleSearchFilter({
                    search: searchQuery,
                    clas: itemValue,
                    status: statusType,
                    startdate: selectedStartDate,
                    enddate: selectedEndDate,
                  })
              }}
              style={styles.dropdown}
            >
              {/* <Picker.Item label="Class Type" value="" /> */}
              <Picker.Item label="Class" value="class" />
              <Picker.Item label="Course" value="course" />
              <Picker.Item label="All" value="" />
              <Picker.Item label="Class Type" value="" />
            </Picker>
          </View>

          <View
            style={{
              flex: 1,
              borderColor: '#e5e7eb',
              borderWidth: 1,
              borderRadius: 6,
            }}>
            <Picker
              selectedValue={statusType}
              onValueChange={(itemValue, itemIndex) => {
                setStatusType(itemValue),
                  handleSearchFilter({
                    search: searchQuery,
                    clas: classType,
                    status: itemValue,
                    startdate: selectedStartDate,
                    enddate: selectedEndDate,
                  })
              }}
              style={styles.dropdown}
            >

              <Picker.Item label="Completed" value="Completed" />
              {/* <Picker.Item label="In progress" value="In progress" /> */}
              <Picker.Item label="Pending" value="Pending" />
              {/* <Picker.Item label="Canceled" value="Canceled" /> */}
              <Picker.Item label="All" value="" />
              <Picker.Item label="Status Type" value="" />
            </Picker>
          </View>
        </View>

        <View style={{ flexDirection: "row", justifyContent: 'space-between', marginHorizontal: '3%', borderWidth: 0, marginBottom: 5 }}>
          {/* start dateeee */}

          <View style={[styles.inputContainer, { marginRight: 5, }]}>
            <TextInput
              style={{ color: 'black' }}
              placeholder="Start Date"
              placeholderTextColor={'#000'}
              name="startDate"
              value={selectedStartDate ? selectedStartDate : ''}
              editable={false}
            />

            <TouchableOpacity
              onPress={showDatePicker}
              style={styles.calendarIcon}>
              <Icon name="calendar" size={20} color="black" />
            </TouchableOpacity>
          </View>

          {/* start date  */}


          {/* enddatee */}
          <View style={styles.inputContainer}>
            <TextInput
              style={{ color: 'black' }}
              placeholder="End Date"
              placeholderTextColor={'#000'}
              name="endDate"
              value={selectedEndDate ? selectedEndDate : ''}
              //value={formik.values.endDate}
              editable={false}
            />

            <TouchableOpacity
              onPress={showEndDatePicker}
              style={styles.calendarIcon}>
              <Icon name="calendar" size={20} color="black" />
            </TouchableOpacity>
          </View>
          {/* end date */}
        </View>


        {/* start date picker */}
        {open && (
          <DatePicker
            mode="calendar"
            name="startDate"
            minDate={new Date()}
            selected={selectedStartDate}
            // onMonthChange={(month, year) => console.log(month, year)}
            onDateChange={handleStartDateChange}
          />
        )}

        {/* end date picker  */}
        {openEndDate && (
          <DatePicker
            mode="calendar"
            name="endDate"
            selected={selectedEndDate}
            //selected={formik.values.endDate}
            // onMonthChange={(month, year) => console.log(month, year)}
            onDateChange={handleEndDateChange}
          />
        )}

      </View>
    )
  }

  return (

    <ScrollView>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" />
        </View>
      ) :
        <View
          style={{
            backgroundColor: '#F5F8FC',
          }}>
          <View style={{ backgroundColor: '#F5F8FC' }}>{seachFilterComp()}</View>

          {(arrayLength === 0 || arrayLength === undefined) ? <View style={{ flex: 1, padding: 5, alignItems: 'center', backgroundColor: 'white' }}>
            <Text style={{ color: 'black', fontSize: 19, padding: 4, fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e' }}>No Bookings Found</Text>
          </View> :

            <View style={{ borderWidth: 0, backgroundColor: 'white' }}>
              <FlatList
                data={bookingDetails}
                keyExtractor={(item, index) => index.toString()}
                renderItem={renderItem}
              //  onEndReached={handleLoadMore}
              //  onEndReachedThreshold={0.8}
              //  ListFooterComponent={ListEndLoader}
              />

            </View>

          }
        </View>

      }
    </ScrollView>
  )
}

export default CoachBooking

const styles = StyleSheet.create({
  loadingContainer: {

    height: '100%',

    justifyContent: 'center',
    alignItems: 'center',
  },

  // search comp

  search: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: '3%',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 6,
    paddingVertical: '1%',
    paddingHorizontal: '7%',
    marginHorizontal: 10,
  },
  input: {
    height: 40,
    width: '95%',
    //borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
    paddingHorizontal: 10,
    alignSelf: 'center',
    //  marginRight: 10,
  },
  dropdownContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: '2%',
    marginHorizontal: '3%',
    borderWidth: 0,
  },

  boxContainer: {
    marginTop: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e'
  },
  inputContainer: {
    // width:'50%',
    flex: 1,
    flexDirection: 'row',
    justifyContent: "space-between",
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 6,
    paddingVertical: '1%',
    paddingHorizontal: '2%',
    color: 'black'
    //backgroundColor:'white',
    //marginHorizontal:'2.5%',
  },

  //   table - card

  cell: {
    fontSize: 14,
    fontWeight: '600',
    borderBottomWidth: 0, // This will remove the bottom border from all cells
    color: 'black',
    borderWidth: 0,
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e'
    //alignItems: 'flex-start',

  },
  row: {
    borderBottomWidth: 0,
    marginVertical: '-1.5%',
  },
  value: {
    color: '#626262',
    fontSize: 14,
    fontWeight: '400',
    borderWidth: 0,
    //alignItems: 'center',
    //justifyContent:'center',
    flexWrap: 'wrap',
    marginLeft: '-25%',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e'
    //marginTop: '1%',
  },

  buttonV: {
    borderWidth: 1,
    borderColor: '#0068FF',
    borderRadius: 6,
  },
  buttonT: {
    paddingHorizontal: 10,
    paddingVertical: 6,
    color: '#0068FF',
    fontWeight: '500',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e'
  },
})
