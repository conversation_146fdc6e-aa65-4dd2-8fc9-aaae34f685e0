import {
  ScrollView,
  StyleSheet,
  Text,
  View,
  useWindowDimensions,
  ActivityIndicator,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {DataTable} from 'react-native-paper';
import {useRoute} from '@react-navigation/native';
import axios from 'axios';
import {API} from '@env';
import moment from 'moment';
import {Image} from 'react-native';
import RenderHtml from 'react-native-render-html';

const BookingDetails = () => {
  const route = useRoute();
  const {booking} = route.params;

  console.log(booking, '----->booking here');
  const {width} = useWindowDimensions();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setTimeout(() => {
      setLoading(false);
    }, 3000);
  }, []);

  const source = {
    html: booking && booking?.courseId?.description,
  };

  const formatTimeDetails = time => {
    const [hours, minutes] = time.split(':');
    let hours12 = parseInt(hours, 10) % 12;
    hours12 = hours12 === 0 ? 12 : hours12;

    // Determine whether it's AM or PM
    const period = parseInt(hours, 10) < 12 ? 'AM' : 'PM';

    return `${hours12}:${minutes} ${period}`;
  };

  const renderTable = (item, index) => {
    let startDateMoment = moment(item?.startDate);
    let formattedDateStart = startDateMoment.format('Do MMMM YYYY');
    let endDateMoment = moment(item?.endDate);
    let formattedDateEnd = endDateMoment.format('Do MMMM YYYY');
    let formatedstartTime = formatTimeDetails(item?.startTime);

    let formatedendTime = formatTimeDetails(item?.endTime);

    return (
      <View
        style={{
          borderColor: '#DFDFDF',
          paddingVertical: '2%',
          borderBottomWidth: 1,
          paddingHorizontal: '2%',
        }}
        key={index}>
        <DataTable>
          <DataTable.Row style={styles.row}>
            <DataTable.Cell>
              <Text style={styles.cell}>Start Date :</Text>
            </DataTable.Cell>
            <DataTable.Cell>
              <Text style={styles.value}>
                {formattedDateStart && formattedDateStart}
              </Text>
            </DataTable.Cell>
          </DataTable.Row>
          <DataTable.Row style={styles.row}>
            <DataTable.Cell>
              <Text style={styles.cell}>End Date :</Text>
            </DataTable.Cell>
            <DataTable.Cell>
              <Text style={styles.value}>
                {formattedDateEnd && formattedDateEnd}
              </Text>
            </DataTable.Cell>
          </DataTable.Row>

          <DataTable.Row style={styles.row}>
            <DataTable.Cell>
              <Text style={styles.cell}>Start Time :</Text>
            </DataTable.Cell>
            <DataTable.Cell>
              <Text style={styles.value}>
                {formatedstartTime && formatedstartTime}
              </Text>
            </DataTable.Cell>
          </DataTable.Row>

          <DataTable.Row style={styles.row}>
            <DataTable.Cell>
              <Text style={styles.cell}>End Time :</Text>
            </DataTable.Cell>
            <DataTable.Cell>
              <Text style={styles.value}>
                {formatedendTime && formatedendTime}
              </Text>
            </DataTable.Cell>
          </DataTable.Row>

          <DataTable.Row style={styles.row}>
            <DataTable.Cell>
              <Text style={styles.cell}>Duration :</Text>
            </DataTable.Cell>
            <DataTable.Cell>
              <Text style={styles.value}>{item?.duration}</Text>
            </DataTable.Cell>
          </DataTable.Row>
        </DataTable>
      </View>
    );
  };

  var bookingDateMoment = moment(booking?.createdAt);
  var formattedBookingDate = bookingDateMoment.format('Do MMMM YYYY');

  // const platformFee = 0.1 * (booking?.pricePaid)
  // const gstFee = (0.18 * (booking?.pricePaid))
  // const courseFee =  ((booking?.pricePaid) - (gstFee) - (platformFee))

  const courseFee = Math.ceil(
    booking.classes.reduce((acc, current) => acc + current.fees, 0).toFixed(2),
  );

  return (
    <ScrollView>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" />
        </View>
      ) : (
        <View style={styles.container}>
          <View style={styles.pagebody}>
            {/* 1 */}
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'flex-end',
                paddingVertical: 10,
              }}>
              <View style={{flexWrap: 'wrap'}}>
                <Text
                  style={{
                    fontSize: 16,
                    color: 'black',
                    fontWeight: '600',
                    fontFamily:
                      'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                  }}>
                  Booking Id-
                </Text>
                <Text
                  style={{
                    fontSize: 14,
                    color: 'black',
                    fontWeight: '600',
                    fontFamily:
                      'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                  }}>
                  {booking?.bookingId}
                </Text>
              </View>
              <View
                style={[
                  {
                    borderWidth: 1,
                    borderRadius: 6,
                    marginLeft: 5,
                  },
                  {
                    borderColor:
                      booking?.status === 'active' ? 'green' : '#E7D745',
                  },
                  {
                    backgroundColor:
                      booking?.status === 'active' ? 'green' : ' #A39832',
                  },
                ]}>
                <Text
                  style={[
                    {
                      paddingHorizontal: 5,
                      paddingVertical: 2,
                      fontWeight: '500',
                      fontSize: 12,
                      fontFamily:
                        'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                    },
                    {
                      color:
                        booking?.status === 'active' ? '#4CAF50' : '#E7D745',
                    },
                  ]}>
                  {/* {(booking?.status).charAt(0).toUpperCase() +
                      (booking?.status).slice(1)} */}
                  {booking?.status == 'Pending' ? 'Active' : booking?.status}
                </Text>
              </View>
            </View>

            {/* 2 */}
            <View style={{borderWidth: 0}}>
              <Text
                style={{
                  fontSize: 16,
                  color: '#5A5D60',
                  lineHeight: 45,
                  fontWeight: '500',
                  fontFamily:
                    'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                }}>{`Booked On ${formattedBookingDate}`}</Text>
            </View>

            {/* 3 - card*/}
            <View style={styles.card}>
              <View style={{paddingVertical: 15, paddingHorizontal: 12}}>
                {/* image */}
                <View
                  style={{
                    height: 330,
                    width: '100%',
                    borderWidth: 0,
                    borderRadius: 10,
                  }}>
                  <Image
                    source={{uri: booking?.courseId?.images[0]?.url}}
                    alt={'course image'}
                    style={{height: '100%', width: '100%', borderRadius: 10}}
                  />
                </View>

                {/* name - price */}
                <View style={{borderWidth: 0, marginTop: 5}}>
                  <DataTable>
                    <DataTable.Row style={styles.row1}>
                      <DataTable.Cell>
                        <Text style={styles.cell1}>Course Name :</Text>
                      </DataTable.Cell>
                      <DataTable.Cell>
                        <Text style={[styles.value1]}>
                          {booking?.courseName}
                        </Text>
                      </DataTable.Cell>
                    </DataTable.Row>
                    <DataTable.Row style={styles.row1}>
                      <DataTable.Cell>
                        <Text style={styles.cell1}>Course Price :</Text>
                      </DataTable.Cell>
                      <DataTable.Cell>
                        <Text style={styles.value1}>{`₹${courseFee.toFixed(
                          2,
                        )}`}</Text>
                      </DataTable.Cell>
                    </DataTable.Row>
                  </DataTable>
                </View>

                {/* descript */}
                <View>
                  <RenderHtml
                    contentWidth={width}
                    source={source}
                    tagsStyles={styles}
                  />
                </View>

                {/* table=  */}

                <View style={{marginTop: '20px'}}>
                  <Text style={{marginTop: '20px'}}>Classes</Text>

                  {booking &&
                    booking?.classes?.length > 0 &&
                    booking?.classes?.map((item, index) => (
                      <View key={index}>{renderTable(item, index)}</View>
                    ))}
                </View>
                <View></View>
              </View>
            </View>

            {/* 4- player card */}
            <View style={styles.playerCard}>
              <View style={{paddingVertical: '7%', paddingHorizontal: 12}}>
                {/* player-info */}
                <View
                  style={{
                    borderBottomWidth: 1,
                    paddingVertical: '5%',
                    borderColor: '#C4C4C4',
                  }}>
                  <Text
                    style={{
                      paddingBottom: '5%',
                      color: 'black',
                      fontSize: 16,
                      fontWeight: '400',
                    }}>
                    Player Information
                  </Text>

                  <View>
                    <Text
                      style={
                        styles.info
                      }>{`${booking?.player?.firstName} ${booking?.player?.lastName}`}</Text>
                    <Text style={styles.info}>{booking?.player?.mobile}</Text>
                    <Text style={styles.info}>{booking?.player?.email}</Text>
                  </View>
                </View>

                {/* venue-info */}
                <View
                  style={{
                    borderBottomWidth: 1,
                    paddingVertical: '5%',
                    borderColor: '#C4C4C4',
                  }}>
                  <Text
                    style={{
                      paddingBottom: '5%',
                      color: 'black',
                      fontSize: 16,
                      fontWeight: '400',
                    }}>
                    Venue Information
                  </Text>

                  <View>
                    <Text
                      style={
                        styles.info
                      }>{`${booking?.courseId?.facility?.name}`}</Text>
                    <Text
                      style={
                        styles.info
                      }>{`${booking?.courseId?.facility?.addressLine1}, ${booking?.courseId?.facility?.addressLine2}`}</Text>
                    <Text
                      style={
                        styles.info
                      }>{`${booking?.courseId?.facility?.city}, ${booking?.courseId?.facility?.state}`}</Text>
                  </View>
                </View>

                {/* payment-info */}

                <View
                  style={{
                    borderBottomWidth: 1,
                    paddingVertical: '5%',
                    borderColor: '#C4C4C4',
                  }}>
                  <Text
                    style={{
                      paddingBottom: '5%',
                      color: 'black',
                      fontSize: 16,
                      fontWeight: '400',
                    }}>
                    Payment Information
                  </Text>

                  <View>
                    <Text
                      style={[
                        styles.info,
                        {paddingBottom: 5},
                      ]}>{`Payment Method:- ${booking?.paymentMode?.toUpperCase()}`}</Text>
                    <Text
                      style={
                        styles.info
                      }>{`Payment Id:- ${booking?.razorPayPaymentId}`}</Text>
                  </View>
                </View>

                {/* table-price */}
                <View style={{paddingVertical: '10%', paddingHorizontal: '2%'}}>
                  <View style={styles.box}>
                    <Text style={styles.title}>Course Price</Text>
                    <Text style={styles.price}>{`₹${courseFee.toFixed(
                      2,
                    )}`}</Text>
                  </View>

                  {/* <View style={styles.box}>
  <Text style={styles.title}>Platform Fee</Text>
  <Text style={styles.price}>{`₹${(platformFee.toFixed(2))}`}</Text>
</View>
<View style={styles.box}>
  <Text style={styles.title}>GST</Text>
  <Text style={styles.price}>{`₹${(gstFee.toFixed(2))}`}</Text>
</View>
<View style={styles.box}>
  <Text style={[styles.title,{color:'black'}]}>Order total</Text>
  <Text style={[styles.price,{color:'blue'}]}>{`₹${booking?.pricePaid}`}</Text>
</View> */}
                </View>
              </View>
            </View>
          </View>
        </View>
      )}
    </ScrollView>
  );
};

export default BookingDetails;

const styles = StyleSheet.create({
  loadingContainer: {
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },

  container: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 6,
    backgroundColor: '#F5F8FC',
    borderWidth: 0,
  },
  pagebody: {
    flex: 1,
    borderWidth: 0,
  },
  card: {
    borderWidth: 0,
    borderRadius: 6,
    borderColor: '#DFDFDF',
    elevation: 2,
    backgroundColor: 'white',
  },
  // h1: {
  //   fontSize: 24,
  //   fontWeight: 'bold',
  //   color: 'blue',
  // },
  p: {
    fontSize: 14,
    lineHeight: 24,
    color: '#626262',
    fontWeight: '400',
    fontFamily: 'Lato',
  },

  // table-1
  cell1: {
    fontSize: 14,
    fontWeight: '600',
    borderBottomWidth: 0, // This will remove the bottom border from all cells
    color: 'black',
    borderWidth: 0,
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  row1: {
    borderBottomWidth: 0,
    marginVertical: '-1.5%',
    marginLeft: '-5%',
  },
  value1: {
    color: '#626262',
    fontSize: 14,
    fontWeight: '400',
    borderWidth: 0,
    flexWrap: 'wrap',
    marginLeft: '-30%',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },

  //   table - card

  cell: {
    fontSize: 14,
    fontWeight: '600',
    borderBottomWidth: 0, // This will remove the bottom border from all cells
    color: 'black',
    borderWidth: 0,
    //alignItems: 'flex-start',
    marginLeft: '-5%',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  row: {
    borderBottomWidth: 0,
    marginVertical: '-2%',
  },
  value: {
    color: '#626262',
    fontSize: 14,
    fontWeight: '400',
    borderWidth: 0,
    fontFamily: 'Lato',
    flexWrap: 'wrap',
    marginLeft: '-25%',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },

  // player card
  playerCard: {
    marginVertical: '7%',
    flex: 1,
    //backgroundColor:'#F3F4F6',
    //backgroundColor:' rgb(243 244 246)',
    backgroundColor: 'lightgrey',
    borderRadius: 6,
    borderColor: '#DFDFDF',
  },
  info: {
    color: '#6B7280',
    fontSize: 15,
    fontWeight: '400',
  },

  // table-price
  box: {
    paddingVertical: '5%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderColor: '#C4C4C4',
  },
  title: {
    fontSize: 16,
  },
  price: {
    fontSize: 16,
    color: 'black',
  },
});
