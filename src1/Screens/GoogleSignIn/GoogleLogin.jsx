import { StyleSheet, Text, View, Button } from 'react-native'
import React from 'react'
import { API, ANDROID_CLIENT_ID, WEB_CLIENT_ID, IOS_CLIENT_ID } from '@env';
import { GoogleSignin, GoogleSigninButton, statusCodes } from '@react-native-google-signin/google-signin';
GoogleSignin.configure({
    webClientId: WEB_CLIENT_ID,
    androidClientId: ANDROID_CLIENT_ID,
    iosClientId: IOS_CLIENT_ID,
    offlineAccess: true,
    scopes: ['profile', 'email', 'https://www.googleapis.com/auth/calendar'],
  })
const GoogleLogin = () => {
    const handleGoogleLogin = async () => {
        setLoading(true);
        try {
            const response = await GoogleLogin();
            const { idToken, user } = response;

            if (idToken && user) {
                const myHeaders = new Headers();
            }
        } catch (error) {
            console.error("Login error: 82");
            setError(
                error?.response?.data?.error?.message || 'Something went wrong during Google login.'
            );
        } finally {

            setLoading(false);
        }
    };
    return (
        <View>
            <TouchableOpacity
                onPress={handleGoogleLogin}
                style={styles.googleSignInButton}>
                <Text style={styles.googleSignInButtonText}>
                    Sign in with Google
                </Text>
            </TouchableOpacity>

        </View>
    )
}

export default GoogleLogin

const styles = StyleSheet.create({
    googleSignInButton: {
        flexDirection: 'row',
        backgroundColor: '#FAFBFCFF',
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 5,
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 15,
        borderWidth: 1,
        borderColor: '#ddd',
    },
    googleSignInButtonText: {
        marginLeft: 10,
        color: '#000',
        fontSize: 16,
    },
    googleLogo: {
        width: 24,
        height: 24,
    },
})