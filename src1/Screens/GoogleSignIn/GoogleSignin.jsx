import { StyleSheet, Text, View, Button, Alert } from 'react-native'
import React, { useEffect , useState} from 'react'
import { useAuth } from '../../components/Auth/AuthContext';
import { GoogleSignin, GoogleSigninButton, statusCodes } from '@react-native-google-signin/google-signin';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { API, ANDROID_CLIENT_ID, WEB_CLIENT_ID, IOS_CLIENT_ID } from '@env';
import axios from 'axios';
GoogleSignin.configure({
  webClientId: WEB_CLIENT_ID,
  androidClientId: ANDROID_CLIENT_ID,
  iosClientId: IOS_CLIENT_ID,
  offlineAccess: true,
  scopes: ['profile', 'email', 'https://www.googleapis.com/auth/calendar'],
})

const GoogleLogin = async () => {
  await GoogleSignin.hasPlayServices();
  // await GoogleSignin.getTokens();
  const userInfo = await GoogleSignin.signIn();
  console.log("---user", userInfo)
  return userInfo;
};

const LoginWithGoogle = () => {
  const { login, isLoggedin, getUserDetails, setIsAuthenticated, isAuthenticated, isHeader, setIsHeader ,setCalendarLinked, calendarLinked, user } = useAuth()
  const [coachEmail, setCoachEmail] = useState(null);
  const [email, setEmail] = useState(null);
  useEffect(()=>{
    getUserDetails()
  },[])
  useEffect(() => {
    const retrieveData = async () => {
      let storedEmail = await AsyncStorage.getItem('coachEmail');
      setCoachEmail(storedEmail);
      setEmail(storedEmail);
    };
    retrieveData();
  }, [isLoggedin, isHeader]);

  // useEffect(() => {
  //   if (coachEmail) {
  //     handleGoogleLogin();
  //   }
  // }, [coachEmail]);


  const handleGoogleLogin = async () => {
    console.log("---hitting")
    try {
      const response = await GoogleLogin();
      const { user, serverAuthCode } = response;
      console.log("----serverAuthCode", serverAuthCode)

      if (user && serverAuthCode) {
        try {
          // Make sure coachEmail is available
          if (!coachEmail) {
            console.log("Coach email is missing, retrieving from AsyncStorage");
            const storedEmail = await AsyncStorage.getItem('coachEmail');
            if (storedEmail) {
              setCoachEmail(storedEmail);
            } else {
              console.error("Coach email not found in AsyncStorage");
              Alert.alert("Error", "User email not found. Please log in again.");
              return;
            }
          }

          const url = `${API}/api/calendar/google/redirect?code=${serverAuthCode}&state=${coachEmail}&mobile=true`;
          console.log("-------------->> URL", url);

          try {
            const response = await axios.get(url, {
              headers: {
                'Content-Type': 'application/json',
              },
            });
            console.log('Data retrieved successfully:', response);

            // Get updated user details with refreshToken
            const userDetails = await getUserDetails();
            console.log("User details after Google auth:", userDetails);

            if (userDetails?.data?.refreshToken) {
              console.log("Calendar linked successfully with refreshToken");
              setCalendarLinked(true);
              Alert.alert("Success", "Google Calendar linked successfully!");
            } else {
              console.log("No refreshToken found in user details");
              Alert.alert("Warning", "Calendar linked but refresh token not found. Please try again.");
            }
          } catch (error) {
            console.log('Error retrieving data:', error);
            if (error.response) {
              // The request was made and the server responded with a status code
              // that falls out of the range of 2xx
              console.log("Error response data:", error.response.data);
              console.log("Error response status:", error.response.status);
              console.log("Error response headers:", error.response.headers);
              Alert.alert("Error", `Server error: ${error.response.status}. Please try again.`);
              GoogleSignin.signOut();
            } else if (error.request) {
              // The request was made but no response was received
              console.log("Error request:", error.request);
              Alert.alert("Error", "No response from server. Please check your connection.");
              GoogleSignin.signOut();
            } else {
              // Something happened in setting up the request that triggered an Error
              console.log('Error message:', error.message);
              Alert.alert("Error", "Failed to link calendar. Please try again.");
              GoogleSignin.signOut();
            }
          }
        } catch (error) {
          console.log('Error in API call:', error);
          Alert.alert("Error", "Failed to process Google authentication. Please try again.");
          GoogleSignin.signOut();
        }
      } else {
        console.log('Missing user or serverAuthCode');
        Alert.alert("Error", "Failed to get authorization from Google. Please try again.");
        GoogleSignin.signOut();
      }
    } catch (error) {
      console.error("Login error:", error);
      Alert.alert("Error", "Google sign-in failed. Please try again.");
      GoogleSignin.signOut();
    }
  };

  return (
    <View>

      <View
        style={{

          display: 'flex',
          justifyContent: 'center',
          alignContent: 'center',
          alignSelf: 'center',
          margin: 'auto',
        }}>
        <Text style={{ marginVertical: 10, fontSize: 18, color: 'black' }}>Please Link your google calendar</Text>
        <Button title="Sign In with Google" onPress={handleGoogleLogin} />

      </View>
    </View>
  )
}

export default LoginWithGoogle

const styles = StyleSheet.create({})