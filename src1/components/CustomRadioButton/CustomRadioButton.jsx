import React from 'react';
import { TouchableOpacity, View, Text, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

const CustomRadioButton = ({ selected, onPress, label, style, disabled=false }) => (
  <TouchableOpacity onPress={onPress} style={[styles.container, style]} activeOpacity={0.7} disabled={disabled}>
    <Icon
      name={selected ? 'radio-button-checked' : 'radio-button-unchecked'}
      size={18}
      color={selected ? '#007AFF' : '#888'}
    />
    <Text style={styles.label}>{label}</Text>
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
    gap: 8,
  },
  label: {
    fontSize: 12,
    color: '#222',
  },
});

export default CustomRadioButton; 