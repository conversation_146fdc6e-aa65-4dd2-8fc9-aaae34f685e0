import React, { useState, useEffect } from 'react';
import { ScrollView, RefreshControl } from 'react-native';

export const RefreshablePage = ({ children }) => {
  const [refreshing, setRefreshing] = useState(false);
  const [key, setKey] = useState(0); // Key to force component remount

  const onRefresh = () => {
    setRefreshing(true);
    // Simulate a delay, replace this with your actual data fetching logic
    setTimeout(() => {
      setRefreshing(false);
    }, 2000); // Adjust the delay as needed
  };

  useEffect(() => {
    if (refreshing === false) {
      setKey(prevKey => prevKey + 1); // Force component remount when refresh is done
    }
  }, [refreshing]);

  return (
    <ScrollView
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
      key={key} // Key to force component remount
    >
      {children}
    </ScrollView>
  );
};
