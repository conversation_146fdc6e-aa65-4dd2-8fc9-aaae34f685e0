import React, { useEffect } from 'react';
import { Platform } from 'react-native';
import Orientation from 'react-native-orientation-locker';

// This component ensures the app is locked to portrait orientation
// It uses react-native-orientation-locker to handle orientation locking at the JS level
// This works in addition to the native configurations in:
// - iOS: AppDelegate.mm and Info.plist
// - Android: MainActivity.kt and AndroidManifest.xml

const OrientationLock = () => {
  useEffect(() => {
    // Lock to portrait orientation when component mounts
    Orientation.lockToPortrait();

    // Add event listener to handle any orientation changes
    Orientation.addOrientationListener(handleOrientationChange);

    return () => {
      // Cleanup: remove event listener when component unmounts
      Orientation.removeOrientationListener(handleOrientationChange);
    };
  }, []);

  // Force back to portrait if orientation somehow changes
  const handleOrientationChange = (orientation) => {
    if (orientation !== 'PORTRAIT') {
      Orientation.lockToPortrait();
    }
  };

  // This component doesn't render anything
  return null;
};

export default OrientationLock;
