import React, {useState, useCallback} from 'react';
import {View, StyleSheet, ActivityIndicator} from 'react-native';
import {useNavigation, useFocusEffect} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useAuth} from '../components/Auth/AuthContext';

const Home = () => {
  const [loading, setLoading] = useState(true);

  const navigation = useNavigation();
  const {
    isLoggedin,
    getUserDetails,
    setIsHeader,
    setIsAuthenticated,
    setPageName,
    isHeader,
  } = useAuth();

  const retrieveData = async () => {
    try {
      setLoading(true);
      const coachToken = await AsyncStorage.getItem('coachToken');
      const coachId = await AsyncStorage.getItem('coachId');
      const coachLoggedIn = await AsyncStorage.getItem('coachLoggedIn');
      if (coachLoggedIn) {
        setIsHeader(true);
        const detailsUser = await getUserDetails();
        if (
          coachToken &&
          coachId &&
          detailsUser?.data?.authStatus === 'authorized' &&
          detailsUser?.data?.status === 'active'
        ) {
          setPageName('Dashboard');
          navigation.navigate('Dashboard');
          setIsAuthenticated(true);
          setLoading(false);
        } else if (
          coachToken &&
          coachId &&
          detailsUser?.data?.authStatus === 'authorized' &&
          detailsUser?.data?.status?.toLowerCase() === 'inactive'
        ) {
          setPageName('KYC Details');
          navigation.navigate('KYCDetails');
          setLoading(false);
          setIsAuthenticated(false);
        } else if (
          coachToken &&
          coachId &&
          detailsUser?.data?.authStatus === 'unauthorized' &&
          detailsUser?.data?.status === 'inactive'
        ) {
          setPageName('Professional Details');
          navigation.navigate('ProfessionalDetails');
          setLoading(false);
          setIsAuthenticated(false);
        }
      } else {
        navigation.navigate('Login');
        setLoading(false);
        setIsAuthenticated(false);
      }
    } catch (error) {
      console.error('Error retrieving data 54:', error);
    }
  };

  useFocusEffect(
    useCallback(() => {
      retrieveData();
    }, [isLoggedin, isHeader]),
  );

  return (
    <View style={styles.container}>
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" />
        </View>
      )}
    </View>
  );
};

export default Home;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
