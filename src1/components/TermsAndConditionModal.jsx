import axios from 'axios';
import React, { useEffect, useState } from 'react';
import { View, Text, Modal, TouchableOpacity, StyleSheet, ScrollView, useWindowDimensions } from 'react-native';
import { API } from '@env';
import RenderHtml from 'react-native-render-html';

const TermsAndConditionsModal = ({ modalVisible, setModalVisible, saveData }) => {
  const [policyData, setPolicyData] = useState('');
  const { width } = useWindowDimensions();

  const closeModal = () => {
    setModalVisible(false);
  };

  const getPrivacyPolicy = async () => {
    try {
      const data = await axios.get(`${API}/api/cms/cms-termsAndCondition-details`);
      setPolicyData(data?.data[0]?.termsAndConditionData || '');
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getPrivacyPolicy();
  }, []);

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={modalVisible}
      onRequestClose={closeModal}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalView}>
          <ScrollView contentContainerStyle={styles.scrollViewContent}>
            <Text style={styles.modalTitle}>Terms and Conditions</Text>
            {policyData ? (
              <RenderHtml
                contentWidth={width * 0.7}
                source={{ html: policyData }}
                tagsStyles={{
                  body: { color: '#222', fontSize: 16 },
                  p: { marginBottom: 10 },
                }}
              />
            ) : (
              <Text style={styles.loadingText}>Loading...</Text>
            )}
          </ScrollView>
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.acceptButton}
              onPress={() => {
                setModalVisible(false);
                saveData.handleSubmit();
              }}
            >
              <Text style={styles.buttonText}>I accept</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.closeButton} onPress={closeModal}>
              <Text style={styles.buttonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalView: {
    width: '85%',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 24,
    maxHeight: '85%',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  scrollViewContent: {
    paddingBottom: 0,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#222',
    textAlign: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#888',
    textAlign: 'center',
    marginVertical: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    // marginTop: 20,
  },
  acceptButton: {
    backgroundColor: '#4CAF50',
    padding: 12,
    borderRadius: 6,
    flex: 1,
    marginRight: 10,
  },
  closeButton: {
    backgroundColor: '#6200EE',
    padding: 12,
    borderRadius: 6,
    flex: 1,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
    fontWeight: '600',
  },
});

export default TermsAndConditionsModal;
