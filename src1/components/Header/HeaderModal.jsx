import React, { useEffect, useState, useRef, } from 'react';
import { View, TouchableOpacity, Modal, Text, StyleSheet, Animated, Platform, Dimensions, TouchableWithoutFeedback, PanResponder } from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context'
import { Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../Auth/AuthContext';

const HeaderModal = ({ closeModal, modalVisible }) => {
  const [showCourse, setShowCourse] = useState(false)
  const [showProfile, setShowProfile] = useState(false)
  const navigation = useNavigation();
  const [key, setKey] = useState(Math.random().toString());

  // Get safe area insets for proper positioning on iOS
  const insets = useSafeAreaInsets();
  const { width: screenWidth } = Dimensions.get('window');

  // Calculate modal width (85% of screen width)
  const modalWidth = screenWidth * 0.85;

  // Initialize slide animation with proper offset considering screen width
  const slideAnim = useRef(new Animated.Value(-modalWidth)).current;
  const {isAuthenticated, setPageName} = useAuth();

  // Internal state to control modal visibility through position
  const [isModalOpen, setIsModalOpen] = useState(false);
  // Additional state to prevent iOS flash-back during close transition
  const [isClosing, setIsClosing] = useState(false);
  // Special state for iOS swipe gestures to completely isolate them
  const [isSwipeClosing, setIsSwipeClosing] = useState(false);

  // Clean animation functions following the example pattern
  const animateIn = () => {
    setIsModalOpen(true);
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const animateOut = () => {
    setIsClosing(true); // Mark as closing to prevent position resets
    Animated.timing(slideAnim, {
      toValue: -modalWidth,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      // iOS-specific fix: Delay state updates to prevent flash-back
      if (Platform.OS === 'ios') {
        // Keep modal off-screen and delay the close to prevent visual glitch
        setTimeout(() => {
          setIsModalOpen(false);
          setIsClosing(false);
          closeModal();
        }, 50);
      } else {
        // Android can handle immediate state updates
        setIsModalOpen(false);
        setIsClosing(false);
        closeModal();
      }
    });
  };

  // Immediate close for navigation scenarios - no animation
  const closeImmediately = () => {
    // Instantly move modal off-screen without animation
    slideAnim.setValue(-modalWidth);
    setIsModalOpen(false);
    setIsClosing(false); // Reset closing state
    // Delay the actual modal close to allow navigation to complete
    setTimeout(() => closeModal(), 50);
  };



const handleNavigate = () => {
  setKey(Math.random().toString());
  navigation.push('CourseCreate', {key});
  closeImmediately();
};

  // Handle modal visibility changes from parent - simplified logic
  useEffect(() => {
    // Prevent any interference during iOS swipe closing
    if (isSwipeClosing) {
      return;
    }

    if (modalVisible && !isModalOpen && !isClosing) {
      animateIn();
    } else if (!modalVisible && isModalOpen && !isClosing) {
      animateOut();
    }
    // Add extra protection for iOS scenarios
    if (Platform.OS === 'ios' && (isClosing || isSwipeClosing)) {
      return; // Don't do anything if we're in closing state on iOS
    }
  }, [modalVisible]);

  // Create PanResponder for swipe gesture detection with smooth animations
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: (_evt, gestureState) => {
        // Only respond to horizontal swipes with sufficient movement
        const { dx, dy } = gestureState;
        return Math.abs(dx) > Math.abs(dy) && Math.abs(dx) > 10;
      },
      onPanResponderMove: (_evt, gestureState) => {
        // Only provide real-time feedback if not currently closing
        if (!isClosing && !isSwipeClosing) {
          // Provide real-time visual feedback during right-to-left swipe
          const { dx } = gestureState;
          if (dx < 0) { // Right-to-left swipe
            // Apply partial translation for immediate visual feedback
            // Limit the translation to prevent over-swiping
            const clampedDx = Math.max(dx, -modalWidth * 0.3); // Max 30% of modal width
            slideAnim.setValue(clampedDx);
          }
        }
      },
      onPanResponderRelease: (_evt, gestureState) => {
        const { dx, vx } = gestureState;

        // Define thresholds for swipe detection
        const SWIPE_THRESHOLD = 50; // Minimum distance
        const VELOCITY_THRESHOLD = 0.3; // Minimum velocity

        // Check if it's a right-to-left swipe with sufficient distance or velocity
        if (dx < -SWIPE_THRESHOLD || (dx < 0 && vx < -VELOCITY_THRESHOLD)) {

          if (Platform.OS === 'ios') {
            // iOS-specific approach: Completely isolated swipe handling

            // Set special swipe closing state to prevent ALL interference
            setIsSwipeClosing(true);
            setIsClosing(true);
            setIsModalOpen(false);

            // Create a completely new animation instance to avoid conflicts
            const swipeCloseAnim = new Animated.Value(slideAnim._value);

            // Animate the new instance
            Animated.timing(swipeCloseAnim, {
              toValue: -modalWidth,
              duration: 300,
              useNativeDriver: false, // Use layout animation for iOS to avoid conflicts
            }).start(() => {
              // Apply the final position to the main slideAnim
              slideAnim.setValue(-modalWidth);

              // Extended delay to ensure no flash-back
              setTimeout(() => {
                setIsSwipeClosing(false);
                setIsClosing(false);
                closeModal();
              }, 200); // Even longer delay
            });

            // Apply the animation to the main slideAnim in real-time
            const listener = swipeCloseAnim.addListener(({ value }) => {
              slideAnim.setValue(value);
            });

            // Clean up listener after animation
            setTimeout(() => {
              swipeCloseAnim.removeListener(listener);
            }, 350);

          } else {
            // Android: Use the simpler approach
            setIsClosing(true);
            Animated.timing(slideAnim, {
              toValue: -modalWidth,
              duration: 300,
              useNativeDriver: true,
            }).start(() => {
              setIsModalOpen(false);
              setIsClosing(false);
              closeModal();
            });
          }
        } else {
          // If swipe doesn't meet threshold, animate back to original position
          Animated.timing(slideAnim, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
          }).start();
        }
      },
      onPanResponderTerminate: () => {
        // If gesture is terminated and not closing, animate back to original position
        if (!isClosing) {
          Animated.timing(slideAnim, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
          }).start();
        }
      },
    })
  ).current;

  // Only render modal when it should be visible or animating
  if (!modalVisible && !isModalOpen && !isClosing && !isSwipeClosing) {
    return null;
  }

  return (
    <Modal
      transparent={true}
      visible={true}
      onRequestClose={animateOut}>
      <View style={styles.modalOverlay} {...panResponder.panHandlers}>
        <TouchableWithoutFeedback onPress={animateOut}>
          <View style={styles.overlayTouchArea}>
            <SafeAreaView style={styles.safeAreaContainer} edges={['top', 'bottom', 'left']}>
              <TouchableWithoutFeedback onPress={() => {}}>
                <Animated.View
                  style={[
                    styles.modalContainer,
                    {
                      transform: [{translateX: slideAnim}],
                      width: modalWidth,
                      paddingTop: Platform.OS === 'ios' ? insets.top : 0,
                    },
                  ]}>
                <View style={styles.modalContent}>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <TouchableOpacity onPress={() => navigation.navigate('Home')}>
              <Image
                source={require('../../assets/MainKhelCoach.png')}
                style={{ width: 150, height: 52 }}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.option}
              onPress={() => animateOut()}>
              <Icon name="close" size={24} color="black" />
            </TouchableOpacity>
          </View>

          {isAuthenticated ? (
            <>
              {/* Options */}
              <TouchableOpacity
                style={[styles.option, {marginTop: '10%'}]}
                onPress={() => {setPageName('Dashboard');
                  navigation.navigate('Dashboard'), closeImmediately();
                }}>
                <Icon name="home" size={20} color="black" />
                <Text style={styles.optionText}>Dashboard</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.option}
                onPress={() => {
                  setPageName('Calendar');
                  navigation.navigate('Calendar1'), closeImmediately();
                }}>
                <Icon name="calendar" size={20} color="black" />
                <Text style={styles.optionText}>Calendar</Text>
              </TouchableOpacity>

              <TouchableOpacity onPress={() => setShowCourse(!showCourse)}>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}>
                  <View style={styles.option}>
                    <Icon name="book" size={20} color="black" />
                    <Text style={styles.optionText}>Course </Text>
                  </View>

                  <View style={styles.option}>
                    <Icon
                      name={showCourse ? 'chevron-down' : 'chevron-right'}
                      size={20}
                      color="black"
                    />
                  </View>
                </View>
              </TouchableOpacity>

              {/* Submenu */}

              {showCourse ? (
                <View style={styles.submenu}>
                  <TouchableOpacity
                    style={styles.suboption}
                    onPress={() => {
                      setPageName("Create Course")
                                handleNavigate();

                    }}>
                    <View style={styles.subIcon}>
                      <Icon name="pencil" size={20} color="black" />
                    </View>
                    <Text style={styles.suboptionText}>Create Course</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.suboption}
                    onPress={() => {
                      setPageName('Course List');
                      navigation.navigate('CourseListing'), closeImmediately();
                    }}>
                    <View style={styles.subIcon}>
                      <Icon name="list" size={20} color="black" />
                    </View>
                    <Text style={styles.suboptionText}>Course Listing </Text>
                  </TouchableOpacity>
                </View>
              ) : (
                <></>
              )}

              <TouchableOpacity
                style={styles.option}
                onPress={() => {
                  setPageName('Bookings');
                  navigation.navigate('Booking'), closeImmediately();
                }}>
                <Icon name="copy" size={20} color="black" />
                <Text style={styles.optionText}>Booking Details</Text>
              </TouchableOpacity>

              <TouchableOpacity onPress={() => setShowProfile(!showProfile)}>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                  }}>
                  <View
                    style={[styles.option, {marginLeft: 5}]}
                    onPress={() => setShowProfile(!showProfile)}>
                    <Icon name="user" size={20} color="black" />
                    <Text style={styles.optionText}>Profile</Text>
                  </View>

                  <View
                    style={styles.option}
                    onPress={() => setShowProfile(!showProfile)}>
                    <Icon
                      name={showProfile ? 'chevron-down' : 'chevron-right'}
                      size={20}
                      color="black"
                    />
                  </View>
                </View>
              </TouchableOpacity>

              {showProfile ? (
                <View style={styles.submenu}>
                  <TouchableOpacity
                    style={styles.suboption}
                    onPress={() => {
                      setPageName('Basic Details');
                      navigation.navigate('Registration'), closeImmediately();
                    }}>
                    <View style={styles.subIcon}>
                      <Icon name="info" size={20} color="black" />
                    </View>
                    <Text style={styles.suboptionText}>Basic Details</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.suboption}
                    onPress={() => {
                      setPageName('Professional Details');
                      navigation.navigate('ProfessionalDetails'), closeImmediately();
                    }}>
                    <View style={styles.subIcon}>
                      <Icon name="briefcase" size={20} color="black" />
                    </View>
                    <Text style={styles.suboptionText}>
                      Professional Details{' '}
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.suboption}
                    onPress={() => {
                      setPageName('KYC Details');
                      navigation.navigate('KYCDetails'), closeImmediately();
                    }}>
                    <View style={styles.subIcon}>
                      <Icon name="id-card" size={20} color="black" />
                    </View>
                    <Text style={styles.suboptionText}>KYC Details</Text>
                  </TouchableOpacity>

                </View>
              ) : (
                <></>
              )}
              <TouchableOpacity
                style={[styles.option]}
                onPress={() => {setPageName('Contact Us');
                  navigation.navigate('Contact'), closeImmediately();
                }}>
                <Icon name="pencil" size={20} color="black" />
                <Text style={styles.optionText}>Contact Us</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.option]}
                onPress={() => {setPageName('Reports');
                  navigation.navigate('Reports'), closeImmediately();
                }}>
                <Icon name="file" size={20} color="black" />
                <Text style={styles.optionText}>Reports</Text>
              </TouchableOpacity>
              {/* <TouchableOpacity style={styles.option} onPress={() => {navigation.navigate('Calendar'),closeModal()}}>
            <Icon name="file-text-o" size={20} color="black" />
              <Text style={styles.optionText}>Reports</Text>
            </TouchableOpacity> */}
            </>
          ) : (
            <></>
          )}
          </View>
          </Animated.View>
            </TouchableWithoutFeedback>
        </SafeAreaView>
          </View>
        </TouchableWithoutFeedback>
      </View>
    </Modal>
  );
}

export default HeaderModal

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  overlayTouchArea: {
    flex: 1,
  },
  safeAreaContainer: {
    flex: 1,
  },
  modalContainer: {
    height: '100%',
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalContent: {
    flex: 1,
    padding: 20,
    paddingBottom: Platform.OS === 'ios' ? 34 : 20, // Extra padding for iOS home indicator
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
  },
  optionText: {
    marginLeft: 10,
    fontSize: 18,
    color: 'black',
    fontWeight: '600',
    fontFamily: 'Lato-Black'
    // fontFamily:'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e'

  },
  submenu: {
    marginLeft: 20,
  },
  suboption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 5,

  },
  suboptionText: {
    marginLeft: 10,
    fontSize: 16,
    color: 'black',
    fontWeight: '500',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e'
  },
  subIcon:{
    width: "10%", display:"flex", justifyContent:"center", alignItems:"center"
  }
});