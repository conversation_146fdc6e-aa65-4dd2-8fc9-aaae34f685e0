import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
  TouchableWithoutFeedback,
  Alert,
  Linking,
  Dimensions
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import axios from 'axios';
import {useNavigation, useNavigationState} from '@react-navigation/native';
import HeaderModal from './HeaderModal';
import {useAuth} from '../Auth/AuthContext';
import {API} from '@env';

const placeholder = require('../../assets/placeholder.png');
const Header = () => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [menuVisible, setMenuVisible] = useState(false);
  const navigation = useNavigation();

  // Get screen dimensions for full-screen overlay
  const { height: screenHeight } = Dimensions.get('window');
  // Use navigation state to track current screen
  const navigationState = useNavigationState(state => state);
  const {isAuthenticated, logout, user, isHeader, pageName, setPageName} =
    useAuth();
  // console.log("user in header", user)

  const [profileImage, setProfileImage] = useState('');

  // Update header title when navigation state changes (including back gesture)
  useEffect(() => {
    if (navigationState && navigationState.routes && navigationState.index >= 0) {
      const currentRoute = navigationState.routes[navigationState.index];

      // Map route names to header titles
      const routeTitles = {
        'Dashboard': 'Dashboard',
        'Registration': 'Basic Detail',
        'OneStepForm': 'Basic Detail',
        'ProfessionalDetails': 'Professional Details',
        'KYCDetails': 'KYC Details',
        'Calendar': 'Calendar',
        'Calendar1': 'Calendar',
        'CourseCreate': 'Create Course',
        'CourseListing': 'Course List',
        'Booking': 'Bookings',
        'BookingDetails': 'Booking Details',
        'Contact': 'Contact',
        'Reports': 'Reports',
        'Invoice': 'Invoice',
        'ListingDetails': 'Course Details'
      };

      // Set the page name based on the current route
      if (currentRoute && routeTitles[currentRoute.name]) {
        setPageName(routeTitles[currentRoute.name]);
      }
    }
  }, [navigationState, setPageName]);

  useEffect(() => {
    setProfileImage(user?.data?.profileImg);
  }, [user]);

  const openModal = () => {
    setModalVisible(true);
  };

  const closeModal = () => {
    setModalVisible(false);
  };

  const toggleDropdown = () => {
    setShowDropdown(!showDropdown);
  };

  const handleLogout = async () => {
    try {
      logout();
    } catch (error) {
      console.log('error', error);
    }
  };

  const handleDeleteAccount = async () => {
    Alert.alert('Hold on!', 'Are you sure you want to delete your account?', [
      {
        text: 'Cancel',
        onPress: () => null,
        style: 'cancel',
      },
      {
        text: 'YES',
        onPress: async () => {
          deleteAccount();
        },
      },
    ]);
  };

  const deleteAccount = async () => {
    try {
      // Redirect to the specified URL without making the API call
      await Linking.openURL("https://coach.khelcoach.com/");

      // Optional: Handle additional cleanup here if needed
      Alert.alert('Redirecting to the account deletion page...');
      logout();
    } catch (error) {
      Alert.alert('Unable to redirect at the moment.');
      console.log('Error redirecting to web URL:', error);
    }
  };

  return (
    <SafeAreaView>
      <View style={styles.container}>
        {/* Left Component */}
        <View style={styles.leftContainer}>
          <TouchableOpacity onPress={openModal} style={styles.menuButton}>
            <Text style={styles.menuText}>{menuVisible ? '☰' : '☰'}</Text>
          </TouchableOpacity>

          <HeaderModal closeModal={closeModal} modalVisible={modalVisible} />
          {/* </Animated.View> */}
          <Text style={styles.separator}>|</Text>
          {/* Text */}
          <Text style={styles.text}>
            {isAuthenticated ? pageName : 'Coach Registration Form'}
          </Text>
        </View>

        {/* Right Component */}

        {isHeader ? (
          <>
            <View style={styles.rightContainer}>
              {/* <TouchableOpacity style={styles.rightItem}>

              <Icon name="notifications-outline" size={22} color="black" />

            </TouchableOpacity> */}
              <TouchableOpacity
                style={styles.rightItem}
                onPress={toggleDropdown}>
                {/* Image */}
                <Image
                  source={{
                    uri: profileImage
                      ? profileImage
                      : 'data:image/jpeg;base64,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',
                  }}
                  style={styles.image}
                  alt={user?.data?.firstName ? `${user?.data?.firstName} ${user?.data?.lastName}` : 'Profile'}
                />
              </TouchableOpacity>
            </View>
            {showDropdown && (
              <>
                <TouchableWithoutFeedback onPress={() => setShowDropdown(false)}>
                  <View style={[styles.dropdownOverlay, { height: screenHeight }]} />
                </TouchableWithoutFeedback>
                <TouchableWithoutFeedback onPress={() => {}}>
                  <View style={styles.dropdown}>
                  <View
                    style={{
                      paddingHorizontal: 16,
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                    <View
                      style={{
                        paddingBottom: 10,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}>
                      <View style={{height: 120, width: 120, borderRadius: 60, overflow: 'hidden', borderWidth: 2, borderColor: '#1F2973', marginBottom: 10}}>
                        <Image
                          source={{
                            uri: profileImage
                              ? profileImage
                              : 'data:image/jpeg;base64,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',
                          }}
                          style={{
                            height: '100%',
                            width: '100%',
                            borderRadius: 60,
                          }}
                          alt={user?.data?.firstName ? `${user?.data?.firstName} ${user?.data?.lastName}` : 'Profile'}
                        />
                      </View>

                      <Text
                        style={{
                          fontSize: 22,
                          color: '#1F2973',
                          fontFamily:
                            'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                          fontWeight: 'bold',
                          marginBottom: 5,
                        }}>{user?.data?.firstName ? `${user?.data?.firstName?.trim()} ${user?.data?.lastName?.trim()}` : 'Guest User'}</Text>
                      <Text
                        style={{
                          fontSize: 16,
                          color: '#4B5563',
                          fontFamily:
                            'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                          marginBottom: 10,
                        }}>{user?.data?.email ? `${user?.data?.email}` : 'Not logged in'}</Text>
                    </View>

                    <View
                      style={{
                        paddingBottom: 10,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}>
                      <TouchableOpacity
                        onPress={() => {
                          // Check if already on Basic Detail page
                          if (pageName === 'Basic Detail') {
                            // Just close the dropdown if already on Basic Detail page
                            setShowDropdown(false);
                          } else {
                            // Navigate to Registration and set page name
                            navigation.navigate('Registration');
                            setPageName('Basic Detail');
                            setShowDropdown(false);
                          }
                        }}
                        style={{
                          paddingVertical: 10,
                          paddingHorizontal: 20,
                          borderWidth: 1,
                          borderRadius: 50,
                          borderColor: '#1F2973',
                          backgroundColor: '#1F2973',
                          elevation: 3,
                        }}>
                        <Text
                          style={{
                            fontSize: 14,
                            color: 'white',
                            fontWeight: '600',
                            fontFamily:
                              'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                          }}>
                          Manage Your Account
                        </Text>
                      </TouchableOpacity>
                    </View>

                    <View
                      style={{
                        paddingBottom: 10,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}>
                      <Text
                        style={{
                          fontSize: 16,
                          color: '#1F2973',
                          fontWeight: 'bold',
                          fontFamily:
                            'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                          marginBottom: 5,
                        }}>
                        Linked Google Account
                      </Text>
                      <Text
                        style={{
                          fontSize: 14,
                          color: '#6B7280',
                          fontFamily:
                            'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                          backgroundColor: '#f3f4f6',
                          paddingVertical: 5,
                          paddingHorizontal: 10,
                          borderRadius: 4,
                        }}>{`${
                        user?.data?.googleEmail
                          ? user?.data?.googleEmail
                          : 'No account linked yet '
                      }`}</Text>
                    </View>
                  </View>
                  <View
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'space-around',
                      marginTop: 10,
                      borderTopWidth: 1,
                      borderColor: '#e5e7eb',
                      paddingTop: 10,
                    }}>
                    <TouchableOpacity
                      style={[
                        styles.dropdownButton,
                        {
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                          width: '48%',
                          backgroundColor: '#f3f4f6',
                          borderRadius: 8,
                          paddingVertical: 10,
                          marginHorizontal: 4,
                          elevation: 2,
                        },
                      ]}
                      onPress={handleLogout}>
                      <Text
                        style={{
                          color: '#1F2973',
                          fontWeight: 'bold',
                          fontSize: 14,
                          fontFamily:
                            'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                        }}>
                        Logout
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[
                        styles.dropdownButton,
                        {
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                          width: '48%',
                          backgroundColor: '#fee2e2',
                          borderRadius: 8,
                          paddingVertical: 10,
                          marginHorizontal: 4,
                          elevation: 2,
                        },
                      ]}
                      onPress={handleDeleteAccount}>
                      <Text
                        style={{
                          color: '#dc2626',
                          fontWeight: 'bold',
                          fontSize: 14,
                          fontFamily:
                            'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
                        }}>
                        Delete Account
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
                  </TouchableWithoutFeedback>
                </>
            )}
          </>
        ) : (
          <></>
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 5,
    borderWidth: 0,
    elevation: 1,
    backgroundColor: 'white',
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  leftItem: {
    marginRight: 10,
  },
  separator: {
    paddingHorizontal: 10,
    fontSize: 25,
    fontWeight: '500',
    color: 'black',
  },
  text: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'black',
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
  rightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rightItem: {
    marginLeft: 10,
  },
  image: {
    width: 30,
    height: 30,
    borderRadius: 50,
    resizeMode: 'cover',
    borderWidth: 1,
  },
  dropdownOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    zIndex: 1,
  },
  dropdown: {
    position: 'absolute',
    top: 50,
    right: 0,
    backgroundColor: '#fff',
    borderRadius: 8,
    paddingTop: 20,
    paddingBottom: 10,
    elevation: 8,
    borderWidth: 0,
    width: 300,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    zIndex: 2,
  },
  dropdownButton: {
    paddingVertical: 5,
    borderColor: '#e5e7eb',
  },
  menuButton: {},
  menuText: {
    fontSize: 24,
    color: '#000',
  },
});

export default Header;