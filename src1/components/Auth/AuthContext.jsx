import React, { createContext, useContext, useState, useEffect } from "react";
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  Alert,
} from 'react-native';
import { API } from '@env';
import axios from 'axios';
import { useNavigation } from '@react-navigation/native';
import { CommonActions } from '@react-navigation/native';
import { GoogleSignin } from "@react-native-google-signin/google-signin";


const AuthContext = createContext()

export const AuthProvider = ({ children }) => {

  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isHeader, setIsHeader] = useState(false);
  const [user, setUser] = useState([]);
  const [isLoggedin, setIsLoggedIn] = useState(false)
  const [calendarLinked, setCalendarLinked] = useState(false);
  const [pageName, setPageName] = useState('')
  const [email, setEmail] = useState('')

  const navigation = useNavigation();


  const getUserDetails = async () => {
    console.log("hitting user details")
    try {
      const tokenValue = await AsyncStorage.getItem('coachToken');
      const id = await AsyncStorage.getItem('coachId');


      if (tokenValue && id) {
        const response = await axios.get(`${API}/api/coach/${id}`, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${tokenValue}`,
          },
        });
        //console.log("dataaa in auth ",response?.data)
        setUser({ data: { ...response?.data, token: tokenValue, id: id } })
        //console.log("dataaa",response?.data)
        return { data: { ...response?.data, token: tokenValue, id: id } }
      } else {
        return { error: "User not authorized" }
      }
    } catch (e) {
      console.log('Error retrieving data 46:', e);
      return { tokenValue: null, id: null };
    }
  };

  const login = async (email, password) => {
    setEmail(email);
    try {

      if (!email) {
        Alert.alert('Error', 'Please enter your Email Address.');
        return;
      }
      if (!password) {
        Alert.alert('Error', 'Please enter your Password.');
        return;
      }
      const userDetails = {
        email,
        password,
      };

      const response = await axios.post(`${API}/api/coach/login`, userDetails, {
        headers: { "Content-Type": "application/json" },
      });
      const token = response?.data?.token;


      const storedToken = await AsyncStorage.setItem('loginToken', token);
      const userId = response?.data?.id;
      const storeId = await AsyncStorage.setItem('userId', userId);
      //setCoachId(userId)

      // const data = await response.json();
      if (response.data.status == "ok") {
        AsyncStorage.setItem('coachEmail', email);
        AsyncStorage.setItem('coachToken', response.data.token);
        AsyncStorage.setItem('coachId', response.data.id);
        AsyncStorage.setItem('coachLoggedIn', 'true');
        // If login is successful
        Alert.alert('Success', 'You have signed in successfully.');

        setIsLoggedIn(true);


        if (token && userId) {
          setIsAuthenticated(true)
          setIsHeader(true)
          const detailsUser = await getUserDetails()

          if (token && userId && detailsUser?.data?.authStatus === 'authorized' && detailsUser?.data?.status === "active") {
            //console.log("111")
            setIsAuthenticated(true)
            setPageName('Dashboard');
            navigation.navigate('Dashboard')
          }
          else if (token && userId && detailsUser?.data?.authStatus === 'authorized' && detailsUser?.data?.status === "inactive") {
            //console.log("2222")
            setIsAuthenticated(false)
            setPageName('KYC Details');
            navigation.navigate('KYCDetails', { showMessage: true });
          }
          else if (token && userId && detailsUser?.data?.authStatus === 'unauthorized' && detailsUser?.data?.status === "inactive") {
            setIsAuthenticated(false)
            //console.log("3333")
            setPageName('Professional Details');
            navigation.navigate('ProfessionalDetails', { showMessage: true })
          }

        }


      } else {
        Alert.alert('Error', response.data.message || 'An error occurred. Please try again.');
      }
    }
    catch (error) {
      console.log("Caught Error Inside Catch Block:", error);
      if (error.response) {
        
        const errorMessage = error.response.data?.message || error.response.data?.error || 'An error occurred during login. Please try again.';
        Alert.alert('Error', errorMessage);

      } else if (error.request) {
        console.error('Login error - Request:', error.request);
        Alert.alert('Error', 'Network error. Please check your connection and try again.');

      } else {
        console.error('Login error - Message:', error.message);
        Alert.alert('Error', 'An unexpected error occurred. Please try again.');
      }
      console.error('Full error object:', error);
    }

  }

  const logout = async () => {
    try {
      // Clear all storage
      await AsyncStorage.clear();
      await GoogleSignin.signOut();

      // Reset all auth states
      setIsHeader(false);
      setIsAuthenticated(false);
      setUser([]);
      setIsLoggedIn(false);
      setPageName('Login');

      // Navigate to Login screen with reset to prevent going back
      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{ name: 'Login' }],
        }),
      );
    }
    catch (error) {
      console.log("error", error);
      // Even if there's an error, try to navigate to Login
      setIsHeader(false);
      navigation.navigate('Login');
    }
  }

  const updateUserDetails = async () => {
    getUserDetails()

  }



  return (
    <AuthContext.Provider value={{ pageName, setPageName, isAuthenticated, isHeader, setIsHeader, user, login, logout, updateUserDetails, getUserDetails, isLoggedin, setIsAuthenticated, setEmail, email, calendarLinked, setCalendarLinked }}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => useContext(AuthContext)