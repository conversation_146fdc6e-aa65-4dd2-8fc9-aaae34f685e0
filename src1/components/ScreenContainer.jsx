import React from 'react';
import { View, StyleSheet, Platform, StatusBar } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

/**
 * A consistent container for all screens that handles safe areas and spacing
 * properly across iOS and Android devices.
 */
const ScreenContainer = ({ children, style }) => {
  return (
    <SafeAreaView 
      edges={['bottom', 'left', 'right']} 
      style={[styles.safeArea, style]}
    >
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <View style={styles.container}>
        {children}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F5F8FC',
  },
  container: {
    flex: 1,
    // This padding ensures there's no black strip between header and content
    paddingTop: Platform.OS === 'ios' ? 0 : 0,
  },
});

export default ScreenContainer;
