import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome';

/**
 * CustomMultiSelect - A custom multi-select component that shows preselected items
 *
 * @param {Object} props
 * @param {Array} props.data - Array of objects with label and value properties
 * @param {Array} props.selectedItems - Array of preselected items
 * @param {Function} props.setSelected - Function to update selected items
 * @param {String} props.label - Label for the component
 */
const CustomMultiSelect = ({ data, selectedItems, setSelected, label }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selected, setSelectedLocal] = useState([]);

  // Initialize selected items from props
  useEffect(() => {
    if (selectedItems && selectedItems.length > 0) {
      setSelectedLocal(selectedItems);
    }
  }, [selectedItems]);

  // Toggle dropdown
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  // Handle item selection
  const handleSelect = (item) => {
    let updatedSelection = [...selected];

    // Check if item is already selected
    const itemIndex = updatedSelection.findIndex(
      (selectedItem) => selectedItem.value === item.value
    );

    if (itemIndex > -1) {
      // Remove item if already selected
      updatedSelection.splice(itemIndex, 1);
    } else {
      // Add item if not selected
      updatedSelection.push(item);
    }

    setSelectedLocal(updatedSelection);
    setSelected(updatedSelection); // Update parent component state
  };

  // Check if an item is selected
  const isSelected = (item) => {
    return selected.some((selectedItem) => selectedItem.value === item.value);
  };

  // Render selected items as chips
  const renderSelectedItems = () => {
    if (selected.length === 0) {
      return <Text style={styles.placeholderText}>Select items</Text>;
    }

    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.selectedItemsContainer}
      >
        {selected.map((item, index) => (
          <View key={index} style={styles.chip}>
            <Text style={styles.chipText}>{item.label}</Text>
            <TouchableOpacity
              onPress={() => handleSelect(item)}
              style={styles.chipRemove}
            >
              <Icon name="close" size={14} color="#fff" />
            </TouchableOpacity>
          </View>
        ))}
      </ScrollView>
    );
  };

  return (
    <View style={styles.container}>
      {label && <Text style={styles.label}>{label}</Text>}

      {/* Dropdown header */}
      <TouchableOpacity
        style={[
          styles.dropdownHeader,
          isOpen && styles.dropdownHeaderOpen
        ]}
        onPress={toggleDropdown}
      >
        {renderSelectedItems()}
        <Icon
          name={isOpen ? "chevron-up" : "chevron-down"}
          size={16}
          color="#000"
        />
      </TouchableOpacity>

      {/* Dropdown content */}
      {isOpen && (
        <View style={styles.dropdownContent}>
          <ScrollView
            style={styles.listContainer}
            showsVerticalScrollIndicator={true}
            nestedScrollEnabled={true}
          >
            {data.map((item, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.item,
                  isSelected(item) && styles.selectedItem
                ]}
                onPress={() => handleSelect(item)}
              >
                <Text style={[
                  styles.itemText,
                  isSelected(item) && styles.selectedItemText
                ]}>
                  {item.label}
                </Text>
                {isSelected(item) && (
                  <Icon name="check" size={16} color="lightskyblue" />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
          <TouchableOpacity
            style={styles.doneButton}
            onPress={() => setIsOpen(false)}
          >
            <Text style={styles.doneButtonText}>Done</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
    position: 'relative',
    zIndex: 1,
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
    color: '#000',
  },
  dropdownHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 6,
    backgroundColor: '#fff',
  },
  dropdownHeaderOpen: {
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  placeholderText: {
    color: '#6b7280',
  },
  selectedItemsContainer: {
    flex: 1,
    flexDirection: 'row',
  },
  chip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'lightskyblue',
    borderRadius: 16,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 8,
  },
  chipText: {
    color: '#fff',
    fontSize: 12,
    marginRight: 4,
  },
  chipRemove: {
    width: 16,
    height: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  dropdownContent: {
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 6,
    backgroundColor: '#fff',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    marginTop: -1,
    borderTopWidth: 0,
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
  },
  listContainer: {
    height: 200,
    width: '100%',
  },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  selectedItem: {
    backgroundColor: '#f3f4f6',
  },
  itemText: {
    color: '#000',
  },
  selectedItemText: {
    fontWeight: '500',
  },
  doneButton: {
    backgroundColor: 'lightskyblue',
    padding: 10,
    alignItems: 'center',
    borderBottomLeftRadius: 6,
    borderBottomRightRadius: 6,
  },
  doneButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
});

export default CustomMultiSelect;