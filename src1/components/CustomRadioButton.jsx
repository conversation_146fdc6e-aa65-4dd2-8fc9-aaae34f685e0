import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';

const CustomRadioButton = ({ value, status, onPress, color = '#000', label, disabled, radioSize = 16, labelFontSize = 13, labelStyle }) => {
  const handlePress = () => {
    if (!disabled) {
      onPress(value);
    }
  };

  return (
    <TouchableOpacity
      style={styles.radioButtonContainer}
      onPress={handlePress}
      disabled={disabled}
    >
      <View style={[
        styles.radioButton,
        { borderColor: disabled ? '#ccc' : color, height: radioSize, width: radioSize, borderRadius: radioSize / 2 }
      ]}>
        {status && <View style={[
          styles.radioButtonInner,
          { backgroundColor: color, height: radioSize / 2, width: radioSize / 2, borderRadius: radioSize / 4 }
        ]} />}
      </View>
      <Text style={[
        styles.radioButtonLabel,
        { fontSize: labelFontSize },
        labelStyle,
        disabled && styles.disabledLabel
      ]}>{label}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  radioButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  radioButton: {
    height: 20,
    width: 20,
    borderRadius: 10,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 5,
  },
  radioButtonInner: {
    height: 10,
    width: 10,
    borderRadius: 5,
  },
  radioButtonLabel: {
    fontSize: 16,
  },
  disabledLabel: {
    color: '#ccc', // Change label color if disabled
  },
});

export default CustomRadioButton;
