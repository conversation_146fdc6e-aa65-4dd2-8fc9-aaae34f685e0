import React, {useEffect, useState} from 'react';
import {ActivityIndicator, ScrollView, StyleSheet, StatusBar, Platform} from 'react-native';
import {NavigationContainer} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {createDrawerNavigator} from '@react-navigation/drawer';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import 'react-native-gesture-handler';

import 'react-native-screens';
import OrientationLock from './src1/components/OrientationLock';

import Registration from './src1/Screens/Coach/Registration';
import ProfessionalDetails from './src1/Screens/Coach/ProfessionalDetails';
import KYCdetails from './src1/Screens/Coach/KYCdetails';
import Login from './src1/Screens/Coach/Login';
import Home from './src1/components/Home';
import {AuthProvider} from './src1/components/Auth/AuthContext';
import create from './src1/Screens/Course/Create';
import Create from './src1/Screens/Course/Create';
import Listing from './src1/Screens/Course/Listing';
import Header from './src1/components/Header/Header';
import Calendar from './src1/Screens/Calendar/Calendar';
import {getLoginToken} from './src1/helpers';

import AsyncStorage from '@react-native-async-storage/async-storage';
import CoachBooking from './src1/Screens/Booking/CoachBooking';
import BookingDetails from './src1/Screens/Booking/BookingDetails';
import ListingDetails from './src1/Screens/Course/ListingDetails';
import Dashboard from './src1/Screens/Dashboard/Dashboard';
import CalendarModule1 from './src1/Screens/Calendar/CalendarModule1';
import Create1 from './src1/Screens/Course/Create1';
import CustomCalendar from './src1/Screens/Calendar/CustomCalendar';
import Listing1 from './src1/Screens/Course/Listing1';
import ForgetPassword from './src1/Screens/Coach/ForgetPassword';
import Reports from './src1/Screens/Reports/Reports';
import Contact from './src1/Screens/Contact/Contact';
import Invoice from './src1/Screens/Invoice';
import OneStepForm from './src1/Screens/Coach/OneStepForm';
const Stack = createNativeStackNavigator();

//const Drawer = createDrawerNavigator();

function App(): React.JSX.Element {
  const [token, setToken] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      const token = await AsyncStorage.getItem('loginToken');
      if (token) {
        setToken(token);
      }
      setLoading(false);
    };
    checkAuth();
  }, []);

  if (loading) {
    <ActivityIndicator size={'large'} />;
  }

  //  screenOptions={{
  //   header: () => <Header />, // Set the default header component
  // }}
  // initialRouteName = {token ? 'Home' : 'Login'}

  return (
    <SafeAreaProvider>
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <OrientationLock />
      <NavigationContainer>
        <AuthProvider>
        <Stack.Navigator
  initialRouteName="Home"
  screenOptions={({ route }) => ({
    headerShown: route.name !== 'Login' && route.name !== 'ForgetPassword' && route.name !== 'OneStepForm',
    gestureEnabled: false, // Disable swipe-to-go-back gesture on iOS
    fullScreenGestureEnabled: false, // Disable full screen gesture on iOS
    gestureDirection: 'horizontal', // Set gesture direction
    animationTypeForReplace: 'push', // Animation type for replace
    // iOS specific options to completely disable swipe gestures
    ...(Platform.OS === 'ios' && {
      gestureEnabled: false,
      fullScreenGestureEnabled: false,
      gestureResponseDistance: 0, // Disable gesture response distance
    }),
    header: () => {
      // Only show header for screens that should have it
      if (route.name !== 'Login' && route.name !== 'ForgetPassword' && route.name !== 'OneStepForm') {
        return <Header />;
      }
      return null;
    },
  })}
>
  <Stack.Screen
    name="Home"
    component={Home}
    options={{ gestureEnabled: false }}
  />
  <Stack.Screen
    name="Registration"
    component={Registration}
    options={{ gestureEnabled: false }}
  />
  <Stack.Screen
    name="OneStepForm"
    component={OneStepForm}
    options={{ gestureEnabled: false }}
  />
  <Stack.Screen
    name="ProfessionalDetails"
    component={ProfessionalDetails}
    options={{ gestureEnabled: false }}
  />
  <Stack.Screen
    name="KYCDetails"
    component={KYCdetails}
    options={{ gestureEnabled: false }}
  />
  <Stack.Screen
    name="Login"
    component={Login}
    options={{ gestureEnabled: false }}
  />
  <Stack.Screen
    name="ForgetPassword"
    component={ForgetPassword}
    options={{ gestureEnabled: false }}
  />
  <Stack.Screen
    name="CourseCreate"
    component={Create}
    options={{ gestureEnabled: false }}
  />
  {/* <Stack.Screen name="CourseCreate" component={Create1} /> */}
  {/* <Stack.Screen name="CourseListing" component={Listing} /> */}
  <Stack.Screen
    name="CourseListing"
    component={Listing1}
    options={{ gestureEnabled: false }}
  />
  <Stack.Screen
    name="ListingDetails"
    component={ListingDetails}
    options={{ gestureEnabled: false }}
  />
  <Stack.Screen
    name="Calendar"
    component={Calendar}
    options={{ gestureEnabled: false }}
  />
  <Stack.Screen
    name="Calendar1"
    component={CalendarModule1}
    options={{ gestureEnabled: false }}
  />
  {/* <Stack.Screen name="CustomCalendar" component={CustomCalendar} /> */}
  <Stack.Screen
    name="Booking"
    component={CoachBooking}
    options={{ gestureEnabled: false }}
  />
  <Stack.Screen
    name="BookingDetails"
    component={BookingDetails}
    options={{ gestureEnabled: false }}
  />
  <Stack.Screen
    name="Dashboard"
    component={Dashboard}
    options={{ gestureEnabled: false }}
  />
  <Stack.Screen
    name="Reports"
    component={Reports}
    options={{ gestureEnabled: false }}
  />
  <Stack.Screen
    name="Invoice"
    component={Invoice}
    options={{ gestureEnabled: false }}
  />
  <Stack.Screen
    name="Contact"
    component={Contact}
    options={{ gestureEnabled: false }}
  />
</Stack.Navigator>

        </AuthProvider>
      </NavigationContainer>
    </SafeAreaProvider>
  );
}

const globalStyles = StyleSheet.create({
  text: {
    fontFamily: 'Lato-Italic__Lato_b8262e, __Lato_Fallback_b8262e',
  },
});

export default App;
