{"name": "khelsportsCoachapp", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.23.1", "@react-native-community/checkbox": "^0.5.20", "@react-native-community/datetimepicker": "^7.6.3", "@react-native-community/geolocation": "^3.2.1", "@react-native-google-signin/google-signin": "^11.0.1", "@react-native-masked-view/masked-view": "^0.3.1", "@react-native-picker/picker": "^2.7.2", "@react-navigation/drawer": "^6.6.15", "@react-navigation/native": "^6.1.17", "@react-navigation/native-stack": "^6.9.26", "axios": "^1.6.8", "country-state-city": "^3.2.1", "formik": "^2.4.5", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "native-base": "^3.4.28", "quill-delta-to-html": "^0.12.1", "react": "18.2.0", "react-native": "0.73.6", "react-native-bouncy-checkbox": "^3.0.7", "react-native-buffer": "^6.0.3", "react-native-calendars": "^1.1304.1", "react-native-cn-quill": "^0.7.18", "react-native-dotenv": "^3.4.11", "react-native-dropdown-select-list": "^2.0.5", "react-native-fs": "^2.20.0", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.16.0", "react-native-html-to-pdf": "^0.12.0", "react-native-image-picker": "^7.1.2", "react-native-modal-datetime-picker": "^17.1.0", "react-native-modern-datepicker": "^1.0.0-beta.91", "react-native-orientation-locker": "^1.7.0", "react-native-paper": "^5.12.3", "react-native-pell-rich-editor": "^1.9.0", "react-native-permissions": "^4.1.5", "react-native-qrcode-svg": "^6.3.0", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^4.9.0", "react-native-scoped-storage": "^1.9.5", "react-native-screens": "^3.30.1", "react-native-sectioned-multi-select": "^0.10.0", "react-native-select-dropdown": "^4.0.0", "react-native-share": "^10.2.1", "react-native-svg": "^15.1.0", "react-native-vector-icons": "^10.0.3", "react-native-webview": "^13.8.4", "yup": "^1.4.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.73.21", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.73.5", "@react-native/typescript-config": "0.73.1", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}